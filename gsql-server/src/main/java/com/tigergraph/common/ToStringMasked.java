package com.tigergraph.common;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
public @interface ToStringMasked {
  /**
   * If target is empty, masking will be applied for all classes.
   * Else, masking will be applied only to the given classes.
   *
   * For example, {@link com.tigergraph.schema.operation.DropOperation#dropList}
   * can have sensitive info like secret
   * in case of {@link com.tigergraph.schema.operation.DropSecretOperation},
   * so we can use {@code @ToStringMasked(target={DropSecretOperation.class})}
   * to mask {@link com.tigergraph.schema.operation.DropSecretOperation#dropList},
   * but not necessarily the other {@code dropList}s in other inherited classes.
   *
   * @return An array of {@code Class}es to apply masking.
   */
  Class[] target() default {};
}
