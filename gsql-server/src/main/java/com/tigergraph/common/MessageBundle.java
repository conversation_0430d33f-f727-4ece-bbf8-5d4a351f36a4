/**
 * ****************************************************************************
 * Copyright (c) 2020, TigerGraph Inc.
 * All rights reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * ****************************************************************************
 */

package com.tigergraph.common;

import com.tigergraph.schema.Util;
import java.util.IllegalFormatException;
import java.util.Locale;
import java.util.ResourceBundle;


/**
 * This is to read messages used in GSQL server
 * from a message bundle based on the unified properties file.<p>
 * All static helper functions related the message bundle belong to here.
 * TO-DO: Think better way of categorizing keys so that we can put all messages in properties file.
 */
public final class MessageBundle {
  /**
   * The base name for the message bundle properties file.<p>
   * We can easily extend other bundles for different locales,
   * e.g. <BUNDLE_BASE_NAME>_zh_CN.properties<p>
   * In order to support i18n, we can think of storing locale info in session,
   * so that we can support client, including GST, in other locales different from server's.
   * This requires changes in GST side though, like passing Accept-Language in the header.
   */
  private static final String BUNDLE_BASE_NAME = "MessageBundle";

  /** The bundle holds all messages. */
  private static final ResourceBundle bundle;

  static {
    // for now, we simply get bundle from the default properties file regardless of locale
    Locale localeDefault = Locale.getDefault();
    bundle = ResourceBundle.getBundle(BUNDLE_BASE_NAME, localeDefault);
    if (localeDefault != bundle.getLocale()) {
      // This is to detect whether bundle's locale is different from JVM.
      // bundle.getLocale() will be empty, however, since it's always using base bundle for now.
      // See https://bugs.openjdk.java.net/browse/JDK-4455055?focusedCommentId=12248932
      // Util.LogDebug("Message locale: %s", bundle.getLocale());
    }
  }

  /**
   * Get a message for the given key from message bundle.
   *
   * @param key The key for the desired message
   * @return The message for the given key
   */
  public static String get(String key) {
    String msg = null;
    try {
      msg = bundle.getString(key);
    } catch (Exception e) {
      // if there's any exceptions, this will return null
      Util.LogExceptions(e);
    }
    return msg;
  }

  /**
   * Get a message for the given key from message bundle.
   *
   * @param key The key for the desired message
   * @param args Arguments referenced by the format specifiers in the formatted message
   * @return The formatted message for the given key
   */
  public static String get(String key, Object... args) {
    String msgFormatted = get(key);
    try {
      msgFormatted = String.format(msgFormatted, args);
    } catch (IllegalFormatException e) {
      // if format doesn't match, this will print out the message with specifiers
      Util.LogExceptions(e);
    }
    return msgFormatted;
  }
}
