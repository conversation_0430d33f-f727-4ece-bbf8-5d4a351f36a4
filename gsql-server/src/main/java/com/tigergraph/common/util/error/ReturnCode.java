package com.tigergraph.common.util.error;

/**
 * The ReturnCode constants between client and server.
 */
public class ReturnCode {
  // normal exit
  public static final int NO_ERROR = 0;

  // timeout
  public static final int TIMEOUT = 101;
  // aborted by user
  public static final int ABORTED = 121;

  // server error
  public static final int SYNTAX_ERROR = 211;
  public static final int RUNTIME_ERROR = 212;
  public static final int NO_GRAPH_ERROR = 213;
  //TODO:GLE-4443 UDF_MISMATCH_ERROR is a temporary client code.
  //     Remove when GraphStudio switches to `import graph` API.
  // [Temporary] udf mismatch error code
  public static final int UDF_MISMATCH_ERROR = 214;
  // [Temporary] violate udf policy error code
  public static final int UDF_PUT_ERROR = 215;
  public static final int FATAL_ERROR = 216;
  // error when query installation is partially successful
  public static final int PARTIAL_INSTALL_ERROR = 217;

  // file policy error
  public static final int FILE_INPUT_POLICY_ERROR = 220;

  // unknown
  public static final int UNKNOWN_ERROR = 255;
}
