package com.tigergraph.common;

import java.lang.reflect.Field;
import java.util.Arrays;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;

public class GsqlReflectionToStringBuilder extends ReflectionToStringBuilder {
  public GsqlReflectionToStringBuilder(Object object) {
    super(object);
  }

  @Override
  protected Object getValue(final Field field) throws IllegalAccessException {
    Object obj = field.get(this.getObject());
    if (field.isAnnotationPresent(ToStringMasked.class)) {
      ToStringMasked a = field.getAnnotation(ToStringMasked.class);
      // by default the target is empty which means the field should be masked
      boolean needMask = true;
      // check if any target class is given
      if (a.target().length > 0) {
        // check if the given class is one of the targets
        needMask = Arrays.asList(a.target()).contains(this.getObject().getClass());
      }
      // mask if applicable
      if (needMask) {
        return obj == null ? "<null>" : String.valueOf(obj).isEmpty() ? "" : "<masked>";
      }
    }
    return obj;
  }
}
