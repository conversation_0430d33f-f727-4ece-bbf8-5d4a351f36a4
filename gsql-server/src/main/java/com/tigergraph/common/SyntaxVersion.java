/**
 * ***************************************************************************
 * Copyright (c)  2014-2019, TigerGraph Inc.
 * All rights reserved
 * Unauthorized copying of this file, via any medium is
 * strictly prohibited
 * Proprietary and confidential
 * Author: <PERSON><PERSON> Wu
 * ****************************************************************************
 */

package com.tigergraph.common;

/**
 * Options for syntax version setting at global or query level.
 * <p>
 * Currently, the default sytnax version is v1.
 */
public enum SyntaxVersion {
  /**
   * syntax up until v2.3, where <code>-(E)-</code> is equivalent
   * to <code>-(E)-></code>
   */
  V1("v1"),
  /** syntax after v2.4, i.e. pattern match syntax */
  V2("v2"),
  /**
   * syntax after v2.4 that shows transformed query in GSQL log,
   * internal use only
   */
  _V2("_v2"),
  /** syntax after 3.7, allows cypher syntax in FROM clause patterns */
  V3("v3"),
  /**
   * syntax after 3.7 that shows transformed query in GSQL debug log,
   * internal use only
   */
  _V3("_v3"),
  /**
   * Allows for expanded cypher syntax beyond v3. We can think
   * about combining cypher and v3 syntax in the future.
   */
  CYPHER("cypher"),
  /** invalid syntax version used for error reporting */
  INVALID("invalid"),
  /** unknown syntax version used for auto detection */
  UNKNOWN("unknown");

  private String version;

  /** Default syntax version is v2 */
  private static SyntaxVersion defaultVersion = V2;

  private SyntaxVersion(String version) {
    this.version = version;
  }

  /**
   * Returns the <code>SyntaxVersion</code> object corresponding to the
   * given version string.
   * <p>
   * Accepted inputs are "v1", "v2", "v3", "V1", "V2", "V3", "_v2", "_V2", "_v3", "_V3" to allow
   * for case-insensitive setting.
   *
   * @param version the version string provided by user
   * @return corresponding <code>SyntaxVersion</code> object for valid input
   *         or <code>SyntaxVersion.UNKNOWN<code> for invalid input
   */
  public static SyntaxVersion getInstance(String version) {
    for (SyntaxVersion obj: SyntaxVersion.values()) {
      // check for each valid input and return the corresponding object if match
      if (obj.version.equalsIgnoreCase(version)) {
        return obj;
      }
    }
    // return invalid syntax version for invalid input
    return SyntaxVersion.INVALID;
  }

  /**
   * Resets default sytnax version to the one corresponding to the provided
   * version string.
   *
   * @param version the version string provided by user
   * @return true if user input is valid and false otherwise
   */
  public static boolean setDefaultSyntaxVersion(String version) {
    SyntaxVersion incomingVersion = getInstance(version);
    // for invalid input, defaultVersion remains unchanged
    if (incomingVersion == INVALID || incomingVersion == UNKNOWN) {
      return false;
    }
    defaultVersion = incomingVersion;
    return true;
  }

  /** Returns the default syntax version in current session. */
  public static SyntaxVersion defaultSyntax() {
    return defaultVersion;
  }

  /** Returns the version string of the current default syntax version */
  public static String defaultVersion() {
    return defaultSyntax().toString();
  }

  /** Returns the syntax version string */
  @Override
  public String toString() {
    return version;
  }
}
