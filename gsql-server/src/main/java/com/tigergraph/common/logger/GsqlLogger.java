/**
 * ****************************************************************************
 * Copyright (c) 2023, TigerGraph Inc.
 * All rights reserved
 * Unauthorized copying of this file, via any medium is
 * strictly prohibited
 * Proprietary and confidential
 * ****************************************************************************
 */

package com.tigergraph.common.logger;

import com.tigergraph.schema.config.Config;
import com.tigergraph.utility.IOUtil;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.attribute.BasicFileAttributes;
import java.nio.file.attribute.PosixFilePermissions;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import lombok.Getter;
import lombok.NonNull;
import tigergraph.tutopia.common.pb.Util;



/**
 * Logger for GSQL server.<p>
 * We maintain log files based on {@code Level}:
 * {@code log.ERROR}, {@code log.INFO}, and {@code log.DEBUG}.
 * The logger will split each log in the following way:<p>
 * - {@code log.ERROR} contains only {@code ERROR} logs.<p>
 * - {@code log.INFO} contains {@code ERROR} and {@code INFO} logs.<p>
 * - {@code log.DEBUG} contains {@code ERROR}, {@code INFO}, and {@code DEBUG} logs.<p>
 * Rotation will be performed based on the configuration file, {@code tg.cfg}.
 * Check out GSQL.BasicConfig.LogConfig for more details about the configurations.
 */
public final class GsqlLogger {
  /**
   * Log levels.<p>
   * The order is important because the logger will print to all files lower than the given level.
   * For example, if the given level for a log is {@code Level.INFO},
   * the log will be printed in log.INFO and log.DEBUG, but not in log.ERROR.<p>
   * For this reason, it's order by severity DESC,
   * i.e. Level of higher severity comes first in the list,
   * and the highest rank takes the highest severity.
   */
  public enum Level {
    /** Systematic error */
    ERROR,
    /** Information for users; Error by users */
    INFO,
    /** Information for developers */
    DEBUG;

    /**
     * Get the symbol of this {@code Level} that is the first character.
     */
    public char getSymbol() {
      return this.name().charAt(0);
    }
  }

  /** Locks for the log file for each {@link Level}. */
  private static final Map<Level, Lock> locks = Map.of(
      Level.ERROR, new ReentrantLock(),
      Level.INFO, new ReentrantLock(),
      Level.DEBUG, new ReentrantLock());

  /** The paths to the log file for each {@link Level}. */
  private static final Map<Level, Path> files = new ConcurrentHashMap<>();

  @Getter
  private static Level logLevel = Level.INFO;

  /**
   * Log {@code text} with the following format:
   * <LOG_LEVEL_SYMBOL>@<TIMESTAMP> <USER>|<IP>|<SESSION> (<FILE_NAME>:<LINE_NUM>) <TEXT>
   * <TIMESTAMP> is in yyyymmdd hh:mm:ss.sss.
   *
   * @param level {@code Level} of this log
   * @param text Message to log
   * @param session Info of <USER>|<IP>|<SESSION>
   * @param stack The number of file in call stack (0 by default)
   * @throws IOException if there is an issue with {@code filePath}
   */
  private static void log(Level level, String text, String session, int stack) throws IOException {
    if (level.ordinal() > logLevel.ordinal()) {
      return;
    }

    // try to get the file name and line number of the caller
    String fileInfo = "";
    try {
      int skip = 2 + stack; // skip first two in stack trace
      for (StackTraceElement s : Thread.currentThread().getStackTrace()) {
        if (skip-- > 0) continue;
        fileInfo = "(" + s.getFileName() + ":" + s.getLineNumber() + ")";
        break;
      }
    } catch (SecurityException e) {
      // ignore SecurityException while logging
    }

    String formattedLog = String.format("%s@%s %s %s %s",
        level.getSymbol(),
        new SimpleDateFormat("yyyyMMdd HH:mm:ss.SSS").format(new Date()),
        session == null ? "" : session,
        fileInfo,
        text == null ? "" : text.trim());
    println(level, formattedLog);
  }

  /**
   * Log stack trace when there is an exception.
   *
   * @param e {@code Throwable}
   * @param session Info of <USER>|<IP>|<SESSION>
   * @param stack The number of file in call stack (0 by default)
   * @throws IOException if there is an issue with {@code filePath}
   */
  public static void exception(Throwable e, String session, int stack) throws IOException {
    log(Level.ERROR, e.toString(), session, stack + 1);
    printStackTrace(e);
  }

  public static void error(String text, String session, int stack) throws IOException {
    log(Level.ERROR, text, session, stack + 1);
  }

  public static void error(String text, String session) throws IOException {
    error(text, session, 1);
  }

  public static void info(String text, String session, int stack) throws IOException {
    log(Level.INFO, text, session, stack + 1);
  }

  public static void info(String text, String session) throws IOException {
    info(text, session, 1);
  }

  public static void debug(String text, String session, int stack) throws IOException {
    log(Level.DEBUG, text, session, stack + 1);
  }

  public static void debug(String text, String session) throws IOException {
    debug(text, session, 1);
  }

  /*
   * Print log to all files with equal and lower rank than the given level.
   *
   * @param levelHi Highest rank of the level for the file to log
   * @param log A formatted log
   */
  public static void println(Level levelHi, String log) {
    Level[] levels = Level.values();
    // Since Level is order by severity DESC, Level of higher severity has lower ordinal.
    // Therefore, we increment the index starting from the ordinal of levelHi
    // to print log to all files of equal and lower severity.
    for (int i = levelHi.ordinal(); i < levels.length; i++) {
      printlnImpl(levels[i], log);
    }
  }

  /*
   * Print log to all files by passing {@code Level} of the highest rank
   * to {@code println(Level, String)}.
   * This method is designed to use with unformatted log,
   * such as GSQL info at the beginning,
   * and redirected stdout/stderr from {@code ProcessBuilder}s.
   *
   * @param log A formatted log
   */
  public static void println(String log) {
    // pass the highest rank so that it can print log to all files
    println(Level.values()[0], log);
  }

  /*
   * Helper function for {@code println(Level, String)}
   * to create a {@code PrintWriter} specific to the file of {@code level}
   * and {@PrintWriter.println}.
   *
   * @param level Specific level corresponds to the file to log
   * @param log A formatted log
   */
  private static void printlnImpl(Level level, String log) {
    try (PrintWriter pw = createPrintWriter(level)) {
      if (pw != null) {
        pw.println(log);
      }
    }
  }

  /**
   * Wrapper for {@code Throwable.printStackTrace(PrintWriter)}
   * to print stack trace for every log level.
   *
   * @param e An exception to log
   */
  private static void printStackTrace(Throwable e) {
    for (Level level : Level.values()) {
      try (PrintWriter pw = createPrintWriter(level)) {
        if (pw != null) {
          e.printStackTrace(pw);
        }
      }
    }
  }

  /**
   * Create {@code PrintWriter} to log based on {@code Level}.
   *
   * @param level Specific level corresponds to the file to log
   * @return {@code PrintWriter} specific to {@code level}
   */
  private static PrintWriter createPrintWriter(Level level) {
    if (level.ordinal() > logLevel.ordinal()) {
      return null;
    }
    PrintWriter pw = null;
    // rotate the current log file if necessary
    if (shouldRotate(files.get(level), Config.gsql().getBasicConfig().getLogConfig())) {
      rotate(level);
    }
    try {
      // prepare PrintWriter to write into the current log file
      pw = new PrintWriter(
          new BufferedWriter(
              new FileWriter(files.get(level).toFile(), true)),
          true);
    } catch (IOException e) {
      // System.out will automatically be redirected into GSQL#?.out
      e.printStackTrace(System.out);
    }
    return pw;
  }

  /**
   * Get symbolic link to the log file of the given {@code suffix},
   * for regular log, it should be {@link GsqlLogger.Level},
   * for audit log, it should be just AUDIT.
   *
   * @param suffix of the log file.
   * @return Symbolic link, e.g. /path/to/tigergraph/log/gsql/log.ERROR
   */
  static Path getSymlink(Path logDir, String suffix) {
    return logDir.resolve(String.format("log.%s", suffix));
  }

  /**
   * wrapper function to get path to current log error.
   * @return
   */
  public static String getPathToLogError() {
    return getPathToLog(Level.ERROR);
  }

  /**
   * @return The path to the current log.
   */
  public static String getPathToLog(Level level) {
    // rotate when the ERROR log file is not yet created
    if (shouldRotate(files.get(level), Config.gsql().getBasicConfig().getLogConfig())) {
      rotate(level);
    }
    return files.get(level).toAbsolutePath().toString();
  }

  /**
   * Check whether the log file should be rotated
   * based on the settings in tg.cfg when the file is:<p>
   * - older than LogFileMaxDurationDay<p>
   * - larger than LogFileMaxSizeMB<p>
   *
   * @param target Path to the log file
   * @param logConfig log related configs
   */
  static boolean shouldRotate(Path target, Util.LogConfig logConfig) {
    // if target does not exist, return true to create a new log file via rotate()
    if (target == null || !Files.exists(target)) {
      return true;
    }
    try {
      BasicFileAttributes bfa = Files.readAttributes(target, BasicFileAttributes.class);
      // check whether the log file is larger than LogFileMaxSizeMB
      int maxSizeMB = logConfig.getLogFileMaxSizeMB();
      long fileSize = bfa.size();
      if ((fileSize / (1024L * 1024L)) > maxSizeMB) {
        return true;
      }
    } catch (IOException e) {
      // System.out will automatically be redirected into GSQL#?.out
      e.printStackTrace(System.out);
    }
    return false;
  }

  /**
   * Create a new log file and update the corresponding symbolic link of the given {@code level}.
   * Plus, remove the oldest file if there are more than {@code LogRotationFileNumber} of files.
   *
   * @param level The {@link Level} of the log file to rotate.
   */
  private static void rotate(Level level) {
    // acquire write lock before possible rotation
    locks.get(level).lock();
    try {
      removeExpiredLog(Config.getLogDir(),
          level.name(), Config.gsql().getBasicConfig().getLogConfig());
      Path target = createLogFile(Config.getLogDir(), level.name());
      // update the log file path for this level
      files.put(level, target);
      sysout("Now log to %s", target);
      // update symlink to the new file
      updateSymlink(target, level.name());
    } catch (IOException e) {
      // System.out will automatically be redirected into GSQL#?.out
      e.printStackTrace(System.out);
    } finally {
      locks.get(level).unlock();
    }
  }

  /**
   * Create a new log file according to the {@code logDir} and {@code LogPrefix}
   *
   * @param logDir The folder of the log file to create in.
   * @param logPrefix The prefix of the log file name.
   * @return Path to the new created log file.
   */
  static Path createLogFile(Path logDir, String logPrefix) {
    // create new canonical file under LOG_DIR
    String targetName = String.format("%s.%s",
            logPrefix,
            new SimpleDateFormat("yyyyMMdd-HHmmss.SSS").format(new Date()));
    Path target = logDir.resolve(targetName);
    try {
      Files.createFile(target, PosixFilePermissions.asFileAttribute(IOUtil.FILE_PERMISSION_SET));
    } catch (IOException e) {
      // System.out will automatically be redirected into GSQL#?.out
      e.printStackTrace();
    }
    return target;
  }

  /**
   * update the symbolic link for the given log file
   *
   * @param target Absolute path to the log file
   * @param logSuffix The suffix of the file name of the linked log.
   */
  static void updateSymlink(Path target, String logSuffix) {
    try {
      Path symLink = getSymlink(target.getParent(), logSuffix);
      // remove symlink if exists
      Files.deleteIfExists(symLink);
      // re-create symlink to new target
      Files.createSymbolicLink(symLink, target.getFileName());
    } catch (IOException e) {
      // System.out will automatically be redirected into GSQL#?.out
      e.printStackTrace();
    }
  }

  /**
   * Remove the oldest files if its lifetime is longer than {@code maxDurationDay}
   * or there are more than {@code LogRotationFileNumber} of files.
   *
   * @param logDir Absolute path to the log file.
   * @param logPrefix The prefix of the file name of the potential expired logs.
   * @param logConfig log related configs
   */
  static void removeExpiredLog(Path logDir, String logPrefix, Util.LogConfig logConfig)
          throws IOException {
    // list all files with specific prefix to delete old files
    File[] files = logDir.toFile().listFiles((dir, name) -> name.startsWith(logPrefix));
    if (files != null) {
      // sort by name DESC
      Arrays.sort(files, Comparator.comparing(File::getName).reversed());

      int i = files.length - 1;
      // remove log files if older than LogFileMaxDurationDay
      int maxDurationDay = logConfig.getLogFileMaxDurationDay();
      // remove old files to keep the amount of log files less than rotationFileNum
      int rotationFileNum = logConfig.getLogRotationFileNumber() < 1
              ? 1 : logConfig.getLogRotationFileNumber();

      BasicFileAttributes bfa = i >= 0
              ? Files.readAttributes(files[i].toPath(), BasicFileAttributes.class) : null;
      long fileDurationMillis = getFileDurationMillis(bfa);
      while ((TimeUnit.MILLISECONDS.toDays(fileDurationMillis) > maxDurationDay)
              || (i >= rotationFileNum - 1)) {
        if (files[i].delete()) {
          sysout("Removed an old file %s", files[i]);
        } else {
          sysout("Failed to remove an old file %s", files[i]);
        }
        i--;
        if (i < 0) return;
        bfa = Files.readAttributes(files[i].toPath(), BasicFileAttributes.class);
        fileDurationMillis = getFileDurationMillis(bfa);
      }
    }
  }

  private static long getFileDurationMillis(BasicFileAttributes bfa) {
    if (bfa == null) return 0;
    long fileMillis = bfa.creationTime().toMillis();
    // for Java 17, the creationTime may be '1970-01-01T00:00:00Z',
    // use lastModifiedTime instead
    fileMillis = fileMillis == 0 ? bfa.lastModifiedTime().toMillis() : fileMillis;
    return System.currentTimeMillis() - fileMillis;
  }

  /**
   * Rotate all log files, typically when the server starts up.
   */
  public static void rotateAll() {
    Path logDir = Config.getLogDir();
    if (logDir != null) {
      for (Level level : Level.values()) {
        // skip if the given level has the higher ordinal than the current log level
        if (level.ordinal() > logLevel.ordinal()) {
          continue;
        }
        rotate(level);
      }
    } else {
      // System.out will automatically be redirected into GSQL#?.out
      sysout("Cannot rotate logs: log directory is null");
    }
  }

  /**
   * Log to stdout, which will automatically be redirected to GSQL#?.out.
   * TO-DO: use ConsoleAppender in log4j
   *
   * @param format A format string
   * @param args Arguments referenced by the format specifiers in the format string (nullable)
   */
  public static void sysout(String format, Object... args) {
    // skip first two in stack trace
    StackTraceElement ste = Thread.currentThread().getStackTrace()[2];
    String timestamp = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS").format(new Date());
    String lineInfo = String.format("(%s:%d)", ste.getFileName(), ste.getLineNumber());
    String log = (args == null) ? format : String.format(format, args);
    // e.g. 2020-06-03 20:48:15.631 (SessionManager.java:108) Server ID: 1_1591242495611
    System.out.println((String.format("%s %s %s", timestamp, lineInfo, log)));
  }

  /**
   * Set log level based on the configuration.
   * @param level the string level from config
   */
  public static synchronized void setLogLevel(@NonNull String level) {
    try {
      if (com.tigergraph.schema.Util.runAsCLIUtil.get()) {
        logLevel = Level.DEBUG;
      } else {
        logLevel = Level.valueOf(level.toUpperCase());
      }
    } catch (Exception e) {
      sysout("Can't read log level from config %s due to %s", level, e.getMessage());
      // fallback to default
      logLevel = Level.INFO;
    }
    // print to stdout
    sysout("Set Log Level to %s", logLevel.name());
  }

  public static boolean isDebugLevel() {
    return getLogLevel() == Level.DEBUG;
  }
}
