/**
 * ****************************************************************************
 * Copyright (c) 2023, TigerGraph Inc.
 * All rights reserved
 * Unauthorized copying of this file, via any medium is
 * strictly prohibited
 * Proprietary and confidential
 * ****************************************************************************
 */

package com.tigergraph.common.logger;

import com.tigergraph.schema.Util;
import lombok.Getter;
import lombok.NonNull;
import org.json.JSONObject;
import java.util.List;

/**
 * Immutable class to store audit log information
 */
@Getter
public class AuditLogEntry {
  public enum ActionType {
    DDL,  // create or drop graph/vertex/edge/loading job...
    DML,  // clear graph store, run loading job...
    DQL,  // run query, interpret query...
    DCL,  // create user, change password, grant privilege...
    TCL,  // pause/resume/abort loading job
    OTHER // show catalog, show leader, translate SQL...
  }

  private final String action;
  private final boolean status;
  private final String message;
  private final JSONObject extraFields;
  private final String graphName;
  // Standard database action types such as DDL, DML, DQL, DCL and/or TCL
  private final List<ActionType> actionType;
  // Impacted object type by the action, e.g. vertex, edge, graph, etc.
  private final String objectType;

  public AuditLogEntry(@NonNull String action, boolean status, String msg,
                       @NonNull JSONObject extraFields, String graphName,
                       List<ActionType> actionType, String objectType) {
    this.action = action;
    this.status = status;
    this.message = msg;
    this.extraFields = extraFields;
    this.graphName = graphName;
    this.actionType = actionType;
    this.objectType = objectType;
  }

  /**
   * Print audit log
   */
  public void LogAudit() {
    // do not print log entry without action
    if (action.isEmpty()) {
      Util.LogInfo("Action name is null or empty when log audit for API %s, "
              + "probably because not adding actionName for new added QBs "
              + "or not creating AuditLogEntry in new added handlers.", Util.endpoint.get());
      return;
    }
    String msg = message;
    if ((message == null) || (message.isEmpty())) {
      msg = status ? action + " succeeded" : action + " failed";
    }
    GsqlAuditLogger.log(Util.sessionLogInfo.get(), Util.userAgent.get(), Util.endpoint.get(),
                        action, status, msg, extraFields, graphName, actionType, objectType);
  }

  /**
   * @param status latest status
   * @return a new {@code AuditLogEntry} object with updated status
   */
  public AuditLogEntry setStatus(Boolean status) {
    return new AuditLogEntry(action, status, message, extraFields, graphName, actionType,
                             objectType);
  }

  /**
   * set status and message for the previously created log entry
   *
   * @param status whether the action succeed or not
   * @param msg detailed message
   * @return a new {@code AuditLogEntry} object with updated status and message
   */
  public AuditLogEntry setLogDetail(boolean status, @NonNull String msg) {
    return new AuditLogEntry(action, status, msg, extraFields, graphName, actionType, objectType);
  }

  /**
   * Add extra customized fields to the current audit log entry
   *
   * @param extraFields to attach to the current audit log entry
   * @return a new {@code AuditLogEntry} object with extraFields attached
   */
  public AuditLogEntry setExtraFields(JSONObject extraFields) {
    return new AuditLogEntry(action, status, message, extraFields, graphName, actionType,
                             objectType);
  }

  /**
   * @param graph current graph
   * @return a new {@code AuditLogEntry} object with updated status
   */
  public AuditLogEntry setGraph(String graph) {
    return new AuditLogEntry(action, status, message, extraFields, graph, actionType,
                             objectType);
  }
}