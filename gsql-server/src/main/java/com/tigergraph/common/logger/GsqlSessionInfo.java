/**
 * ****************************************************************************
 * Copyright (c) 2023, TigerGraph Inc.
 * All rights reserved
 * Unauthorized copying of this file, via any medium is
 * strictly prohibited
 * Proprietary and confidential
 * ****************************************************************************
 */
package com.tigergraph.common.logger;

import java.util.Objects;
import java.util.StringJoiner;
import lombok.Getter;
import lombok.NonNull;


/**
 * Immutable class to store session information
 */
@Getter
public class GsqlSessionInfo {

  private final String username;
  private final String clientHost;
  private final String sessionId;

  public GsqlSessionInfo(
          @NonNull String username,
          @NonNull String clientHost,
          @NonNull String sessionId) {
    this.username = username;
    this.clientHost = clientHost;
    this.sessionId = sessionId;
  }

  /**
   * serialize the object to string
   *
   * @return a string that contains session info in the format <username>|<client IP>|<session ID>
   */
  @Override
  public String toString() {
    String res = new StringJoiner("|").add(username).add(clientHost).add(sessionId).toString();
    return (Objects.equals(res, "||")) ? "" : res;
  }

  /**
   * Update whole session info
   *
   * @param username new username
   * @param clientHost new clientHost
   * @param sessionId new sessionId
   * @return a new {@code GsqlSessionInfo} object with updated properties.
   */
  public GsqlSessionInfo Update(
          @NonNull String username,
          @NonNull String clientHost,
          @NonNull String sessionId) {
    return new GsqlSessionInfo(username, clientHost, sessionId);
  }

  /**
   * Update session Id
   *
   * @param newSessionId new sessionId
   * @return a new {@code GsqlSessionInfo} object with updated sessionId.
   */
  public GsqlSessionInfo UpdateSessionId(String newSessionId) {
    return new GsqlSessionInfo(username, clientHost, newSessionId);
  }

  /**
   * Update session Id
   *
   * @param newUsername new Username
   * @return a new {@code GsqlSessionInfo} object with updated newUsername.
   */
  public GsqlSessionInfo UpdateUsername(String newUsername) {
    return new GsqlSessionInfo(newUsername, clientHost, sessionId);
  }
}
