/**
 * ****************************************************************************
 * Copyright (c) 2023, TigerGraph Inc.
 * All rights reserved
 * Unauthorized copying of this file, via any medium is
 * strictly prohibited
 * Proprietary and confidential
 * ****************************************************************************
 */

package com.tigergraph.common.logger;

import com.tigergraph.schema.*;
import com.tigergraph.schema.config.Config;
import com.tigergraph.schema.controller.LoginController;
import com.tigergraph.schema.security.rbac.User;
import com.tigergraph.utility.AuthenticateUtil;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.OffsetDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

import org.json.JSONObject;

/**
 * Audit Logger for GSQL server.<p>
 * Unlike GSQL log, audit logs record a historical record of activity event,
 * the time at which it occurred, the responsible user or service, and the impacted entity.<p>
 * Audit log will be printed in JSON format.<p>
 * Rotation will be performed based on the configuration file, {@code tg.cfg}.
 * Check out System.Audit.LogConfig for more details about the configurations.
 */
public class GsqlAuditLogger {
  private static final String TIMESTAMP = "timestamp";
  private static final String DBNAME = "databaseName";
  private static final String SERVER_HOST_IP = "serverHostIP";
  private static final String VERSION = "version";
  private static final String ENDPOINT = "endpoint";
  private static final String CLIENT_HOST = "clientHost";
  private static final String USER_AGENT = "userAgent";
  private static final String USERNAME = "userName";
  private static final String AUTH_TYPE = "authType";
  private static final String MESSAGE = "message";
  private static final String ACTION_NAME = "actionName";
  private static final String STATUS = "status";
  private static final String FAILED_ATTEMPTS = "failedAttempts";
  private static final String CLIENT_OS_USER = "clientOSUsername";
  private static final String GRAPH_NAME = "graphName";
  private static final String ACTION_TYPE = "actionType";
  private static final String OBJECT_TYPE = "objectType";
  private static final String CURRENT_VERSION = "2.0";

  /** Lock for the log file */
  private static final Lock lock = new ReentrantLock();

  /** The path to the log file */
  private static Path file = null;

  public static boolean auditEnabled = false;

  public static boolean printDiscreteJson = false;

  /** public ip address of this gsql server */
  private static String publicIP = null;

  /**
   * Log the activity event in JSON format
   *
   * @param session Contains username and client host
   * @param endpoint The endpoint which triggered the action
   * @param action Action performed
   * @param status Indicates whether the action was succeed or failed
   * @param message Message to log
   */
  public static void log(GsqlSessionInfo session, String userAgent, String endpoint, String action,
                         boolean status, String message, JSONObject extraFields, String graphName,
                         List<AuditLogEntry.ActionType> actionType, String objectType) {
    if (!auditEnabled) {
      return;
    }
    String username = session.getUsername();
    String clientOsUsername = Util.clientOSUser.get();
    JSONObject auditJSON = new JSONObject();

    auditJSON.put(TIMESTAMP, getCurrentISOTime());
    auditJSON.put(USERNAME, username);
    auditJSON.put(AUTH_TYPE, AuthenticateUtil.getAuthenticationMethodString());
    auditJSON.put(CLIENT_HOST, session.getClientHost());
    if (session.getSessionId().isEmpty()) {
      auditJSON.put(USER_AGENT, userAgent);
    } else {
      if (SessionManager.getCurrentSession().isFromGraphStudio()) {
        auditJSON.put(USER_AGENT, "GraphStudio");
      } else if (SessionManager.getCurrentSession().isFromGsqlClient()) {
        auditJSON.put(USER_AGENT, "GSQL Shell");
      } else {
        auditJSON.put(USER_AGENT, userAgent);
      }
    }
    if (clientOsUsername != null && !clientOsUsername.isEmpty()) {
      auditJSON.put(CLIENT_OS_USER, Util.clientOSUser.get());
    }
    auditJSON.put(ENDPOINT, endpoint);
    auditJSON.put(ACTION_NAME, action);
    auditJSON.put(STATUS, status ? "SUCCESS" : "FAILURE");
    auditJSON.put(MESSAGE, message);
    if (action.equals(LoginController.ACTION_NAME)) {
      GlobalCatalog globalCatalog = CatalogManager.getGlobalCatalog();
      User currentUser = globalCatalog == null ? null : globalCatalog.GetUser(username);
      int failedAttempts = currentUser == null ? 1 : currentUser.getFailedAttempts().get();
      auditJSON.put(FAILED_ATTEMPTS, failedAttempts);
    }
    extraFields.toMap().forEach(auditJSON::put);
    auditJSON.put(GRAPH_NAME, graphName != null ? graphName : "global");
    String actionTypeVal = actionType.stream()
                                     .map(Object::toString)
                                     .collect(Collectors.joining(", "));
    auditJSON.put(ACTION_TYPE, actionTypeVal);
    auditJSON.put(OBJECT_TYPE, objectType);

    if (GsqlLogger.shouldRotate(file, Config.sys().getAudit().getLogConfig())) {
      rotate();
      printLogHeader();
    }
    if (printDiscreteJson) {
      printlnIndependently(auditJSON.toString());
    } else {
      printlnInArray(auditJSON.toString());
    }
  }

  /**
   * Print log header at the top of new created log file
   */
  public static void printLogHeader() {
    try {
      JSONObject auditJSON = new JSONObject();
      auditJSON.put(VERSION, CURRENT_VERSION);
      auditJSON.put(TIMESTAMP, getCurrentISOTime());
      String databaseName = Config.sys().getAudit().getDatabaseName();
      if (!databaseName.isEmpty()) {
        auditJSON.put(DBNAME, Config.sys().getAudit().getDatabaseName());
      }
      if (publicIP == null) {
        publicIP = Util.getPublicIP();
      }
      auditJSON.put(SERVER_HOST_IP, publicIP);

      if (printDiscreteJson) {
        printlnIndependently(auditJSON.toString());
      } else {
        printlnInArray(auditJSON.toString());
      }
    } catch (Exception e) {
      GsqlLogger.sysout("Encountered an error when printing audit log header!");
    }
  }

  /**
   * get current time in ISO 8601 format
   *
   * @return current time in ISO 8601 format, e.g. "2024-01-08T16:57:31.702+08:00"
   */
  private static String getCurrentISOTime() {
    OffsetDateTime currentTime = OffsetDateTime.now().truncatedTo(ChronoUnit.MILLIS);
    DateTimeFormatter formatter = DateTimeFormatter.ISO_DATE_TIME;
    return formatter.format(currentTime);
  }

  /**
   * Print JSONArray format log to file
   *
   * @param auditLog audit log entry to print
   */
  private static void printlnInArray(String auditLog) {
    lock.lock();
    try {
      RandomAccessFile file1 = new RandomAccessFile(file.toFile(), "rw");
      if (file1.length() > 2) {
        file1.seek(file1.length() - 2);
        file1.write((",\n" + auditLog + "\n]").getBytes());
      } else {
        file1.write(("[\n" + auditLog + "\n]").getBytes());
      }
    } catch (IOException e) {
      // System.out will automatically be redirected into GSQL#?.out
      e.printStackTrace(System.out);
    } finally {
      lock.unlock();
    }
  }

  /**
   * Print JSONObject format log to file
   *
   * @param auditLog audit log entry to print
   */
  private static void printlnIndependently(String auditLog) {
    lock.lock();
    try {
      // prepare PrintWriter to write into the current log file
      PrintWriter pw = new PrintWriter(
          new BufferedWriter(
              new FileWriter(file.toFile(), true)),
          true);
      pw.println(auditLog);
    } catch (IOException e) {
      // System.out will automatically be redirected into GSQL#?.out
      e.printStackTrace(System.out);
    } finally {
      lock.unlock();
    }
  }

  /**
   * Create a new log file and update the corresponding symbolic link.
   * Plus, remove the oldest file if there are more than {@code LogRotationFileNumber} of files.
   */
  public static void rotate() {
    // acquire write lock before possible rotation
    lock.lock();
    try {
      Path target = Config.getAuditLogDir();
      GsqlLogger.removeExpiredLog(target, "AUDIT-GSQL",
          Config.sys().getAudit().getLogConfig());
      if (!Files.exists(target)) {
        if (target.toFile().mkdirs()) {
          GsqlLogger.sysout("Audit log dir %s does not exist, so we created it for you.", target);
        } else {
          GsqlLogger.sysout("Cannot create Audit log dir %s. Log will be in %s",
                  target, Config.getLogDir());
          target = Config.getLogDir();
        }
      }
      // create new log file for audit log
      target = GsqlLogger.createLogFile(target, "AUDIT-GSQL");
      file = target;
      GsqlLogger.sysout("Now log to %s", target);
      // update symlink to the new file
      GsqlLogger.updateSymlink(target, "AUDIT-GSQL");
    } catch (IOException e) {
      // System.out will automatically be redirected into GSQL#?.out
      e.printStackTrace(System.out);
    } finally {
      lock.unlock();
    }
  }
}
