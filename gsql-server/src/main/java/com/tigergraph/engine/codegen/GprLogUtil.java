/******************************************************************************
 * Copyright (c)  2017, TigerGraph Inc.
 * All rights reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 ******************************************************************************/
package com.tigergraph.engine.codegen;

/**
 * This class helps to generate the logging statement in gpr code.
 * wiki: https://tigergraph.atlassian.net/wiki/pages/viewpage.action?pageId=105119998
 * we use 'GUDFInfo(infologlevel, tag) for inside UDF log'.
 *
 * The infologlevel will be 0 or 4, depends on the value of query parameter __GQUERY_LOG_LEVEL
 * and the log statement's LogLevel
 */
public class GprLogUtil {
  public enum LogLevel {
    DEFAULT(0), BRIEF(1), DEBUG(2), DETAIL(3);
    private final int value;
    LogLevel(int v) {
      value = v;
    }
    @Override
    public String toString() {
      return Integer.toString(value);
    }
  }

  private static final String LEVEL = "__GQUERY_LOG_LEVEL";
  // the value of InfoLvl::off in gpelibidtypes, will print anyway
  private static final String InfoLvl_print = "0";
  // the value larger than all values in InfoLvl, will never print
  private static final String InfoLvl_no_print = "0xffff";

  private static String genLog(String logFlag, String log, String queryName) {
    String infologlevel = logFlag + " ? " + InfoLvl_print + " : " + InfoLvl_no_print;
    return
        "GUDFInfo(("
            + infologlevel
            + "), \""
            + queryName
            + "\") << " + "_request.requestid_ << \"|\" "
            + log
            + ";\n"
        ;
  }

  public static String genUserExceptionLog(String queryName) {
    return genUserLog("true", " << \"get an exception\"", queryName);
  }

  public static String genUserErrorLog(String queryName) {
    return genUserLog("true", " << \"action cmd encounter error\"", queryName);
  }

  public static String genUserLog(String logFlag, String log, String queryName) {
    return genLog(logFlag, log, queryName);
  }

  public static String genActionStartLog(String actionName, String queryName) {
    return genSystemLog(LogLevel.BRIEF, "starts " + actionName, queryName);
  }

  public static String genWorkerStartLog(String actionName, String queryName) {
    return genSystemLog(LogLevel.DEBUG, "worker start " + actionName, queryName);
  }

  public static String genWorkerFinishLog(String actionName, String queryName) {
    return genSystemLog(LogLevel.DEBUG, "worker finish " + actionName, queryName);
  }

  public static String genExecutorFinishLog(String actionName, String queryName) {
    return genSystemLog(LogLevel.DEBUG, "executor finish " + actionName, queryName);
  }

  public static String genGraphUpdateLog(String actionName, String queryName) {
    return genSystemLog(LogLevel.BRIEF, "graph update " + actionName, queryName);
  }

  public static String genLeaderStartLog(String actionName, String queryName) {
    return genSystemLog(LogLevel.BRIEF, "master starts " + actionName, queryName);
  }

  public static String genSystemLog(LogLevel level, String log, String queryName) {
    log = "\"" + log + "\"";
    return genLog(level.toString() + "<=" + LEVEL, "<< " + log, queryName);
  }

  public static String genConstrCode() {
    String cpp = "if (request.jsoptions_.isMember(\"" + LEVEL + "\")) {\n";
    cpp += "std::string __level = request.jsoptions_[\"" + LEVEL + "\"][0].asString();\n";
    boolean first = true;
    for (LogLevel ll : LogLevel.values()) {
      if (!first) {
        cpp += " else ";
      }
      first = false;
      cpp += "if (__level == \"" + ll.name().toLowerCase() + "\") {\n"
          + LEVEL + " = " + ll
          + ";\n}";
    }
    cpp += "\n} else {\n"
        + LEVEL + " = " + LogLevel.DEFAULT.toString() + ";\n"
        + "}\n"
    ;
    return cpp;
  }

  public static String genClassMemberDecl() {
    return "uint64_t " + LEVEL + ";\n";
  }
}
