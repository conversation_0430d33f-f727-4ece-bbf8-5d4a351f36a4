/**
 * ****************************************************************************
 * Copyright (c) 2015-2016, TigerGraph Inc.
 * All rights reserved
 * Unauthorized copying of this file, via any medium is
 * strictly prohibited
 * Proprietary and confidential
 * ****************************************************************************
 */
package com.tigergraph.engine.codegen;

import com.tigergraph.engine.util.CodeBuilder;
import com.tigergraph.schema.plan.query.Queries;
import com.tigergraph.schema.topology.*;
import com.tigergraph.utility.CodeGenUtil;

/**
 * * Generates c++ enum definitions that correctly enumerate vertex types, edge types and their
 * attributes. Needed in subsequent expression and condition translation pass because UDF code does
 * not refer to these by their string name, but by their int code.
 *
 * <p>In addition, generates typedefs allowing us to refer appropriately to attribute types. *
 */
public class Schema2EnumsGenerator {
  // graph schema
  Graph _schema;

  public Schema2EnumsGenerator(Graph s) {
    _schema = s;
  }

  /**
   * NOTE: don't define struct to group schema index, since it will be included
   * as header in query.cpp. If we skip compiling the query.cpp after schema change,
   * the schema struct is old one, which is inconsistent with current schema and
   * leads error in {@code enableAllEdgeTypesInGraph()}.
   *
   * Solution: we use map to group all schema index, and only declare functions in
   * header file which has no change after schema change.
   *
   * @return the file content of schemaIndex.hpp
   */
  public String getSchemaIndexDeclareStr() {
    CodeBuilder cpp = new CodeBuilder();
    cpp.append("#ifndef GSQL_SCHEMAINDEX_HPP_")
        .append("#define GSQL_SCHEMAINDEX_HPP_")
        .append("#include \"gle/engine/cpplib/TypeUtil.hpp\"")
        .append("#include \"gle/engine/cpplib/acclib/SetAccum.hpp\"")
        .append("#include \"olgp/gpe/serviceapi.hpp\"")
        .append("using namespace gperun;")
        .append("namespace UDIMPL {")
        .append("namespace " + _schema.GraphName + " {")
        .append("void addAllVertexTypesToSet(SetAccum<uint32_t>& __GQUERY__vts_);")
        .append("void enableAllEdgeTypesInGraph(gpelib4::TypeFilterController* edgeFilterCtrl);")
        .append("void enableAllTgtVertexTypesInGraph("
            + "gpelib4::TypeFilterController* edgeFilterCtrl);")
        .append("}")
        .append("}")
        .append("#endif /* GSQL_SCHEMAINDEX_HPP_ */");
    return cpp.toString();
  }

  /**
   * Generate update schema index functions
   *
   * @return the file content of schemaIndex.cpp
   */
  public String getIndexValueStr() {
    CodeBuilder cpp = new CodeBuilder();
    String fileNameDirective = CodeGenUtil.generateCppFileNameDirective(
        _schema.GraphName, "schemaIndex");
    cpp.append(fileNameDirective)
        .append("#include \"gle/engine/cpplib/TypeUtil.hpp\"")
        .append("#include \"olgp/gpe/serviceapi.hpp\"")
        .append(Queries.getSchemaHeaderIncludeString(_schema.GraphName))
        .append("using namespace gperun;")
        .append("namespace UDIMPL {")
        .append("namespace " + _schema.GraphName + " {");

    // add all vertex types into set
    cpp.append("// add all vertex types into set")
        .append("void addAllVertexTypesToSet(SetAccum<uint32_t>& __GQUERY__vts_) {");
    _schema.vertexTypes().stream().map(v -> v.VertexId)
        .forEach(id -> cpp.append("__GQUERY__vts_ += %d;", id));
    cpp.append("}");

    // add all edge types into type filter controller
    cpp.append("// used to enable all edge types in a graph when traverse 'L:s-(:e)-:t'")
        .append("void enableAllEdgeTypesInGraph(gpelib4::TypeFilterController* edgeFilterCtrl) {");
    _schema.edgeTypes().stream().map(e -> e.EdgeId)
        .forEach(id -> cpp.append("edgeFilterCtrl->EnableEdgeType(%d);", id));
    cpp.append("}");

    // add all target vertex types into type filter controller
    cpp.append("// used to enable all vertex types in a graph for target vertex type filter")
        .append("void enableAllTgtVertexTypesInGraph("
            + "gpelib4::TypeFilterController* edgeFilterCtrl) {");
    _schema.vertexTypes().stream().map(v -> v.VertexId)
        .forEach(id -> cpp.append("edgeFilterCtrl->EnableTgtVertexType(%d);", id));
    cpp.append("}");

    // close the namespace for graph
    cpp.append("}");
    // close the namespace for UDIMPL
    cpp.append("}");
    return cpp.toString();
  }
}
