package com.tigergraph.engine.codegen;

import com.tigergraph.schema.Util;
import org.antlr.v4.runtime.BaseErrorListener;
import org.antlr.v4.runtime.RecognitionException;
import org.antlr.v4.runtime.Recognizer;
import org.json.JSONObject;

/**
 * Customized anltr error msg.
 */
public class PrintStreamListener extends BaseErrorListener {

  private final int errorCode;

  public PrintStreamListener() {
    this.errorCode = -1;
  }

  public PrintStreamListener(final int errorCode) {
    this.errorCode = errorCode;
  }

  @Override
  public void syntaxError(Recognizer<?, ?> recognizer,
                          Object offendingSymbol,
                          int line,
                          int charPositionInLine,
                          String msg,
                          RecognitionException e) {
    // line 12:34 no viable alternative at ...
    String errMsg = String.format("line %d:%d %s", line, charPositionInLine, msg);
    Util.printlnToStream(errMsg);
    Util.LogError(errMsg);
    JSONObject errorInJson = new JSONObject();
    errorInJson.put("line", line);
    errorInJson.put("charpositioninline", charPositionInLine);
    errorInJson.put("msg", msg);
    if (errorCode != -1) {
      errorInJson.put("errorcode", errorCode);
    }
    com.tigergraph.schema.Util.errorsInJson.get().put(errorInJson);
  }
}
