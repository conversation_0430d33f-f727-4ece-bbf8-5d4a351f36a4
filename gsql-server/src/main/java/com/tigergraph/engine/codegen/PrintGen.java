/******************************************************************************
 * Copyright (c)  2017, TigerGraph Inc.
 * All rights reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 ******************************************************************************/
package com.tigergraph.engine.codegen;

import com.tigergraph.engine.queryplanner.action.PrintAction;
import com.tigergraph.engine.queryplanner.expression.AttributeExpression;
import com.tigergraph.engine.queryplanner.expression.Expression;
import com.tigergraph.engine.queryplanner.expression.LocalAccumExpression;
import com.tigergraph.engine.queryplanner.expression.Variable;
import com.tigergraph.engine.typechecker.DataType;
import com.tigergraph.engine.util.CodeBuilder;
import com.tigergraph.engine.util.Util;
import com.tigergraph.engine.util.error.SystemException;
import com.tigergraph.schema.topology.Attribute;
import com.tigergraph.schema.topology.Graph;
import com.tigergraph.schema.topology.VertexType;
import com.tigergraph.utility.CodeGenUtil;
import java.util.*;


/**
 * Generate print actions c++ code for GPR
 */
public class PrintGen {

  /**
   * function to generate vset write cpp code
   * @param action
   * @return
   */
  public static String getVsetWriteCppCode(PrintAction action, Graph schema,
                                           List<Variable> accums, CommonVariable cv) {
    CodeBuilder cppBuilder = new CodeBuilder("");
    cppBuilder.append("// json writer\ngutil::JSONWriter writer;\n");

    // only get 0 since we only support one vset in print action
    String vsetName = action.getInputVset().get(0);
    String vsetKey = action.getNames().size() > 0 ? action.getNames().get(0) : vsetName;
    // declare vset type id
    cppBuilder.append("int " + Util.GetVertexRunTimeTypeIdVarName(vsetName)
        + " = graphAPI->GetVertexType(v);");
    // for each vertex type of this vset
    Iterator<String> it = action.vtypes.get(0).iterator();
    for (int j = 0; j < action.vtypes.get(0).size(); j++ ) {
      String vtypeName = it.next();
      String runtimeTypeId = Util.GetVertexRunTimeTypeIdVarName(vsetName);
      String myTypeId = Util.GetVertexTypeVarName(vtypeName, cv);

      String condition = j == 0 ? "if" : "else if";
      condition += "(" + myTypeId + " != -1 && " + myTypeId + " == " + runtimeTypeId + ") {";
      cppBuilder.append(condition);
      cppBuilder.append("writer.WriteStartObject();");
      // write vertex name and id
      cppBuilder.append(
          CodeGenUtil.getJsonWriteCpp(cv, DataType.VERTEX_TYPE, "v_id", "v", null, false, false));
      // write vertex type
      cppBuilder.append(CodeGenUtil.getJsonWriteCpp(cv,
          DataType.STRING_TYPE,
          "v_type",
          "graphAPI->GetVertexTypeName(v)",
          null, false, false));
      boolean printAllAttrs = action.isPrintVset(vsetName);

      switch (cv.JsonAPI) {
        case V2:
          // v2 don't need to write v_set
          cppBuilder.append("writer.WriteName(\"attributes\");");
          break;
        default:
          throw new SystemException("Json API error.");
      }

      // write attributes
      cppBuilder.append("writer.WriteStartObject();");

      VertexType vt = schema.GetVertex(vtypeName);

      if (printAllAttrs && !vt.hasStringCompressAttribute()) {
        cppBuilder.append("vVertex.GetAttr().WriteAttributeToJson(writer);");
      } else {
        if (vt.Attributes != null) {
          for (Attribute att : vt.Attributes) {
            String attName = att.AttributeName;
            // skip internal att, if need to print tag will gen code below
            if (att.internalAttribute())
              continue;
            if (!printAllAttrs && !action.isAttributeUsed(vsetName, attName))
              continue;

            condition = "if (" + Util.GetVertexAttrVarName(vt.Name, attName, cv) + " != -1) {";
            cppBuilder.append(condition);
            DataType dtype = DataType.createAttributeDataType(att, cv);
            cppBuilder.append(
                CodeGenUtil.getJsonWriteCpp(cv,
                    dtype,
                    attName,
                    dtype.GetAttrCpp(attName, vt.Name, true, cv, att.IsNullable()),
                    null, false, att.IsNullable()));
            // end current attribute
            cppBuilder.append("}");
          }
        }
      }

      // write vaccums
      for (Variable variable : accums) {
        if (!printAllAttrs && !action.isAttributeUsed(vsetName, variable.name))
          continue;
        if (!variable.isTransformAccum()) {
          String codeGenName = variable.name;
          String originName = codeGenName.substring(0, codeGenName.lastIndexOf("_"));
          // only adding the vAccs declared before this print action
          if (action.isVAccDeclared("@" + originName)) {
            cppBuilder.append("writer.WriteName(\"@" + originName + "\");");
            cppBuilder.append("v_val." + variable.name
                + ".json_printer(writer, _request, context->GraphAPI(), true);");
          }
        }
      }
      cppBuilder.append("writer.WriteEndObject();");
      if (action.withEmbedding) {
        cppBuilder.append("writer.WriteName(\"Embeddings\");");
        cppBuilder.append("writer.WriteStartObject();");
        cppBuilder.append(
            "graphAPI->WriteEmbeddings(vVertex.vid(), context.GetRemoteEmbeddingsMap(), writer);");
        cppBuilder.append("writer.WriteEndObject();");
      }
      cppBuilder.append("writer.WriteEndObject();");
      cppBuilder.append("}");
    }

    // if has order, print json to map
    cppBuilder.append(outputJsonWriterWithOrderCpp());

    String str = cppBuilder.toString();
    //  TODO since UDF code gen has some difference
    str = str.replace("context->", "context.")
        .replace("v_attr->", "vVertex.GetAttr().")
        .replace("v_attr,", "&vVertex.GetAttr(),")
        .replace("v_val.", ExprGen.aliasValue("v") + ".");
    return str;
  }

  /**
   * Generate print expr to json c++ code
   * @param action
   * @return
   */
  public static String getExprWriteCppCode(PrintAction action, Graph schema, CommonVariable cv) {
    CodeBuilder cppBuilder = new CodeBuilder("");
    cppBuilder.append("// json writer\ngutil::JSONWriter writer;\n");
    if (action.filterType != PrintAction.FilterType.VSET_ACCESS) { // may already declared
      // declare and assign attribute var
      cppBuilder.append(getAttrVars(action, schema, cv));
    }
    // only get 0 since we only support one vset in print action
    String vsetName = action.getInputVset().get(0);

    cppBuilder.append("writer.WriteStartObject ();");
    // write vertex name and id
    cppBuilder.append(
        CodeGenUtil.getJsonWriteCpp(cv, DataType.VERTEX_TYPE, "v_id", "v", null, false, false));
    // write vertex type
    cppBuilder.append(CodeGenUtil.getJsonWriteCpp(
        cv,
        DataType.STRING_TYPE,
        "v_type",
        "graphAPI->GetVertexTypeName(v)",
        null, false, false));

    switch (cv.JsonAPI) {
      case V2:
        // v2 don't need to write v_set
        cppBuilder.append("writer.WriteName(\"attributes\");");
        break;
      default:
        throw new SystemException("Json API error.");
    }

    // start to write expr
    cppBuilder.append("writer.WriteStartObject();");

    if (!cppBuilder.toString().contains("int v_typeIDVar")) {
      // to avoid redeclare
      cppBuilder.append("int v_typeIDVar = graphAPI->GetVertexType(v);");
    }
    // for each expressions
    for (int i = 0; i < action.expressions.size(); ++i) {
      Expression expr = action.expressions.get(i);
      String name = action.names.size() > i
          ? action.names.get(i)
          : expr.text;
      cppBuilder.append(genExprCpp(expr, action, name, schema, false, cv));
    }

    cppBuilder.append("writer.WriteEndObject();");
    cppBuilder.append("writer.WriteEndObject();");
    // if has order, print json to map
    cppBuilder.append(outputJsonWriterWithOrderCpp());

    String str = cppBuilder.toString();
    //  TODO since UDF code gen has some difference
    str = str.replace("context->", "context.")
        .replace("v_attr->", "vVertex.GetAttr().")
        .replace("v_attr,", "&vVertex.GetAttr(),")
        .replace("v_val.", ExprGen.aliasValue("v") + ".");
    return str;
  }

  /**
   * add json writer to output global variable of print action
   * @return the output json writer cpp
   */
  private static String outputJsonWriterWithOrderCpp() {
    return "if (hasOrder) {\n"
            + "// print json to map for retrieve in oder later\n"
            + "context.GlobalVariableAdd(0, "
            + "MapAccum<VERTEX, JsonWriterGV>(v, JsonWriterGV(writer)));\n"
            + "} else {\n"
            + "// json writer\n"
            + "context.GlobalVariableAdd(0, JsonWriterGV(writer));\n"
            + "}";
  }

  /**
   * Generate print to file c++ code
   * @param action
   * @param schema
   * @return
   */
  public static String getExprWriteToFileCppCode(PrintAction action, Graph schema,
      CommonVariable cv) {
    CodeBuilder cppBuilder = new CodeBuilder("");
    // declare ostream
    cppBuilder.append("auto& ostream = context.GetOStream();");
    if (action.filterType != PrintAction.FilterType.VSET_ACCESS) { // may already declared
      // declare and assign attribute var
      cppBuilder.append(getAttrVars(action, schema, cv));
    }

    cppBuilder.append("ostream.WriteVertexId(v);");
    if (!cppBuilder.toString().contains("int v_typeIDVar")) {
      // to avoid redeclare
      cppBuilder.append("int v_typeIDVar = graphAPI->GetVertexType(v);");
    }
    for (Expression expression : action.expressions) {
      cppBuilder.append(genExprCpp(expression, action, "", schema, true, cv));
    }

    cppBuilder.append("ostream << \"\\n\";");
    String str = cppBuilder.toString();
    //  TODO since UDF code gen has some difference
    str = str.replace("context->", "context.")
        .replace("v_attr->", "vVertex.GetAttr().")
        .replace("v_attr,", "&vVertex.GetAttr(),")
        .replace("v_val.", ExprGen.aliasValue("v") + ".");
    return str;
  }

  public static String getAttrVars(PrintAction action, Graph schema, CommonVariable cv) {
    List<AttributeExpression> list = new ArrayList<>();

    for (Expression expression : action.expressions) {
      ExprGen.addAllAttrExpr(list, expression.getAllRecursiveExpr(AttributeExpression.class), cv);
    }

    final Expression filter = action.filter;
    if (filter != null) {
      ExprGen.addAllAttrExpr(list, filter.getAllRecursiveExpr(AttributeExpression.class), cv);
    }

    return ExprGen.getAttrVars(list, action, schema, cv.varType, cv, false);
  }

  private static String ostreamOutputStr(DataType dt, String value, CommonVariable cv) {
    String result = "";
    final String ostream = "ostream";
    String type = CodeGenUtil.getOutputTypeStr(dt);

    switch (type) {
      case "uint64_t":
        result += ostream + ".WriteUnsignedInt(static_cast<uint64_t>(" + value + "));\n";
        break;
      case "int64_t":
        result +=
            dt.isDatetimeNut()
                ? ostream + " << gutil::GtimeConverter::seconds_to_datetime_string("
                + value
                + ");\n"
                : ostream + ".WriteSignedInt(static_cast<int64_t>(" + value + "));\n";
        break;
      case "DATETIME":
        result +=
            ostream + " << gutil::GtimeConverter::seconds_to_datetime_string(" + value + ");\n";
        break;
      case "float":
      case "double":
        result += ostream + ".WriteSignedFloat(" + value + ");\n";
        break;
      case "string":
        result += ostream + " << " + value + ";\n";
        break;
      case "bool":
        result += ostream + " << " + value + "? \"true\":\"false\";\n";
        break;
      case "char":
      // char case, need to calculate size
      {
        // strnlen(const char* c, size_t maxLen)
        String len = ", strnlen(" + value + ", sizeof(" + value + "))";
        result += ostream + " << std::string(" + value + len + ");\n";
        break;
      }
      case "VERTEX":
        // TODO translate id
        result += ostream + ".WriteVertexId(" + value + ");\n";
        //result += ostream + " << " + value + ";\n";
        break;
      case "EDGE":
        break;
      case "JsonObject":
      case "JsonArray":
      case "json_printer":
        result += ostream + " << " + value + ";\n";
        break;
      default:
        String msg = "unexpected ostream output type " + type;
        Util.error(null, msg, 0, Util.ErrorType.SYSTEM_ERROR, cv);
    }
    return result;
  }

  /**
   * Generate cpp for expr in PRINT. Handle special case that allows multiple type expr
   * @param expr the expression to print
   * @param action
   * @param name name of the expr, used for json key
   * @param schema the graph schema
   * @param toFile whether the cpp is printing to file
   * @return
   */
  private static String genExprCpp(Expression expr, PrintAction action,
      String name, Graph schema, boolean toFile, CommonVariable cv) {
    String cpp = "";
    if (expr instanceof AttributeExpression && ((AttributeExpression) expr).types.isEmpty()) {
      // special case, print an attribute directly with multiple types
      String attrName = ((AttributeExpression) expr).attributeName;
      int isFirst = 0;
      for (VertexType vt : schema.vertexTypes()) {
        Attribute attr = vt.getAttribute(attrName);
        if (attr != null) {
          boolean useStdOptional = attr.IsNullable();
          DataType dt = DataType.createAttributeDataType(attr, cv);
          String exprCpp = dt.GetAttrCpp(attrName, vt.Name, true, cv, useStdOptional);
          String vt_name = Util.GetVertexTypeVarName(vt.Name, cv);
          cpp += (isFirst++ == 0 ? "if (" : "else if (")
              + "v_typeIDVar == " + vt_name + ") {\n";
          if (toFile) {
            cpp += "ostream << file_sep_;\n"
                + ostreamOutputStr(dt, exprCpp, cv)
            ;
          } else {
            cpp += CodeGenUtil.getJsonWriteCpp(cv, dt, name, exprCpp, null, false, useStdOptional);
          }
          cpp += "\n}\n";
        }
      }
    } else {
      Boolean useStdOptional = false;
      // normal case
      if (toFile) {
        cpp += "ostream << file_sep_;\n"
            + ostreamOutputStr(expr.dtype, expr.toCpp(action, cv), cv);
      } else {
        AttributeExpression attExpr = null;
        if (expr instanceof AttributeExpression) {
          attExpr = ((AttributeExpression) expr);
          String attrName = attExpr.attributeName;
          if (!attExpr.types.isEmpty()) {
            useStdOptional = attExpr.types.stream()
            .map(type -> cv._schema.getAttribute(type, attrName))
            .anyMatch(att -> att != null && att.IsNullable());
          }
        } else if (expr instanceof LocalAccumExpression) {
          useStdOptional = expr.dtype.isNullable();
        }
        /*
        Below logic is added to handle case like this in cpp code for nullable attribute
        if (V_year_uint64_t_flag) {
          writer.WriteNameString("V.year");
          auto writeValue = V_year_uint64_t;
          if (!writeValue.has_value()) {
            writer.WriteNull();
          } else {
            writer.WriteUnsignedInt(writeValue.value());
          }
        }
        */
        if (useStdOptional) {
          cpp += writeJsonCpp(cv, expr, action, name, true);
        } else {
          cpp += writeJsonCpp(cv, expr, action, name, false);
        }
      }
    }

    return cpp;
  }

  /**
   * helper method to invoke json cpp code
   */
  private static String writeJsonCpp(CommonVariable cv, Expression expr, PrintAction action,
      String name, boolean useStdOptional) {
    return CodeGenUtil.getJsonWriteCpp(
      cv,
      expr.dtype,
      name,
      expr.toCpp(action, cv),
      null, false, useStdOptional);
  }
}