package com.tigergraph.engine.codegen;

import com.tigergraph.engine.parser.GSQLParser;
import com.tigergraph.engine.parser.GSQLParser.*;
import com.tigergraph.engine.typechecker.DataType;
import com.tigergraph.engine.typechecker.VarType;
import com.tigergraph.engine.util.Util;
import com.tigergraph.engine.util.gsqlUtilBaseListener;
import com.tigergraph.utility.StringUtil;
import com.tigergraph.engine.queryplanner.expression.AttributeDynamicExpression;

import java.util.*;
import java.util.regex.Pattern;
import org.antlr.v4.runtime.ParserRuleContext;
import org.antlr.v4.runtime.tree.ParseTreeProperty;
import org.antlr.v4.runtime.tree.ParseTreeWalker;



/**
 * Generate Index Hint for UDF.
 * Simulate a CNF translate and generate IndexHint, then generate c++ code.
 */
public class IndexHintGenerator extends gsqlUtilBaseListener {
  public class IndexHint {

    public String attribute;
    public String vtype;
    public String dtype;
    // for equal
    public Set<String> eqValue;
    // for range
    public String minValue;
    public String maxValue;

    // comparator
    public String comp;

    public IndexHint(DataType dt) {
      setDtype(dt);
    }

    public IndexHint(IndexHint other) {
      attribute = other.attribute;
      vtype = other.vtype;
      maxValue = other.maxValue;
      minValue = other.minValue;
      dtype = other.dtype;
      comp = other.comp;
      eqValue = other.eqValue;
    }

    private void setDtype(DataType dt) {
      if (dt == null || !dt.isPrimitive() && !dt.isPrimitiveAccum()) {
        return;
      }
      dt = dt.getNut();
      switch (dt.Name) {
        case "uint":
          dtype = "uint64_t";
          minValue = "GSQL_UINT_MIN";
          maxValue = "GSQL_UINT_MAX";
          break;
        case "int":
        case "datetime":
          dtype = "int64_t";
          minValue = "GSQL_INT_MIN";
          maxValue = "GSQL_INT_MAX";
          break;
        case "string":
          dtype = "string";
          minValue = "\"\"";
          maxValue = "\"\"";
          break;
        default:
      }
    }
  }

  private ParseTreeProperty<List<List<IndexHint>>> _srcIndexHints;
  private ParseTreeProperty<Boolean> _negative;
  // get from BuildTypeMap, stores v/e types for each ctx
  ParseTreeProperty<Map<String, Set<String>>> _canonical2Types;
  private ParseTreeProperty<String> _exprCpp;

  /**
   * @param ctx The context of a WHERE condition.
   * @param name An unique name.
   * @return c++ code.
   */
  public static String generate(ParserRuleContext ctx, String name,
      CommonVariable cv, VarType varType,
      ParseTreeProperty<Map<String, Set<String>>> typeTree,
      ParseTreeProperty<String> exprCpp) {

    IndexHintGenerator generator = new IndexHintGenerator(cv, varType, typeTree, exprCpp);
    ParseTreeWalker walker = new ParseTreeWalker();
    walker.walk(generator, ctx);

    List<List<IndexHint>> indexHint = generator._srcIndexHints.get(ctx);

    if (indexHint == null || indexHint.isEmpty()) return "";

    String cpp = "{\ngvector<gvector<gindex::IndexPredicate>> hints_" + name + ";\n";
    for (List<IndexHint> hints : indexHint) {
      cpp += "{\n gvector<gindex::IndexPredicate> hint_" + name + ";\n";
      for (IndexHint hint : hints) {

        String vtype = Util.GetVertexTypeVarName(hint.vtype, cv);

        cpp += "if (" + vtype + " != -1 && " + hint.attribute + " != -1) {\n";
        cpp += "hint_" + name + ".emplace_back(gindex::IndexHint::CreateIndexPredicate_"
            + hint.dtype + "(" + hint.comp + ", " + vtype
            + ", " + hint.attribute + ", ";
        if (hint.eqValue != null) {
          String eqCpp = "";
          for (String expr : hint.eqValue) {
            eqCpp += expr + ", ";
          }
          cpp += "{"
              + eqCpp.substring(0, eqCpp.length() - 2)
              + "}\n";
        } else {
          cpp += "{"
              + hint.minValue
              + ", "
              + hint.maxValue
              + "}";
        }
        cpp += "));\n}\n";
      }
      cpp += "hints_" + name + ".push_back(std::move(hint_" + name + "));\n}\n";
    }
    cpp += "gindex::IndexHint indexHint_" + name + "(std::move(hints_" + name + "));\n";
    cpp += "context->SetSrcIndexHint(std::move(indexHint_" + name + "));\n}\n";
    return cpp;
  }

  public IndexHintGenerator(
      CommonVariable cv, VarType varType,
      ParseTreeProperty<Map<String, Set<String>>> typeTree,
      ParseTreeProperty<String> exprCpp) {
    super(varType, cv);
    _canonical2Types = typeTree;
    _exprCpp = exprCpp;
    _srcIndexHints = new ParseTreeProperty<>();
    _negative = new ParseTreeProperty<>();
  }

  @Override
  public void enterCondParen(GSQLParser.CondParenContext ctx) {
    if (_negative.get(ctx.getParent()) != null) {
      _negative.put(ctx, _negative.get(ctx.getParent()));
    }
  }

  @Override
  public void enterCondAnd(GSQLParser.CondAndContext ctx) {
    if (_negative.get(ctx.getParent()) != null) {
      _negative.put(ctx, _negative.get(ctx.getParent()));
    }
  }

  @Override
  public void enterCondOr(GSQLParser.CondOrContext ctx) {
    if (_negative.get(ctx.getParent()) != null) {
      _negative.put(ctx, _negative.get(ctx.getParent()));
    }
  }

  @Override
  public void enterCondNot(GSQLParser.CondNotContext ctx) {
    if (_negative.get(ctx.getParent()) != null) {
      _negative.put(ctx, !_negative.get(ctx.getParent()).booleanValue());
    } else {
      _negative.put(ctx, Boolean.TRUE);
    }
  }

  @Override
  public void enterCondCompare(GSQLParser.CondCompareContext ctx) {
    if (_negative.get(ctx.getParent()) != null) {
      _negative.put(ctx, _negative.get(ctx.getParent()));
    }
  }

  @Override
  public void exitCondParen(GSQLParser.CondParenContext ctx) {
    _srcIndexHints.put(ctx, _srcIndexHints.get(ctx.condition()));
  }

  @Override
  public void exitCondAnd(GSQLParser.CondAndContext ctx) {
    boolean negative = false;
    if (_negative.get(ctx.getParent()) != null && _negative.get(ctx.getParent()).booleanValue()) {
      negative = true;
    }
    List<List<IndexHint>> left = _srcIndexHints.get(ctx.condition(0));
    List<List<IndexHint>> right = _srcIndexHints.get(ctx.condition(1));
    List<List<IndexHint>> result = negative ? genOrHint(left, right) : genAndHint(left, right);
    _srcIndexHints.put(ctx, result);
  }

  @Override
  public void exitCondOr(GSQLParser.CondOrContext ctx) {
    boolean negative = false;
    if (_negative.get(ctx.getParent()) != null && _negative.get(ctx.getParent()).booleanValue()) {
      negative = true;
    }
    List<List<IndexHint>> left = _srcIndexHints.get(ctx.condition(0));
    List<List<IndexHint>> right = _srcIndexHints.get(ctx.condition(1));
    List<List<IndexHint>> result = negative ? genAndHint(left, right) : genOrHint(left, right);
    _srcIndexHints.put(ctx, result);
  }

  @Override
  public void exitCondNot(GSQLParser.CondNotContext ctx) {
    _srcIndexHints.put(ctx, _srcIndexHints.get(ctx.condition()));
  }

  @Override
  public void exitCondCompare(GSQLParser.CondCompareContext ctx) {
    String op = Util.getComparisonOp(ctx);
    boolean negative = false;
    if (_negative.get(ctx.getParent()) != null && _negative.get(ctx.getParent()).booleanValue()) {
      negative = true;
    }

    if (negative) {
      negative = false;
      switch (op) {
        case "==":
          op = "!=";
          break;
        case "!=":
          op = "==";
          break;
        case ">":
          op = "<=";
          break;
        case ">=":
          op = "<";
          break;
        case "<":
          op = ">=";
          break;
        case "<=":
          op = ">";
          break;
        default:
          negative = true;
      }
    }

    if (!"==".equals(op)
        && !"<".equals(op)
        && !"<=".equals(op)
        && !">".equals(op)
        && !">=".equals(op)) {
      return;
    }

    ExprAtomContext attr;
    ParserRuleContext constant;
    if (isAttri(ctx.exprAtom(0)) && isConstant(ctx.exprAtom(1))) {
      attr = ctx.exprAtom(0);
      constant = ctx.exprAtom(1);
    } else if (isAttri(ctx.exprAtom(1)) && isConstant(ctx.exprAtom(0))) {
      attr = ctx.exprAtom(1);
      constant = ctx.exprAtom(0);
      if (op.contains(">")) {
        op = op.replace(">", "<");
      } else if (op.contains("<")) {
        op = op.replace("<", ">");
      }
    } else {
      return;
    }

    // get name of variable and normalize it
    String varName;
    // get name of attribute
    String attrName;
    boolean isGetAttrGV = false;

    if (attr instanceof AllTupleFieldAccessContext) {
      varName = ((AllTupleFieldAccessContext) attr).exprAtom().getText();
      attrName = ((AllTupleFieldAccessContext) attr).ident().getText();
    } else {
      // getAttr case, attrName can have quotation marks
      varName = ((AllFuncAccessContext) attr).exprAtom().getText();
      GSQLParser.ExprContext expr =
          ((AllFuncAccessContext) attr).accFuncRval().funcParams().expr(0);
      attrName = expr.getText();
      // skip if the parameter is a Global Variable
      boolean isParam = _varType.IsParameter(attrName);
      boolean isConst = Util.isStringConst(expr);
      if (!isParam && !isConst) {
        isGetAttrGV = true;
        String vName = _varType.getCodeGenName(attrName, ctx, "GlobalVar");
        attrName = "context->GlobalVariable_GetValue<gvector<int>> (GV_PosMapV_"
            + vName + ")";
      } else {
        attrName = AttributeDynamicExpression.getAttrKey(isConst,
          StringUtil.stripQuote(attrName));
      }
    }

    List<List<IndexHint>> result = new ArrayList<>();
    List<IndexHint> res = new ArrayList<>();

    // get type of normalized variable
    Set<String> Types = getSrcTypeFromMap(varName, attr);
    if (Types == null || Types.isEmpty()) {
      return;
    }
    DataType dt = _varType.getType(attr);
    if (dt == null) return;

    boolean unsupportedIndexDataType = false;
    for (String type : Types) {
      IndexHint indexHint = new IndexHint(dt);
      indexHint.vtype = type;
      if (attr instanceof AllTupleFieldAccessContext) {
        indexHint.attribute = Util.GetVertexAttrVarName(type, attrName, commonVariable);
      } else {
        String va_name = Util.GetVertexTypeVarName(type, commonVariable);
        indexHint.attribute = isGetAttrGV
            ? AttributeDynamicExpression.getAttrPosGV(attrName, va_name)
            : AttributeDynamicExpression.getAttrPosConst(attrName, va_name, false);
      }
      if (indexHint.dtype == null) {
        unsupportedIndexDataType = true;
        break;
      }
      switch (op) {
        case "==": {
          indexHint.eqValue = new HashSet<>();
          indexHint.eqValue.add(_exprCpp.get(constant));
          indexHint.comp = "gindex::EQUAL";
          break;
        }
        case "<=": {
          indexHint.maxValue = _exprCpp.get(constant);
          indexHint.comp = "gindex::RANGE_GE_LE";
          break;
        }
        case "<": {
          indexHint.maxValue = _exprCpp.get(constant);
          indexHint.comp = "gindex::RANGE_GE_LT";
          break;
        }
        case ">=": {
          indexHint.minValue = _exprCpp.get(constant);
          indexHint.comp = "gindex::RANGE_GE_LE";
          break;
        }
        case ">": {
          indexHint.minValue = _exprCpp.get(constant);
          indexHint.comp = "gindex::RANGE_GT_LE";
          break;
        }
      }
      res.add(indexHint);
    }

    if (!unsupportedIndexDataType) result.add(res);
    _srcIndexHints.put(ctx, result);
  }

  @Override
  public void exitCondBetween(GSQLParser.CondBetweenContext ctx) {
    if (_negative.get(ctx.getParent()) != null && _negative.get(ctx.getParent()).booleanValue()) {
      return;
    }

    if (!isAttri(ctx.exprAtom(0))
        || !isConstant(ctx.exprAtom(1))
        || !isConstant(ctx.exprAtom(2))) {
      return;
    }

    ExprAtomContext attr = ctx.exprAtom(0);
    ParserRuleContext constant1 = ctx.exprAtom(1);
    ParserRuleContext constant2 = ctx.exprAtom(2);

    // get name of variable and normalize it
    String varName;
    // get name of attribute
    String attrName;
    boolean isGetAttrGV = false;

    if (attr instanceof AllTupleFieldAccessContext) {
      varName = ((AllTupleFieldAccessContext) attr).exprAtom().getText();
      attrName = ((AllTupleFieldAccessContext) attr).ident().getText();
    } else {
      // getAttr case, attrName can have quotation marks
      varName = ((AllFuncAccessContext) attr).exprAtom().getText();
      GSQLParser.ExprContext expr =
          ((AllFuncAccessContext) attr).accFuncRval().funcParams().expr(0);
      attrName = expr.getText();
      // skip if the parameter is a Global Variable
      boolean isParam = _varType.IsParameter(attrName);
      boolean isConst = Util.isStringConst(expr);
      if (!isParam && !isConst) {
        isGetAttrGV = true;
        String vName = _varType.getCodeGenName(attrName, ctx, "GlobalVar");
        attrName = "context->GlobalVariable_GetValue<gvector<int>> (GV_PosMapV_"
            + vName + ")";
      } else {
        attrName = AttributeDynamicExpression.getAttrKey(isConst,
          StringUtil.stripQuote(attrName));
      }
    }

    List<List<IndexHint>> result = new ArrayList<>();
    List<IndexHint> res = new ArrayList<>();

    // get type of normalized variable
    Set<String> Types = getSrcTypeFromMap(varName, attr);
    if (Types == null || Types.isEmpty()) {
      return;
    }
    DataType dt = _varType.getType(attr);
    if (dt == null) return;

    boolean unsupportedIndexDataType = false;
    for (String type : Types) {
      IndexHint indexHint = new IndexHint(dt);
      if (indexHint.dtype == null) {
        unsupportedIndexDataType = true;
        break;
      }
      indexHint.minValue = _exprCpp.get(constant1);
      indexHint.maxValue = _exprCpp.get(constant2);
      indexHint.comp = "gindex::RANGE_GE_LE";
      if (attr instanceof AllTupleFieldAccessContext) {
        indexHint.attribute = Util.GetVertexAttrVarName(type, attrName, commonVariable);
      } else {
        String va_name = Util.GetVertexTypeVarName(type, commonVariable);
        indexHint.attribute = isGetAttrGV
            ? AttributeDynamicExpression.getAttrPosGV(attrName, va_name)
            : AttributeDynamicExpression.getAttrPosConst(attrName, va_name, false);
      }
      indexHint.vtype = type;
      res.add(indexHint);
    }

    if (!unsupportedIndexDataType) result.add(res);
    _srcIndexHints.put(ctx, result);
  }

  @Override
  public void exitCondInOrNot(GSQLParser.CondInOrNotContext ctx) {
    boolean negative = false;
    if (_negative.get(ctx.getParent()) != null && _negative.get(ctx.getParent()).booleanValue()) {
      negative = true;
    }

    negative = negative ^ ctx.NOT() != null;
    if (negative) return;

    if (!isAttri(ctx.exprAtom(0))
        || ! (ctx.exprAtom(1) instanceof SetInlistContext)) {
      return;
    }

    ExprAtomContext attr = ctx.exprAtom(0);
    SetInlistContext set = (SetInlistContext) ctx.exprAtom(1);

    // all IN expr must be constant
    for (ExprAtomContext context : set.exprAtom()) {
      if (!isConstant(context)) return;
    }

    // get name of variable and normalize it
    String varName;
    // get name of attribute
    String attrName;
    boolean isGetAttrGV = false;

    if (attr instanceof AllTupleFieldAccessContext) {
      varName = ((AllTupleFieldAccessContext) attr).exprAtom().getText();
      attrName = ((AllTupleFieldAccessContext) attr).ident().getText();
    } else {
      // getAttr case, attrName can have quotation marks
      varName = ((AllFuncAccessContext) attr).exprAtom().getText();
      GSQLParser.ExprContext expr =
          ((AllFuncAccessContext) attr).accFuncRval().funcParams().expr(0);
      attrName = expr.getText();
      // skip if the parameter is a Global Variable
      boolean isParam = _varType.IsParameter(attrName);
      boolean isConst = Util.isStringConst(expr);
      if (!isParam && !isConst) {
        isGetAttrGV = true;
        String vName = _varType.getCodeGenName(attrName, ctx, "GlobalVar");
        attrName = "context->GlobalVariable_GetValue<gvector<int>> (GV_PosMapV_"
            + vName + ")";
      } else {
        attrName = AttributeDynamicExpression.getAttrKey(isConst,
          StringUtil.stripQuote(attrName));
      }
    }

    List<List<IndexHint>> result = new ArrayList<>();
    List<IndexHint> res = new ArrayList<>();

    // get type of normalized variable
    Set<String> Types = getSrcTypeFromMap(varName, attr);
    if (Types == null || Types.isEmpty()) {
      return;
    }
    DataType dt = _varType.getType(attr);
    if (dt == null) return;

    boolean unsupportedIndexDataType = false;
    for (String type : Types) {
      IndexHint indexHint = new IndexHint(dt);
      if (indexHint.dtype == null) {
        unsupportedIndexDataType = true;
        break;
      }
      indexHint.eqValue = new HashSet<>();
      for (ExprAtomContext context : set.exprAtom()) {
        indexHint.eqValue.add(_exprCpp.get(context));
      }
      indexHint.comp = "gindex::EQUAL";
      if (attr instanceof AllTupleFieldAccessContext) {
        indexHint.attribute = Util.GetVertexAttrVarName(type, attrName, commonVariable);
      } else {
        String va_name = Util.GetVertexTypeVarName(type, commonVariable);
        indexHint.attribute = isGetAttrGV
            ? AttributeDynamicExpression.getAttrPosGV(attrName, va_name)
            : AttributeDynamicExpression.getAttrPosConst(attrName, va_name, false);
      }
      indexHint.vtype = type;
      res.add(indexHint);
    }

    if (!unsupportedIndexDataType) result.add(res);
    _srcIndexHints.put(ctx, result);
  }

  static final Pattern pattern = Pattern.compile("\"[^_%\\[\\]]+%.*\"");
  @Override
  public void exitCondLike(GSQLParser.CondLikeContext ctx) {
    boolean negative = false;
    if (_negative.get(ctx.getParent()) != null && _negative.get(ctx.getParent()).booleanValue()) {
      negative = true;
    }

    negative = negative ^ ctx.NOT() != null;
    if (negative) return;

    if (!isAttri(ctx.exprAtom(0))
        || ! (ctx.exprAtom(1) instanceof GSQLParser.ExprConstantContext)) {
      return;
    }

    ExprAtomContext attr = ctx.exprAtom(0);
    String string = ctx.exprAtom(1).getText();

    // only support prefix like "abc%"
    if (!pattern.matcher(string).matches()) {
      return;
    }

    // get name of variable and normalize it
    String varName;
    // get name of attribute
    String attrName;
    boolean isGetAttrGV = false;

    if (attr instanceof AllTupleFieldAccessContext) {
      varName = ((AllTupleFieldAccessContext) attr).exprAtom().getText();
      attrName = ((AllTupleFieldAccessContext) attr).ident().getText();
    } else {
      // getAttr case, attrName can have quotation marks
      varName = ((AllFuncAccessContext) attr).exprAtom().getText();
      GSQLParser.ExprContext expr =
          ((AllFuncAccessContext) attr).accFuncRval().funcParams().expr(0);
      attrName = expr.getText();
      // skip if the parameter is a Global Variable
      boolean isParam = _varType.IsParameter(attrName);
      boolean isConst = Util.isStringConst(expr);
      if (!isParam && !isConst) {
        isGetAttrGV = true;
        String vName = _varType.getCodeGenName(attrName, ctx, "GlobalVar");
        attrName = "context->GlobalVariable_GetValue<gvector<int>> (GV_PosMapV_"
            + vName + ")";
      } else {
        attrName = AttributeDynamicExpression.getAttrKey(isConst,
          StringUtil.stripQuote(attrName));
      }
    }
    List<List<IndexHint>> result = new ArrayList<>();
    List<IndexHint> res = new ArrayList<>();

    // get type of normalized variable
    Set<String> Types = getSrcTypeFromMap(varName, attr);
    if (Types == null || Types.isEmpty()) {
      return;
    }
    DataType dt = _varType.getType(attr);
    if (dt == null) return;

    boolean unsupportedIndexDataType = false;
    for (String type : Types) {
      IndexHint indexHint = new IndexHint(dt);
      if (indexHint.dtype == null) {
        unsupportedIndexDataType = true;
        break;
      }
      string = string.substring(0, string.indexOf('%'));

      indexHint.minValue = "std::string(" + string + "\")";
      indexHint.maxValue = "std::string(" + string + "\") + std::string(1, (char)255)";
      indexHint.comp = "gindex::RANGE_GE_LT";
      if (attr instanceof AllTupleFieldAccessContext) {
        indexHint.attribute = Util.GetVertexAttrVarName(type, attrName, commonVariable);
      } else {
        String va_name = Util.GetVertexTypeVarName(type, commonVariable);
        indexHint.attribute = isGetAttrGV
            ? AttributeDynamicExpression.getAttrPosGV(attrName, va_name)
            : AttributeDynamicExpression.getAttrPosConst(attrName, va_name, false);
      }
      indexHint.vtype = type;
      res.add(indexHint);
    }

    if (!unsupportedIndexDataType) result.add(res);
    _srcIndexHints.put(ctx, result);
  }

  // canonical name to type set, e.g.
  // src --> {vt1, vt2}
  private Set<String> getSrcTypeFromMap(String alias, ParserRuleContext ctx) {

    // if in print, we should get "v"
    // o/w get origin normName: "src" or "tgt"
    String norm = !Util.InPrint(ctx) ? _varType.getNormalizeName(ctx, alias).getIsSrcOrTgt()
        : _varType.getNormalizeName(ctx, alias).getName();


    Map<String, Set<String>> map = _canonical2Types.get(ctx);
    if (map == null || "e".equals(norm) || "tgt".equals(norm)) return null;
    return map.get(norm);
  }

  private boolean isAttri(ParserRuleContext ctx) {
    if (ctx instanceof AllTupleFieldAccessContext) {
      AllTupleFieldAccessContext attr = (AllTupleFieldAccessContext) ctx;
      String varName = attr.exprAtom().getText();
      if (attr.exprAtom() instanceof GSQLParser.ExprRhsIdentContext) {
        if (Util.InFilterCond(ctx)
            || _varType.GetVarType(varName, ctx) == VarType.GLOBAL_VARIABLE
            || _varType.IsParameter(varName)) {
          return false;
        }
        return true;
      }
    } else if (ctx instanceof AllFuncAccessContext) {
      AllFuncAccessContext func = (AllFuncAccessContext) ctx;
      String fName = func.accFuncRval().funcName().getText().toLowerCase();
      if (fName.equals("getattr") || fName.equals("gettags")) {
        String varName = func.exprAtom().getText();
        if (func.exprAtom() instanceof GSQLParser.ExprRhsIdentContext) {
          if (Util.InFilterCond(ctx)
              || _varType.GetVarType(varName, ctx) == VarType.GLOBAL_VARIABLE
              || _varType.IsParameter(varName)) {
            return false;
          }
          return true;
        }
      }
    }
    return false;
  }

  private boolean isConstant(ParserRuleContext ctx) {
    if (ctx instanceof NonLogicalExprContext) {
      return isConstant(((NonLogicalExprContext) ctx).exprAtom());
    }

    if (ctx instanceof GSQLParser.ExprGAccRvalContext
        || ctx instanceof GSQLParser.ExprConstantContext) {
      return true;
    }
    if (ctx instanceof GSQLParser.ExprRhsIdentContext) {
      String varName = ctx.getText();
      if (_varType.GetVarType(varName, ctx) == VarType.GLOBAL_VARIABLE
          || _varType.IsParameter(varName)) {
        return true;
      }
      return false;
    }

    if (ctx instanceof GSQLParser.ExprParenContext) {
      return isConstant(((ExprParenContext) ctx).exprAtom());
    }
    if (ctx instanceof GSQLParser.ExprSignContext) {
      return isConstant(((ExprSignContext) ctx).exprAtom());
    }

    if (ctx instanceof ExprFuncRvalContext) {
      FuncRvalContext funcCtx = ((ExprFuncRvalContext) ctx).funcRval();
      // Check whether all function parameters are constants
      // Note: This is under the assumption that the function have consistent output given the
      // same input, i.e. the function is not randomized.
      String funcName = funcCtx.funcName().getText();
      if (funcName.toLowerCase().equals("rand")) {
        // rand()/rand(seed) is randomized function
        return false;
      }
      for (ParserRuleContext c : funcCtx.funcParams().expr()) {
        if (!isConstant(c)) {
          return false;
        }
      }
      return true;
    }

    if (ctx instanceof ExprMultDivModContext) {
      ExprMultDivModContext c = (ExprMultDivModContext) ctx;
      return isConstant(c.exprAtom(0)) && isConstant(c.exprAtom(1));
    }
    if (ctx instanceof ExprAddDiffContext) {
      ExprAddDiffContext c = (ExprAddDiffContext) ctx;
      return isConstant(c.exprAtom(0)) && isConstant(c.exprAtom(1));
    }
    if (ctx instanceof ExprShiftContext) {
      ExprShiftContext c = (ExprShiftContext) ctx;
      return isConstant(c.exprAtom(0)) && isConstant(c.exprAtom(1));
    }
    if (ctx instanceof ExprBitAndContext) {
      ExprBitAndContext c = (ExprBitAndContext) ctx;
      return isConstant(c.exprAtom(0)) && isConstant(c.exprAtom(1));
    }
    if (ctx instanceof ExprBitOrContext) {
      ExprBitOrContext c = (ExprBitOrContext) ctx;
      return isConstant(c.exprAtom(0)) && isConstant(c.exprAtom(1));
    }
    if (ctx instanceof SetScalarFunctionContext) {
      return isConstant(((SetScalarFunctionContext) ctx).exprAtom());
    }

    return false;
  }

  private List<List<IndexHint>> genOrHint(List<List<IndexHint>> left, List<List<IndexHint>> right) {
    if (left == null || left.isEmpty() || right == null || right.isEmpty()) {
      return null;
    }

    // merge when attr == 0 OR attr == 5  -->  attr IN (0, 5)
    if (left != null && left.size() == 1 && right != null && right.size() == 1) {
      int leftsize = left.get(0).size();
      int rightsize = right.get(0).size();
      List<IndexHint> results = new ArrayList<>();
      for (int i = 0; i < leftsize && i < rightsize; ++i) {
        IndexHint l = left.get(0).get(i);
        IndexHint r = right.get(0).get(i);
        if (l.attribute.equals(r.attribute) && l.eqValue != null && r.eqValue != null) {
          l.eqValue.addAll(r.eqValue);
          results.add(l);
        }
      }

      if (results.size() == leftsize && results.size() == rightsize) {
        left.clear();
        right.clear();
        left.add(results);
        return left;
      }
    }

    List<List<IndexHint>> result = new ArrayList<>();
    for (List<IndexHint> l : left) {
      for (List<IndexHint> r : right) {
        List<IndexHint> res = new ArrayList<>();
        res.addAll(l);
        res.addAll(r);
        result.add(res);
      }
    }

    return result;
  }

  private List<List<IndexHint>> genAndHint(
      List<List<IndexHint>> left, List<List<IndexHint>> right) {

    // merge when attr > 0 AND attr < 5  -->  attr BETWEEN 0 AND 5
    if (left != null && left.size() == 1 && right != null && right.size() == 1) {
      int leftsize = left.get(0).size();
      int rightsize = right.get(0).size();
      List<IndexHint> results = new ArrayList<>();
      for (int i = 0; i < leftsize && i < rightsize; ++i) {
        IndexHint result = mergeIndexHint(left.get(0).get(i), right.get(0).get(i));
        if (result != null) {
          results.add(result);
        }
      }

      if (results.size() == leftsize && results.size() == rightsize) {
        left.clear();
        right.clear();
        left.add(results);
      }
    }

    List<List<IndexHint>> result = new ArrayList<>();
    if (left != null) {
      result.addAll(left);
    }
    if (right != null) {
      result.addAll(right);
    }
    return result;
  }

  /**
   * Merge two IndexHint when left is attr <(=) const, and right is attr >(=) const.
   */
  IndexHint mergeIndexHint(IndexHint left, IndexHint right) {

    if (!left.attribute.equals(right.attribute)) {
      return null;
    }

    // if left is >(=), and right is not >(=), swap
    if ((left.maxValue.equals("GSQL_UINT_MAX")
        || left.maxValue.equals("GSQL_INT_MAX")
        || left.maxValue.equals("\"\""))
        && !(right.maxValue.equals("GSQL_UINT_MAX")
        || right.maxValue.equals("GSQL_INT_MAX")
        || right.maxValue.equals("\"\""))
        && !left.comp.equals("gindex::EQUAL")
        && !right.comp.equals("gindex::EQUAL")) {
      return mergeIndexHint(right, left);
    }


    // if left is <(=), and right is >(=)
    if ((left.minValue.equals("GSQL_UINT_MIN")
        || left.minValue.equals("GSQL_INT_MIN")
        || left.minValue.equals("\"\""))
        && (right.maxValue.equals("GSQL_UINT_MAX")
        || right.maxValue.equals("GSQL_INT_MAX")
        || right.maxValue.equals("\"\""))
        && !left.comp.equals("gindex::EQUAL")
        && !right.comp.equals("gindex::EQUAL")) {

      IndexHint hint = new IndexHint(left);
      hint.minValue = right.minValue;
      hint.comp = "gindex::RANGE_";
      if (right.comp.equals("gindex::RANGE_GT_LT")
          || right.comp.equals("gindex::RANGE_GT_LE")) {
        hint.comp += "GT_";
      } else {
        hint.comp += "GE_";
      }
      if (left.comp.equals("gindex::RANGE_GT_LT")
          || left.comp.equals("gindex::RANGE_GE_LT")) {
        hint.comp += "LT";
      } else {
        hint.comp += "LE";
      }
      return hint;
    }

    return null;
  }
}
