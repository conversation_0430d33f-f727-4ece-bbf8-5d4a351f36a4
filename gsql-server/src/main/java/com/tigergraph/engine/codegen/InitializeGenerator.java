/**
 * ****************************************************************************
 * Copyright (c) 2015-2016, TigerGraph Inc.
 * All rights reserved
 * Unauthorized copying of this file, via any medium is
 * strictly prohibited
 * Proprietary and confidential
 * ****************************************************************************
 */
package com.tigergraph.engine.codegen;

import com.tigergraph.engine.parser.GSQLParser;
import com.tigergraph.engine.parser.GSQLParser.GeneralAccTypeContext;
import com.tigergraph.engine.parser.GSQLParser.HeapSizeContext;
import com.tigergraph.engine.parser.GSQLParser.BitwiseAccTypeContext;
import com.tigergraph.engine.parser.GSQLParserBaseListener;
import com.tigergraph.engine.typechecker.DataType;
import com.tigergraph.engine.typechecker.VarType;
import com.tigergraph.engine.util.Util;
import com.tigergraph.utility.CodeGenUtil;
import java.util.Map;
import org.antlr.v4.runtime.tree.ParseTree;
import org.antlr.v4.runtime.tree.ParseTreeProperty;


/**
 * * Translates gsql seed statement to UDF SetActiveFlag () calls. Translates vertex acc
 * initializations to WriteAll () calls. Puts them all in UDF Initialize ().
 *
 * <p>For now, assumes that there is a single seed statement, which appears before the first query
 * block. This should be checked by the semantic checker. In view of future relaxations of this
 * restriction, a map from seed set name to corresponding seed activation c++ is generated. It is
 * not yet used anywhere... *
 */
public class InitializeGenerator extends GSQLParserBaseListener {

  // accumulates c++ code setting up a WriteAll () call to initialize all vertex values
  // based on the initialization directive in the gsql vertex acc delcaration.
  private String _writeCpp;

  // captures generated code
  private StringBuilder _udfCpp;

  // generated by ExprAndCondTranslator, add declaration before set
  // condition check or computation
  private Map<String, String> _setDeclare;

  // heap accum related variables
  private String _heapSize;

  // annotates nodes with cpp code. computed in ExprAndCondTranslator.
  ParseTreeProperty<String> _cppCode;

  private CommonVariable commonVariable;
  private VarType _varType;

  public InitializeGenerator(
      VarType varType,
      CommonVariable cv,
      ParseTreeProperty<String> cppCode,
      StringBuilder u,
      Map<String, String> setDeclare) {

    _varType = varType;
    commonVariable = cv;
    _cppCode = cppCode;
    _writeCpp = "";
    _udfCpp = u;
    _setDeclare = setDeclare;

    _heapSize = String.valueOf(Integer.MAX_VALUE);
  }

  //////////////////
  // listener rules
  /////////////////
  @Override
  public void exitJob(GSQLParser.JobContext ctx) {
    _udfCpp.append(genInitialize());
  }

  @Override
  public void exitAccDecl(GSQLParser.AccDeclContext ctx) {
    for (int i = 0; i < ctx.accnames().accnameconst().size(); i++) {
      if (ctx.accnames().accnameconst(i).accname().VACCNAME() != null) {
        String vaccName = ctx.accnames().accnameconst(i).accname().getText().substring(1);
        String codeGenName = _varType.getCodeGenName(vaccName, ctx, "VACC");

        // heap resize
        if (Util.HasChildOf(ctx, GSQLParser.TopkHeapTypeContext.class)) {
          _writeCpp +=
              "   vval" + commonVariable.VValueAccessor() + codeGenName
                  + ".resize(" + _heapSize + ");\n";
        } else {
          String typeName;
          if (ctx.accType() instanceof GeneralAccTypeContext) {
            typeName = ((GeneralAccTypeContext) ctx.accType()).ident().getText();
          } else {
            typeName = ((BitwiseAccTypeContext) ctx.accType()).bitwiseAccumType().getText();
          }
          if (_varType.GetDtByTypedefAccumName(typeName) != null) {
            DataType dt = _varType.GetDtByTypedefAccumName(typeName);
            if (dt.size != -1) {
              _writeCpp +=
                  "   vval" + commonVariable.VValueAccessor() + codeGenName
                      + ".resize(" + dt.size + ");\n";
            }
          }
        }

        // no initial value
        // change for initialization of expression
        if (ctx.accnames().accnameconst(i).EQ() == null) { // no initialization required
          continue;
        }

        _writeCpp +=
            "   vval"
                + commonVariable.VValueAccessor()
                + codeGenName
                + " = "
                + getCpp(ctx.accnames().accnameconst(i).expr())
                + ";\n";
      }
    }
  }

  @Override
  public void exitTopkHeapType(GSQLParser.TopkHeapTypeContext ctx) {
    HeapSizeContext n = ctx.heapSize();
    if (n != null) {
      _heapSize = n.rhsIdent() != null ? "_" + n.getText() : n.getText();
    } else {
      _heapSize = String.valueOf(Integer.MAX_VALUE); 
    }
  }

  //////////////
  // helper fns
  /////////////
  private String genInitialize() {
    String initCpp =
        "ALWAYS_INLINE "
            + "void Initialize (gpelib4::GlobalSingleValueContext<V_VALUE>* context) {\n"
            + "\n";

    initCpp += "  if (_request.error_) {\n    context->Abort();\n    return;\n  }\n";

    // UDF will abort once it meets unexpected error. That means there are many places
    // that the UDF may abort.
    // Set the value for "jsonAPIVersion_" here, then no need to worry about it later.
    initCpp += "  _request.SetJSONAPIVersion(\"" + commonVariable.JsonAPI.getVersion() + "\");\n";

    initCpp += "\n" + getDeclareCpp("Initialize");

    initCpp +=
        "   // vertex acc initialization:\n"
            + (commonVariable.VValueUsePointer
                ? "   V_VALUE vval (new " + CodeGenUtil.toCppName("VertexVal") + " ());\n"
                : "   V_VALUE vval = V_VALUE ();\n")
            + _writeCpp
            + "   context->WriteAll (vval, false);\n\n"
            + "   vvalptr = new V_VALUE(vval);\n\n"
            + "   this->writeAllValue_ = (void*)vvalptr;\n\n";

    initCpp += "   pthread_mutex_init(&jsonWriterLock, NULL);\n\n";

    initCpp += "   // writing\n" + "   __GQUERY__local_writer->WriteStartArray();\n";

    initCpp += "}\n";

    return initCpp;
  }

  private String getDeclareCpp(String key) {
    String res = _setDeclare.get(key);

    if (res == null) {
      return "";
    } else {
      return res;
    }
  }

  private String getCpp(ParseTree ctx) {
    return _cppCode.get(ctx);
  }
}
