/**
 * ****************************************************************************
 * Copyright (c) 2016, TigerGraph Inc.
 * All rights reserved
 * Unauthorized copying of this file, via any medium is
 * strictly prohibited
 * Proprietary and confidential
 * ****************************************************************************
 */
package com.tigergraph.engine.codegen;

import com.tigergraph.engine.parser.GSQLParser;
import com.tigergraph.engine.parser.GSQLParserBaseListener;
import com.tigergraph.engine.typechecker.VarType;
import com.tigergraph.engine.util.*;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Stack;
import org.antlr.v4.runtime.ParserRuleContext;
import org.antlr.v4.runtime.tree.ParseTreeProperty;



/**
 * ******************************************************************************************* Gives
 * each statement a number, starting from 0. Used to distinguish among map and reduce functions
 * generated per statement.
 *
 * <p>When the query job is linear, this number coincides with the iteration number. This will no
 * longer be true when we go for non-linear jobs (e.g. we introduce loops). Which is why we do not
 * call this number the "iteration number", but the "program counter" (PC).
 *
 * <p>There are five kinds of statements:
 *
 * <p>- DML blocks : each block has a pc - print statement : each print has a pc - seed statement :
 * each seed statement has a pc - WHILE loop : while condition has a pc - IF-ELSE : IF condition has
 * a pc, ElseIF condition has a pc
 *
 * <p>**********************************************************************************************
 */
public class StatementCounter extends GSQLParserBaseListener {
  private ParserRuleContext jobCtx;
  private int _pc;

  private VarType _varType;

  private ParseTreeProperty<Integer> _statementCounters;

  public ParseTreeProperty<Integer> getStatementPCs() {
    return _statementCounters;
  }

  /*
   * "WHILE loops" and "if then else" need to know where to jump
   * upon breaking out or upon ending an iteration:
   */
  private Map<ParserRuleContext, ParserRuleContext> _gotos;

  public Map<ParserRuleContext, ParserRuleContext> getGoTos() {
    return _gotos;
  }

  /*
   * There are three places PC may jump.
   *
   * 1. when in IF-ElseIf-Else block, we may enter different scopes, and they will
   *    need to jump back to the next PC in the entering scope.
   *
   * 2. WHILE (condition) {} block, when it evaluates to FALSE, we need to
   *    jump to the next PC of at the end of WHILE block.
   *
   * 3. WHILE (condition) { } block, when the last statement finish, it needs to
   *    jump back to the beginning of WHILE.
   *
   * We use a stack to maintain the next statement to jump to depending on the
   * above scenarios.
   *
   */
  private Stack<ParserRuleContext> _nextStatement;

  private CommonVariable commonVariable;

  public StatementCounter(VarType varType, CommonVariable cv) {
    commonVariable = cv;
    _statementCounters = new ParseTreeProperty<Integer>();
    _gotos = new HashMap<ParserRuleContext, ParserRuleContext>();
    _pc = 0;
    _nextStatement = new Stack<ParserRuleContext>();
    _varType = varType;
  }

  //////////////////
  // listener rules
  /////////////////

  ////////////////////
  // enter statements
  ////////////////////

  // job
  @Override
  public void enterJob(GSQLParser.JobContext ctx) {
    // Push itself as the next statement for statements within body
    // Job is assigned as the last PC for the program.
    // In Switch (PC) control in BeforeIteration, we will call exit()
    // in the last PC. So, any one jump to here will notify system to
    // signal DONE.
    _nextStatement.push(ctx);
    jobCtx = ctx;
  }

  // Variable Declaration, add PC for it
  // when all declarations do not have any initialization, switch clause could be empty
  // if there are multiple initializations, they will share the same PC
  @Override
  public void enterVariableDeclaration(GSQLParser.VariableDeclarationContext ctx) {
    // For variable declaration statements, increment pc.
    _statementCounters.put(ctx, _pc++);
  }

  // Exception Declaration, add PC for it
  // when all declarations do not have any initialization, switch clause could be empty
  // if there are multiple initializations, they will share the same PC
  @Override
  public void enterExceptionDeclaration(GSQLParser.ExceptionDeclarationContext ctx) {
    // For variable declaration statements, increment pc.
    _statementCounters.put(ctx, _pc++);
  }

  // seed
  @Override
  public void enterStatementSeed(GSQLParser.StatementSeedContext ctx) {
    // For seed statements, increment pc.
    // In code generation, if the seed vt set is used directly by succeeding
    // statement, it will be compiled to set active flags in
    // the next statement's BeforeIteration
    _statementCounters.put(ctx, _pc++);
  }

  @Override
  public void enterStatementTopKVecSearchRval(GSQLParser.StatementTopKVecSearchRvalContext ctx) {
    _statementCounters.put(ctx, _pc++);
  }

  // block
  @Override
  public void enterStatementBlock(GSQLParser.StatementBlockContext ctx) {
    // block statement always have its own pc
    _statementCounters.put(ctx, _pc++);
  }

  // update vertex block
  @Override
  public void enterStatementUpdate(GSQLParser.StatementUpdateContext ctx) {
    // update vertex block statement always have its own pc
    _statementCounters.put(ctx, _pc++);
  }

  @Override
  public void enterStatementDelete(GSQLParser.StatementDeleteContext ctx) {
    // update vertex block statement always have its own pc
    _statementCounters.put(ctx, _pc++);
  }

  // insert block
  @Override
  public void enterStatementInsert(GSQLParser.StatementInsertContext ctx) {
    // insert statement always have its own pc
    _statementCounters.put(ctx, _pc++);
  }

  // delete var block
  @Override
  public void enterStatementDeleteVar(GSQLParser.StatementDeleteVarContext ctx) {
    // insert statement always have its own pc
    _statementCounters.put(ctx, _pc++);
  }

  // enter "try..exception.." block
  @Override
  public void enterStatementException(GSQLParser.StatementExceptionContext ctx) {
    if (!_isLastStatementInScope(ctx)) {
      _nextStatement.push(_getNextStatement(ctx));
    }

    //jump to caseExceptBlock if there exists a raised exception
    // GLE-2092: update Except to Exception
    _gotos.put(ctx, ctx.caseExceptionBlock().get(0));

    // assign PC to TRY clause
    _statementCounters.put(ctx, _pc++);

  }

  // exit "try..exception.." block
  @Override
  public void exitStatementException(GSQLParser.StatementExceptionContext ctx) {
    // GLE-2092: update Except to Exception
    int caseExceptionBlockNumber = ctx.caseExceptionBlock().size();
    // each "when..then" block jump to succeeding "when..then" block
    for (int i = 0; i < caseExceptionBlockNumber - 1; ++i) {
      _gotos.put(ctx.caseExceptionBlock().get(i), ctx.caseExceptionBlock().get(i + 1));
    }

    // GLE-2092: update Except to Exception
    GSQLParser.CaseExceptionBlockContext lastCaseExceptionBlock =
                             ctx.caseExceptionBlock().get(caseExceptionBlockNumber - 1);
    if (ctx.elseExceptionBlock() != null) {
      _gotos.put(lastCaseExceptionBlock, ctx.elseExceptionBlock());
    } else {
      //If the raised exception cannot be dealt with in this
      //"try..exception.." block. Jump to the correct outer
      //"try..exception.." block to deal with.
      // GLE-2092: update Except to Exception
      _gotos.put(lastCaseExceptionBlock, _getStatementExceptionContext(lastCaseExceptionBlock));
    }

    // Pop the statement pushed by enterStatementException
    if (!_isLastStatementInScope(ctx)) {
      _nextStatement.pop();
    }
  }

  // loop unified
  @Override
  public void enterStatementLoop(GSQLParser.StatementLoopContext ctx) {
    // If the "while" statement is not the last statement in scope,
    // push next sibling statement as the next statement for itself to
    // jump to when condition evaluate as false.
    if (!_isLastStatementInScope(ctx)) {
      _nextStatement.push(_getNextStatement(ctx));
    }

    // Need to jump to the next statement when condition evaluate as false
    _gotos.put(ctx, _nextStatement.peek());

    // Push itself as the next statement for statements within loop body
    _nextStatement.push(ctx);

    // assign PC to loop start
    _statementCounters.put(ctx, _pc++);
  }

  // foreach
  @Override
  public void enterStatementForeach(GSQLParser.StatementForeachContext ctx) {
    // If the "while" statement is not the last statement in scope,
    // push next sibling statement as the next statement for itself to
    // jump to when condition evaluate as false.
    if (!_isLastStatementInScope(ctx)) {
      _nextStatement.push(_getNextStatement(ctx));
    }

    // Need to jump to the next statement when condition evaluate as false
    _gotos.put(ctx, _nextStatement.peek());

    // Push itself as the next statement for statements within loop body
    _nextStatement.push(ctx);

    // assign PC to loop start
    _statementCounters.put(ctx, _pc++);
  }

  // case expr when constant
  @Override
  public void enterStatementCaseConst(GSQLParser.StatementCaseConstContext ctx) {
    // If the "case when" statement is not the last statement in scope,
    // push next sibling statement as the next statement within the
    // if body, since it's the moment to know the current scope's
    // next statement. However, if the "case when" statement is the last statemnt
    // in scope, we are sure the parent scope has pushed the next
    // statement in the stack.
    if (!_isLastStatementInScope(ctx)) {
      _nextStatement.push(_getNextStatement(ctx));
    }

    // assign PC to case when start
    _statementCounters.put(ctx, _pc++);
  }

  // when constant
  @Override
  public void enterCaseConstBlock(GSQLParser.CaseConstBlockContext ctx) {
    // assign PC to when start
    _statementCounters.put(ctx, _pc++);
  }

  // when..then.. in "EXCEPTION" block
  // update Except to Exception for GLE-2092
  @Override
  public void enterCaseExceptionBlock(GSQLParser.CaseExceptionBlockContext ctx) {
    // assign PC to "when..then.." block
    _statementCounters.put(ctx, _pc++);
  }

  // else in "EXCEPTION" block
  // update Except to Exception for GLE-2092
  @Override
  public void enterElseExceptionBlock(GSQLParser.ElseExceptionBlockContext ctx) {
    // assign an unique PC to else start.
    // It will be used to reset the raised exception,
    // so it cannot share the PC with other statement.
    _statementCounters.put(ctx, _pc++);
  }

  // case when condition
  @Override
  public void enterStatementCaseCond(GSQLParser.StatementCaseCondContext ctx) {
    // If the "case when" statement is not the last statement in scope,
    // push next sibling statement as the next statement within the
    // if body, since it's the moment to know the current scope's
    // next statement. However, if the "case when" statement is the last statemnt
    // in scope, we are sure the parent scope has pushed the next
    // statement in the stack.
    if (!_isLastStatementInScope(ctx)) {
      _nextStatement.push(_getNextStatement(ctx));
    }

    // assign PC to case when start
    _statementCounters.put(ctx, _pc++);
  }

  // when condition
  @Override
  public void enterCaseCondBlock(GSQLParser.CaseCondBlockContext ctx) {
    // assign PC to when start
    _statementCounters.put(ctx, _pc++);
  }

  // if
  @Override
  public void enterStatementIf(GSQLParser.StatementIfContext ctx) {
    // If the "if" statement is not the last statement in scope,
    // push next sibling statement as the next statement within the
    // if body, since it's the moment to know the current scope's
    // next statement. However, if the "if" statement is the last statemnt
    // in scope, we are sure the parent scope has pushed the next
    // statement in the stack.
    if (!_isLastStatementInScope(ctx)) {
      _nextStatement.push(_getNextStatement(ctx));
    }

    // assign PC to IF start
    _statementCounters.put(ctx, _pc++);
  }

  // else if
  @Override
  public void enterElseIfBlock(GSQLParser.ElseIfBlockContext ctx) {
    // assign PC to ELSE-IF start
    _statementCounters.put(ctx, _pc++);
  }

  // else
  @Override
  public void enterElseBlock(GSQLParser.ElseBlockContext ctx) {
    // assign PC to else start
    _statementCounters.put(ctx, _pc);
  }

  // attribute assign
  @Override
  public void enterStatementAssignAttr(GSQLParser.StatementAssignAttrContext ctx) {
    _statementCounters.put(ctx, _pc++);
  }

  // global accum assign
  @Override
  public void enterStatementAssignGlobalAccum(GSQLParser.StatementAssignGlobalAccumContext ctx) {
    // assign PC to assign statement
    _statementCounters.put(ctx, _pc++);
  }

  // reduce
  @Override
  public void enterStatementReduceGlobalAccum(GSQLParser.StatementReduceGlobalAccumContext ctx) {
    // assign PC to reduce statement
    _statementCounters.put(ctx, _pc++);
  }

  // continue
  @Override
  public void enterStatementContinue(GSQLParser.StatementContinueContext ctx) {
    // continue statement always have its own pc
    _statementCounters.put(ctx, _pc++);
  }

  // break
  @Override
  public void enterStatementBreak(GSQLParser.StatementBreakContext ctx) {
    // break statement always have its own pc
    _statementCounters.put(ctx, _pc++);
  }

  // gacc function
  @Override
  public void exitStatementExprFunc(GSQLParser.StatementExprFuncContext ctx) {
    // The last statement in the scope need jump.
    if (_isLastStatementInScope(ctx)) {
      _gotos.put(ctx, _nextStatement.peek());
    }
    // gacc has its own PC
    _statementCounters.put(ctx, _pc++);

    // The last statement in the scope need jump.
    if (_isLastStatementInScope(ctx)) {
      _gotos.put(ctx, _nextStatement.peek());
    }
  }

  // log
  @Override
  public void exitStatementLog(GSQLParser.StatementLogContext ctx) {
    _statementCounters.put(ctx, _pc++);

    // The last statement in the scope need jump.
    if (_isLastStatementInScope(ctx)) {
      _gotos.put(ctx, _nextStatement.peek());
    }
  }

  // expr function
  @Override
  public void exitStatementFuncRval(GSQLParser.StatementFuncRvalContext ctx) {
    _statementCounters.put(ctx, _pc++);

    // The last statement in the scope need jump.
    if (_isLastStatementInScope(ctx)) {
      _gotos.put(ctx, _nextStatement.peek());
    }
  }

  // load accum
  @Override
  public void exitStatementLoadAccum(GSQLParser.StatementLoadAccumContext ctx) {
    // assign PC to reduce statement
    _statementCounters.put(ctx, _pc++);

    // The last statement in the scope need jump.
    if (_isLastStatementInScope(ctx)) {
      _gotos.put(ctx, _nextStatement.peek());
    }
  }

  // table project vset
  @Override
  public void exitStatementProjectVSet(GSQLParser.StatementProjectVSetContext ctx) {
    _statementCounters.put(ctx, _pc++);

    // The last statement in the scope need jump.
    if (_isLastStatementInScope(ctx)) {
      _gotos.put(ctx, _nextStatement.peek());
    }
  }

  ////////////////////
  // exit statements
  ////////////////////

  // job
  @Override
  public void exitJob(GSQLParser.JobContext ctx) {
    // Assign PC to job
    _statementCounters.put(ctx, _pc);

    // pop the job from stack
    _nextStatement.pop();
  }

  // variable declaration
  @Override
  public void exitVariableDeclaration(GSQLParser.VariableDeclarationContext ctx) {
    // The last statement in the scope need jump.
    if (_isLastStatementInScope(ctx)) {
      _gotos.put(ctx, _nextStatement.peek());
    }
  }

  // exception declaration
  @Override
  public void exitExceptionDeclaration(GSQLParser.ExceptionDeclarationContext ctx) {
    // The last statement in the scope need jump.
    if (_isLastStatementInScope(ctx)) {
      _gotos.put(ctx, _nextStatement.peek());
    }
  }

  // seed
  @Override
  public void exitStatementSeed(GSQLParser.StatementSeedContext ctx) {
    // The last statement in the scope need jump.
    if (_isLastStatementInScope(ctx)) {
      _gotos.put(ctx, _nextStatement.peek());
    }
  }

  @Override
  public void exitStatementTopKVecSearchRval(GSQLParser.StatementTopKVecSearchRvalContext ctx) {
    // The last statement in the scope need jump.
    if (_isLastStatementInScope(ctx)) {
      _gotos.put(ctx, _nextStatement.peek());
    }
  }

  // print
  @Override
  public void exitStatementPrint(GSQLParser.StatementPrintContext ctx) {
    // The last statement in the scope need jump.
    if (_isLastStatementInScope(ctx)) {
      _gotos.put(ctx, _nextStatement.peek());
    }

    // Set the PC of the statement. Merge consecutive print statements
    // with vSetVar access into same pc.
    Integer pc = Integer.valueOf(_pc++);

    _statementCounters.put(ctx, pc);
  }

  // block
  @Override
  public void exitStatementBlock(GSQLParser.StatementBlockContext ctx) {
    // The last statement in the scope need jump.
    if (_isLastStatementInScope(ctx)) {
      _gotos.put(ctx, _nextStatement.peek());
    }
  }

  // update vertex block
  @Override
  public void exitStatementUpdate(GSQLParser.StatementUpdateContext ctx) {
    // The last statement in the scope need jump.
    if (_isLastStatementInScope(ctx)) {
      _gotos.put(ctx, _nextStatement.peek());
    }
  }

  // delete vertex block
  @Override
  public void exitStatementDelete(GSQLParser.StatementDeleteContext ctx) {
    // The last statement in the scope need jump.
    if (_isLastStatementInScope(ctx)) {
      _gotos.put(ctx, _nextStatement.peek());
    }
  }

  // insert statement
  @Override
  public void exitStatementInsert(GSQLParser.StatementInsertContext ctx) {
    // The last statement in the scope need jump.
    if (_isLastStatementInScope(ctx)) {
      _gotos.put(ctx, _nextStatement.peek());
    }
  }

  // delete var statement
  @Override
  public void exitStatementDeleteVar(GSQLParser.StatementDeleteVarContext ctx) {
    // The last statement in the scope need jump.
    if (_isLastStatementInScope(ctx)) {
      _gotos.put(ctx, _nextStatement.peek());
    }
  }

  // raise statement
  @Override
  public void exitStatementRaise(GSQLParser.StatementRaiseContext ctx) {
    _statementCounters.put(ctx, _pc++);
    //jump to the correct "try..exception.." block that
    //can deal with the exception. Perhaps it isn't in the
    //current "try..exception.." block.
    _gotos.put(ctx, _getStatementExceptionContext(ctx));
  }

  /*
   * WHILE condition
   *
   * statement //m
   *
   * When the while condition evalutes to false, we do
   *
   * 1. pop n from the stack.
   * 2. put n->m mapping to the goto map.
   * 3. pop m from the stack if it's not the last statement of current scope.
   *
   */

  //unified while loop
  @Override
  public void exitStatementLoop(GSQLParser.StatementLoopContext ctx) {
    // Pop the next statement from the stack because statements
    // within loop body has all already been processed.
    _nextStatement.pop();

    // Pop the statement pushed by this while statement
    if (!_isLastStatementInScope(ctx)) {
      _nextStatement.pop();
    }
  }

  @Override
  public void exitStatementForeach(GSQLParser.StatementForeachContext ctx) {
    // Pop the next statement from the stack because statements
    // within loop body has all already been processed.
    _nextStatement.pop();

    // Pop the statement pushed by this while statement
    if (!_isLastStatementInScope(ctx)) {
      _nextStatement.pop();
    }
  }

  @Override
  public void exitStatementLVarAssignExpr(GSQLParser.StatementLVarAssignExprContext ctx) {
    // The last statement in the scope need jump.
    if (_isLastStatementInScope(ctx)) {
      _gotos.put(ctx, _nextStatement.peek());
    }

    // assign PC to assign global var
    _statementCounters.put(ctx, _pc++);
  }

  /*
   * CASE WHEN condition  statement flow control:
   * Case 1.
   *   CASE WHEN condition THEN         // n
   *              statement
   *   WHEN condition THEN   // m
   *
   *   END;
   *
   * Case 2.
   *   CASE WHEN condition THEN        // n
   *
   *   ELSE
   *     statement              // m
   *   END;
   *
   * Case 3.
   *   CASE WHEN condition THEN          // n
   *
   *   END;
   *   statement                // m
   *
   * - put n->m mapping to the goto map
   *
   * WHEN statement flow control:
   * Case 1.
   *   ...
   *    WHEN condition THEN  // n
   *
   *    WHEN condition THEN  // m
   *
   *   END;
   *
   * Case 2.
   *   ...
   *   WHEN condition THEN  // n
   *
   *   ELSE                 // m
   *
   *   END;
   *
   * Case 3.
   *   ...
   *   WHEN condition THEN // n
   *
   *   END;
   *   statement               // m
   *
   * - put n->m mapping to the goto map
   *
   * Finally, pop top of the stack if CASE WHEN statement
   * is not the last statement of current scope.
   */

  // case expr when constant
  @Override
  public void exitStatementCaseConst(GSQLParser.StatementCaseConstContext ctx) {
    // Set "case when" statement's next statement when condition evaluated as false
    if (ctx.caseConstBlock() != null && ctx.caseConstBlock().size() > 0) { // goto second when
      _gotos.put(ctx, ctx.caseConstBlock().get(0));
    } else if (ctx.elseBlock() != null) { // goto else
      _gotos.put(ctx, ctx.elseBlock());
    } else { // goto next statement
      _gotos.put(ctx, _nextStatement.peek());
    }

    // Set "when condition" statements' next statement when condition is false
    if (ctx.caseConstBlock() != null && ctx.caseConstBlock().size() > 0) {
      int caseConstNumber = ctx.caseConstBlock().size();
      // each "when condition" statement jump to succeeding "when condition" block
      for (int i = 0; i < caseConstNumber - 1; ++i) {
        _gotos.put(ctx.caseConstBlock().get(i), ctx.caseConstBlock().get(i + 1));
      }
      // last "when condition" block jump to "else" block (if exist), or next statement
      GSQLParser.CaseConstBlockContext lastCaseConst
          = ctx.caseConstBlock().get(caseConstNumber - 1);
      if (ctx.elseBlock() != null) {
        _gotos.put(lastCaseConst, ctx.elseBlock());
      } else {
        _gotos.put(lastCaseConst, _nextStatement.peek());
      }
    }

    // If the "if" statement is not the last statement in the scope,
    // pop the top statement out from stack.
    if (!_isLastStatementInScope(ctx)) {
      _nextStatement.pop();
    }
  }

  // case when condition
  @Override
  public void exitStatementCaseCond(GSQLParser.StatementCaseCondContext ctx) {
    // Set "case when" statement's next statement when condition evaluated as false
    if (ctx.caseCondBlock() != null && ctx.caseCondBlock().size() > 0) { // goto second when
      _gotos.put(ctx, ctx.caseCondBlock().get(0));
    } else if (ctx.elseBlock() != null) { // goto else
      _gotos.put(ctx, ctx.elseBlock());
    } else { // goto next statement
      _gotos.put(ctx, _nextStatement.peek());
    }

    // Set "when condition" statements' next statement when condition is false
    if (ctx.caseCondBlock() != null && ctx.caseCondBlock().size() > 0) {
      int caseCondNumber = ctx.caseCondBlock().size();
      // each "when condition" statement jump to succeeding "when condition" block
      for (int i = 0; i < caseCondNumber - 1; ++i) {
        _gotos.put(ctx.caseCondBlock().get(i), ctx.caseCondBlock().get(i + 1));
      }
      // last "when condition" block jump to "else" block (if exist), or next statement
      GSQLParser.CaseCondBlockContext lastCaseCond = ctx.caseCondBlock().get(caseCondNumber - 1);
      if (ctx.elseBlock() != null) {
        _gotos.put(lastCaseCond, ctx.elseBlock());
      } else {
        _gotos.put(lastCaseCond, _nextStatement.peek());
      }
    }

    // If the "if" statement is not the last statement in the scope,
    // pop the top statement out from stack.
    if (!_isLastStatementInScope(ctx)) {
      _nextStatement.pop();
    }
  }
  /*
   * IF statement flow control:
   * Case 1.
   *   IF condition THEN         // n
   *
   *   ELSE IF condition THEN  // m
   *
   *   END;
   *
   * Case 2.
   *   IF condition THEN         // n
   *
   *   ELSE
   *     statement              // m
   *   END;
   *
   * Case 3.
   *   IF condition THEN         // n
   *
   *   END;
   *   statement                // m
   *
   * - put n->m mapping to the goto map
   *
   * ELSE-IF statement flow control:
   * Case 1.
   *   ...
   *   ELSE IF condition THEN  // n
   *
   *   ELSE IF condition THEN // m
   *
   *   END;
   *
   * Case 2.
   *   ...
   *   ELSE IF condition THEN  // n
   *
   *   ELSE                  // m
   *
   *   END;
   *
   * Case 3.
   *   ...
   *   ELSE IF condition THEN  // n
   *
   *   END;
   *   statement               // m
   *
   * - put n->m mapping to the goto map
   *
   * Finally, pop top of the stack if IF-ELSE statement
   * is not the last statement of current scope.
   */
  @Override
  public void exitStatementIf(GSQLParser.StatementIfContext ctx) {
    // Set "if" statement's next statement when condition evaluated as false
    if (ctx.elseIfBlock() != null && ctx.elseIfBlock().size() > 0) { // goto first else-if
      _gotos.put(ctx, ctx.elseIfBlock().get(0));
    } else if (ctx.elseBlock() != null) { // goto else
      _gotos.put(ctx, ctx.elseBlock());
    } else { // goto next statement
      _gotos.put(ctx, _nextStatement.peek());
    }

    // Set "else-if" statements' next statement when condition is false
    if (ctx.elseIfBlock() != null && ctx.elseIfBlock().size() > 0) {
      int elseIfNumber = ctx.elseIfBlock().size();
      // each "else-if" statement jump to succeeding "else-if" block
      for (int i = 0; i < elseIfNumber - 1; ++i) {
        _gotos.put(ctx.elseIfBlock().get(i), ctx.elseIfBlock().get(i + 1));
      }
      // last "else-if" block jump to "else" block (if exist), or next statement
      GSQLParser.ElseIfBlockContext lastElseIf = ctx.elseIfBlock().get(elseIfNumber - 1);
      if (ctx.elseBlock() != null) {
        _gotos.put(lastElseIf, ctx.elseBlock());
      } else {
        _gotos.put(lastElseIf, _nextStatement.peek());
      }
    }

    // If the "if" statement is not the last statement in the scope,
    // pop the top statement out from stack.
    if (!_isLastStatementInScope(ctx)) {
      _nextStatement.pop();
    }
  }

  // attribute assign
  @Override
  public void exitStatementAssignAttr(GSQLParser.StatementAssignAttrContext ctx) {
    // The last statement in the scope need jump.
    if (_isLastStatementInScope(ctx)) {
      _gotos.put(ctx, _nextStatement.peek());
    }
  }

  // global accum assign
  @Override
  public void exitStatementAssignGlobalAccum(GSQLParser.StatementAssignGlobalAccumContext ctx) {
    // The last statement in the scope need jump.
    if (_isLastStatementInScope(ctx)) {
      _gotos.put(ctx, _nextStatement.peek());
    }
  }

  // reduce
  @Override
  public void exitStatementReduceGlobalAccum(GSQLParser.StatementReduceGlobalAccumContext ctx) {
    // The last statement in the scope need jump.
    if (_isLastStatementInScope(ctx)) {
      _gotos.put(ctx, _nextStatement.peek());
    }
  }

  @Override
  public void exitStatementReturn(GSQLParser.StatementReturnContext ctx) {
    _statementCounters.put(ctx, _pc++);
    // The last statement in the scope need jump.
    if (_isLastStatementInScope(ctx)) {
      _gotos.put(ctx, _nextStatement.peek());
    }
  }

  /**
   * WHILE (condition) { // n ... continue; // m ... }
   *
   * <p>- put m->n mapping to the goto map
   */
  @Override
  public void exitStatementContinue(GSQLParser.StatementContinueContext ctx) {
    // The continue statement should jump to the
    // first ancient WHILE statement
    _gotos.put(ctx, _getFirstAncientLoop(ctx));
  }

  /**
   * WHILE (condition) { ... break; // m ... } statement // n
   *
   * <p>- put m->n mapping to the goto map
   */
  @Override
  public void exitStatementBreak(GSQLParser.StatementBreakContext ctx) {
    // The break statement should jump to the
    // first ancient WHILE statement's next statement
    _gotos.put(ctx, _gotos.get(_getFirstAncientLoop(ctx)));
  }

  /**
   * ************************************************************************** Helper functions *
   * **************************************************************************
   */

  /**
   * Judge if the statement is the last statement in the scope Example: s1 false s2 { false s3 false
   * s4 true } s5 { true s6 false s7 { true s8 false s9 true } }
   *
   * @param ctx the statement to be judged
   */
  private boolean _isLastStatementInScope(GSQLParser.StatementContext ctx) {

    List<GSQLParser.StatementContext> statements =
        ((GSQLParser.StatementsContext) ctx.getParent()).statement();

    return ctx == statements.get(statements.size() - 1);
  }

  /**
   * Return the next sibling statement of a given statement in the same scope Return null if the
   * given statement is the last statement in the scope
   */
  private GSQLParser.StatementContext _getNextStatement(GSQLParser.StatementContext ctx) {

    if (_isLastStatementInScope(ctx)) {
      return null;
    }

    List<GSQLParser.StatementContext> statements =
        ((GSQLParser.StatementsContext) ctx.getParent()).statement();

    for (int i = 0; i < statements.size() - 1; ++i) {
      if (statements.get(i) == ctx) {
        return statements.get(i + 1);
      }
    }

    Util.error(
        ctx.getStart(),
        "Didn't find sibling statement of " + Util.getRuleText(ctx, Util.EXPR_DUMP_LIMIT),
        0,
        Util.ErrorType.SYSTEM_ERROR, commonVariable);

    return null;
  }

  /**
   * Get the first ancient WHILE statement of a CONTINUE or BREAK statement. Semantic error if
   * it is not nested in any WHILE statement.
   */
  private ParserRuleContext _getFirstAncientLoop(ParserRuleContext ctx) {
    ParserRuleContext ctxCopy = ctx;

    while (!(ctxCopy instanceof GSQLParser.JobContext)) {
      if (ctxCopy instanceof GSQLParser.StatementLoopContext
          || ctxCopy instanceof GSQLParser.StatementForeachContext) {
        return ctxCopy;
      }
      ctxCopy = ctxCopy.getParent();
    }

    Util.error(
        ctx.getStart(),
        "'" + ctx.getText() + "' is not in WHILE statement nor in FOREACH statement.",
        100,
        Util.ErrorType.SEMANTIC_ERROR, commonVariable);

    return null;
  }

  /**
   * In StatementException, this function is used to find the right "TRY" clause
   * where should be jumped if an exception is raised.
   *
   * We use the following sampe code to describe the algorithm:
   * ////////////////////code start//////////////////////
   * TRY                            // TRY 3 (unique number)
   *   statements;
   *   TRY                          // TRY 2 (unique number)
   *     statements;
   *   EXCEPTION
   *     WHEN .. THEN               // WHEN_THEN 2
   *       TRY                      // TRY 1 (unique number)
   *         raise exception1;      // exception1
   *       EXCEPTION
   *         WHEN .. THEN           // WHEN_THEN 1
   *           raise exception2;    // exception2
   *       END;
   *   END;
   * EXCEPTION
   *   ...
   * END;
   * ////////////////////code end//////////////////////
   * The above code contains 3 "try exception" blocks. We assgin an unique
   * number for each of them from outside to inside, see the right comments.
   * Before the algorithm start to run, we will set "level" to 1.
   *
   * 1) If exception1 is raised, the exception should be dealt with in "TRY 1" try
   * exception block. At the begining, level = 1, ctx = exception1.
   * Steps:
   *   1. The parent of "ctx" is StatementException =>
   *                   level = level - 1 = 0, ctx = "TRY 1";
   *   2. level = 0, find the "TRY" clause it should jump => "TRY 1"
   *
   * 2) If exception2 is raised, the exception should be dealt with in "TRY 3" try
   * exception block. At the begining, level = 1, ctx = exception2.
   * Steps:
   *   1. The parent of "ctx" is CaseExceptBlock =>
   *                  level = level + 1 = 2, ctx = "WHEN_THNE 1";
   *   2. The parent of "ctx" is StatementException =>
   *                  level = level - 1 = 1, ctx = "TRY 1";
   *   3. The parent of "ctx" is CaseExceptBlock =>
   *                  level = level + 1 = 2, ctx = "WHEN_THNE 2";
   *   4. The parent of "ctx" is StatementException =>
   *                  level = level - 1 = 1, ctx = "TRY 2";
   *   5. The parent of "ctx" is StatementException =>
   *                  level = level - 1 = 0, ctx = "TRY 3";
   *   6. level = 0, find the "TRY" clause it should jump => "TRY 3"
   */
  private ParserRuleContext _getStatementExceptionContext(ParserRuleContext ctx) {
    int level = 1;
    while (ctx != null) {
      if (ctx instanceof GSQLParser.StatementExceptionContext) {
        --level;
      }
      // GLE-2092: update Except to Exception
      if (ctx instanceof GSQLParser.CaseExceptionBlockContext
          || ctx instanceof GSQLParser.ElseExceptionBlockContext) {
        ++level;
      }
      if (level == 0) {
        return ctx;
      }
      ctx = ctx.getParent();
    }
    // JobCtx:
    //   Raised exception cannot be dealt with.
    //   Jump to last PC and then abort.
    return jobCtx;
  }
}
