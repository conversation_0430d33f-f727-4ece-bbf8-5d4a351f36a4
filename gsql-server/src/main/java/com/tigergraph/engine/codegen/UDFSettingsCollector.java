/**
 * ****************************************************************************
 * Copyright (c) 2016, TigerGraph Inc.
 * All rights reserved
 * Unauthorized copying of this file, via any medium is
 * strictly prohibited
 * Proprietary and confidential
 * ****************************************************************************
 */
package com.tigergraph.engine.codegen;

import com.tigergraph.engine.parser.GSQLParserBaseListener;
import com.tigergraph.engine.parser.GSQLParser;
import com.tigergraph.engine.parser.GSQLParser.StatementPrintContext;
import com.tigergraph.engine.parser.GSQLParser.VSetProjContext;
import com.tigergraph.engine.parser.GSQLParser.AllTupleFieldAccessContext;
import com.tigergraph.engine.typechecker.VarType;
import com.tigergraph.engine.util.*;
import org.antlr.v4.runtime.ParserRuleContext;
import org.antlr.v4.runtime.tree.ParseTreeProperty;
import com.tigergraph.common.JsonAPIVersion;

/**
 * Collect UDFSettings.
 * If src_attr is needed in ACCUM clause, set EdgeMapSetting to 1. .....
 *
 * Currently three kinds of statement enter MapReduce
 * - Query Block (StatementBlock)
 * - Print Statement (StatementPrint)
 * - Update Block (updateBlock)
 */
public class UDFSettingsCollector extends GSQLParserBaseListener {

  private ParserRuleContext blockCtx;
  private ParseTreeProperty<UDFSettings> _settings;

  public ParseTreeProperty<UDFSettings> getSettings() {
    return _settings;
  }

  private VarType _varType;

  private CommonVariable commonVariable;

  private boolean _skipSetValue = false;

  public UDFSettingsCollector(VarType varType, CommonVariable cv) {
    commonVariable = cv;
    _varType = varType;
    _settings = new ParseTreeProperty<UDFSettings>();
  }

  //////////////////
  // listener rules
  /////////////////
  @Override
  public void enterStatementBlock(GSQLParser.StatementBlockContext ctx) {
    blockCtx = ctx;
    _settings.put(blockCtx, new UDFSettings());
  }

  @Override
  public void exitStatementBlock(GSQLParser.StatementBlockContext ctx) {
    blockCtx = null;
  }

  @Override
  public void enterStatementPrint(GSQLParser.StatementPrintContext ctx) {
    blockCtx = ctx;
    _settings.put(blockCtx, new UDFSettings());
  }

  @Override
  public void exitStatementPrint(GSQLParser.StatementPrintContext ctx) {
    blockCtx = null;
  }

  @Override
  public void enterStatementUpdate(GSQLParser.StatementUpdateContext ctx) {
    blockCtx = ctx;
    _settings.put(blockCtx, new UDFSettings());

    // graph update always need v_attr in Reduce
    _settings.get(blockCtx).ReduceSetting |= UDFSettings.VAttrRequired;
  }

  @Override
  public void enterStatementDelete(GSQLParser.StatementDeleteContext ctx) {
    blockCtx = ctx;
    _settings.put(blockCtx, new UDFSettings());

    // graph update always need v_attr in Reduce
    _settings.get(blockCtx).ReduceSetting |= UDFSettings.VAttrRequired;
  }

  @Override
  public void exitStatementUpdate(GSQLParser.StatementUpdateContext ctx) {
    blockCtx = null;
  }

  @Override
  public void exitStatementDelete(GSQLParser.StatementDeleteContext ctx) {
    blockCtx = null;
  }

  @Override
  public void exitAttrLval(GSQLParser.AttrLvalContext ctx) {
    // skip setting in query-body level
    if (Util.InBlock(ctx)) {
      // if can be attribute only
      // this is in Reduce only
      SetValue(UDFSettings.VAttrRequired, ctx);
    }
  }

  @Override
  public void exitAllTupleFieldAccess(GSQLParser.AllTupleFieldAccessContext ctx) {
    // check valid attribute
    SetValue(ctx.exprAtom().getText(), ctx);
  }

  @Override
  public void enterNonLogicalExpr(GSQLParser.NonLogicalExprContext ctx) {
    // if NonLogicalExpr is in KvRvalCtx's key, then set the flag to not setValue for udfsetting
    if (ctx.getParent() instanceof GSQLParser.KvRvalContext) {
      GSQLParser.KvRvalContext kvCtx = (GSQLParser.KvRvalContext) ctx.getParent();
      for (GSQLParser.ExprContext exprCtx : kvCtx.keys) {
        if (exprCtx == ctx) {
          _skipSetValue = true;
          break;
        } 
      }
    }
  }

  @Override
  public void exitNonLogicalExpr(GSQLParser.NonLogicalExprContext ctx) {
    // check valid attribute only if this NonLogicalExpr is not skipped 
    // For now, that is if this NonLogicalExpr is an exprAtom under key of
    // kvRval context.
    if (!_skipSetValue) {
      SetValue(ctx.exprAtom().getText(), ctx);
    }
    // if current NonLogicalExpr has parent kvRval and it is in keys
    if (ctx.getParent() instanceof GSQLParser.KvRvalContext) {
      GSQLParser.KvRvalContext kvCtx = (GSQLParser.KvRvalContext) ctx.getParent();
      for (GSQLParser.ExprContext exprCtx : kvCtx.keys) {
        if (exprCtx == ctx) {
          _skipSetValue = false;
          break;
        }
      }
    }
  }

  @Override
  public void exitVAccLval(GSQLParser.VAccLvalContext ctx) {
    String norm = _varType.getNormalizeName(ctx, ctx.var().getText()).getName();
    boolean isSrc = norm != null && norm.equals("src");

    // v_val is needed in reduce, since the delta is not empty
    _settings.get(blockCtx).ReduceSetting |= UDFSettings.VValueRequired;

    if (Util.InVertexMap(ctx) || Util.InPrint(ctx) || Util.InEdgeMap(ctx) && isSrc) return;
    if (Util.InReduce(ctx)) {
      SetValue(UDFSettings.VValueRequired, ctx);
    }
  }

  @Override
  public void exitVAccRval(GSQLParser.VAccRvalContext ctx) {
    String norm = _varType.getNormalizeName(ctx, ctx.var().getText()).getName();
    boolean isSrc = norm != null && norm.equals("src");

    if (Util.InVertexMap(ctx) || Util.InPrint(ctx) || Util.InEdgeMap(ctx) && isSrc) {
      return;
    }
    if (Util.InReduce(ctx)) {
      SetValue(UDFSettings.VValueRequired, ctx);
    } else {
      SetValue(UDFSettings.TgtValueRequired, ctx);
    }
  }

  @Override
  public void exitVAccPrevRval(GSQLParser.VAccPrevRvalContext ctx) {
    SetValue(UDFSettings.VValueRequired, ctx);
  }

  @Override
  public void exitExprRhsIdent(GSQLParser.ExprRhsIdentContext ctx) {
    // print vSet case
    if (Util.InPrint(ctx) && _varType.IsVSetVar(ctx.getText())
        && ctx.parent.parent != null
        && ctx.parent.parent.parent instanceof StatementPrintContext) {
      SetValue(UDFSettings.VAttrRequired, ctx);
      SetValue(UDFSettings.VValueRequired, ctx); //unncessary?
    }
  }

  @Override
  public void exitFuncRval(GSQLParser.FuncRvalContext ctx) {
    String funcName = ctx.funcName().getText();

    // For evaluate, we need to use attribute and v_value
    if (funcName.toLowerCase().equals("evaluate")) {
      // set all values
      SetValue(UDFSettings.VAttrRequired, ctx); //1
      SetValue(UDFSettings.VValueRequired, ctx); //2
      SetValue(UDFSettings.TgtValueRequired, ctx); //4
    }
  }

  @Override
  public void exitAllFuncAccess(GSQLParser.AllFuncAccessContext ctx) {
    String funcName = ctx.accFuncRval().funcName().getText();

    if (funcName.toLowerCase().equals("outdegree")) {
      SetValue(ctx.exprAtom().getText(), ctx);
    } else if (funcName.toLowerCase().equals("getattr")) {
      SetValue(ctx.exprAtom().getText(), ctx);
    } else if (funcName.toLowerCase().equals("gettags")) {
      SetValue(ctx.exprAtom().getText(), ctx);
    }
  }

  @Override
  public void exitPrintExpr(GSQLParser.PrintExprContext ctx) {
    //set 'set_udfprintsetting' value for each expression in PRINT clause
    if (commonVariable.JsonAPI == JsonAPIVersion.V2) {
      ParserRuleContext tmpBlockCtx = blockCtx;

      blockCtx = ctx;
      _settings.put(blockCtx, new UDFSettings());

      if (ctx.vSetProj().size() > 0) {
        for (VSetProjContext vSetProj : ctx.vSetProj()) {
          if (Util.HasVSetChild(vSetProj, _varType, AllTupleFieldAccessContext.class)
              || Util.hasVsetSupportedFunctionInPrintStmt(ctx)) {
            // attribute
            SetValue(UDFSettings.VAttrRequired, ctx);
            break;
          }
        }
      } else if (_varType.IsVSetVar(Util.stripExtraParens(ctx.expr()).getText())) {
        SetValue(UDFSettings.VAttrRequired, ctx);
      }

      blockCtx = tmpBlockCtx;
    }
  }

  /** **** helper function ********* */

  /**
   * set the settings based on flag.
   *
   * @param val one of the enum in UDFSettings
   */
  private void SetValue(int val, ParserRuleContext ctx) {
    if (Util.InVertexMap(ctx)) {
      _settings.get(blockCtx).VertexMapSetting |= val;
    } else if (Util.InEdgeMap(ctx)) {
      _settings.get(blockCtx).EdgeMapSetting |= val;
    } else if (Util.InReduce(ctx)) {
      _settings.get(blockCtx).ReduceSetting |= val;
    } else if (Util.InPrint(ctx)) {
      _settings.get(blockCtx).PrintSetting |= val;
    }
  }

  /** set settings based on var is src tgt or vt */
  private void SetValue(String var, ParserRuleContext ctx) {
    int at = _varType.GetVarType(var, ctx);

    switch (at) {
      case VarType.TGT:
        if (Util.InEdgeMap(ctx)) {
          SetValue(UDFSettings.TgtAttrRequired, ctx);
          break;
        }
      case VarType.VT: // can be tgt or src
      {
        String norm = _varType.getNormalizeName(ctx, var).getName();
        if (Util.InEdgeMap(ctx) && norm != null && norm.equals("tgt")) {
          SetValue(UDFSettings.TgtAttrRequired, ctx);
          break;
        }
      }
      case VarType.SRC: //src in vertex or edge map
      case VarType.VSET_VAR: //in print
        // src and v (reduce and print) are same value
        SetValue(UDFSettings.SrcAttrRequired, ctx);
        break;
      default:
        break;
    }
  }
}
