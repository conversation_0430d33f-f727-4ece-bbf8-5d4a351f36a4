/**
 * ****************************************************************************
 * Copyright (c) 2024, TigerGraph Inc.
 * All rights reserved
 * Unauthorized copying of this file, via any medium is
 * strictly prohibited
 * Proprietary and confidential
 * ****************************************************************************
 */
package com.tigergraph.engine.codegen;

import com.tigergraph.engine.util.CodeBuilder;
import com.tigergraph.schema.topology.*;
import static com.tigergraph.utility.CodeGenUtil.CPP_SEPARATOR;

import java.security.InvalidParameterException;
import java.util.*;
import java.util.stream.Collectors;

public class SchemaIndex {
  private Graph schema;
  private Set<String> vertexTypes;
  private Set<String> edgeTypes;
  private Map<String, Set<String>> vertexAttributes;
  private Map<String, Set<String>> edgeAttributes;

  protected SchemaIndex(Graph graph) {
    schema = graph;
    vertexTypes = new HashSet<>();
    edgeTypes = new HashSet<>();
    vertexAttributes = new HashMap<>();
    edgeAttributes = new HashMap<>();
  }

  protected String addVertexTypeIndex(String typeName) {
    if (schema.hasVertex(typeName)) vertexTypes.add(typeName);
    return vertexTypeIndex(typeName);
  }

  private String vertexTypeIndex(String typeName) {
    return String.format("_schema_VTY_%s", typeName);
  }

  protected String addEdgeTypeIndex(String typeName) {
    if (schema.hasEdge(typeName)) edgeTypes.add(typeName);
    return edgeTypeIndex(typeName);
  }

  private String edgeTypeIndex(String typeName) {
    return String.format("_schema_ETY_%s", typeName);
  }

  protected String addVertexAttributeIndex(String typeName, String attrName) {
    if (schema.hasVertexAttribute(typeName, attrName)) {
      Set<String> attributes = vertexAttributes.getOrDefault(typeName, new HashSet<>());
      attributes.add(attrName);
      vertexAttributes.put(typeName, attributes);
    }
    if (schema.hasVertexEmbeddingAttribute(typeName,attrName)) {
      throw new InvalidParameterException(
          String.format("adding vector attr into vertex attr index is not allowed"));
    }
    return vertexAttributeIndex(typeName, attrName);
  }

  private String vertexAttributeIndex(String typeName, String attrName) {
    return String.format("_schema_VATT_%s%s%s", typeName, CPP_SEPARATOR, attrName);
  }

  protected String addEdgeAttributeIndex(String typeName, String attrName) {
    if (schema.hasEdgeAttribute(typeName, attrName)) {
      Set<String> attributes = edgeAttributes.getOrDefault(typeName, new HashSet<>());
      attributes.add(attrName);
      edgeAttributes.put(typeName, attributes);
    }
    return edgeAttributeIndex(typeName, attrName);
  }

  private String edgeAttributeIndex(String typeName, String attrName) {
    return String.format("_schema_EATT_%s%s%s", typeName, CPP_SEPARATOR, attrName);
  }

  protected String getSchemaDeclaration() {
    CodeBuilder cpp = new CodeBuilder();
    // declare vertex/edge types as const int because the type ids are known in GSQL catalog
    generateVertexTypeIndices().forEach(cpp::append);
    generateEdgeTypeIndices().forEach(cpp::append);
    // declare vertex/edge attributes as int because the attr idx is in GPE TopologyMeta
    // TODO: GLE-5528 change to const int once we start tracking attribute index in GSQL catalog
    generateVertexAttributeIndices(true).forEach(cpp::append);
    generateEdgeAttributeIndices(true).forEach(cpp::append);
    return cpp.toString();
  }

  protected String getSchemaInitialization() {
    // TODO: GLE-5528 no longer necessary once we start tracking attribute index in GSQL catalog
    List<String> lines = new ArrayList<>();
    // initialize vertex attributes, ordered by vertex id and attribute index
    lines.addAll(generateVertexAttributeIndices(false));
    // initialize edge attributes, ordered by edge id and attribute index
    lines.addAll(generateEdgeAttributeIndices(false));
    // return empty string if there is no attribute access
    if (lines.isEmpty()) return "";
    CodeBuilder cpp = new CodeBuilder();
    cpp.append("topology4::TopologyMeta* meta = _serviceapi->GetTopologyMeta();");
    lines.forEach(cpp::append);
    return cpp.toString();
  }

  private List<String> generateVertexTypeIndices() {
    return schema.vertexTypes().stream()
        .filter(type -> vertexTypes.contains(type.Name))
        .map(this::declareTypeIndex)
        .collect(Collectors.toList());
  }

  private List<String> generateEdgeTypeIndices() {
    return schema.edgeTypes().stream()
        .filter(type -> edgeTypes.contains(type.Name))
        .map(this::declareTypeIndex)
        .collect(Collectors.toList());
  }

  /**
   * Generate cpp code for vertex attribute indices.
   * @param isDeclaration true for generating declaration code, false for initialization
   * @return list of strings, each one for a line of cpp code
   */
  private List<String> generateVertexAttributeIndices(boolean isDeclaration) {
    List<String> lines = new ArrayList<>();
    for (VertexType type : schema.vertexTypes()) {
      if (vertexAttributes.containsKey(type.Name)) {
        // need vertex attribute meta to obtain the attribute positions
        if (!isDeclaration) lines.add(initAttributeMeta(type));
        // go through each attribute, generate code for it and
        // add to lines if the attribute is in use
        type.Attributes.stream()
            .filter(attribute -> attributeInUse(type, attribute))
            .map(attribute -> attributeIndexCpp(type, attribute, isDeclaration))
            .forEach(lines::add);
      }
    }
    return lines;
  }

  private List<String> generateEdgeAttributeIndices(boolean isDeclaration) {
    List<String> lines = new ArrayList<>();
    for (EdgeType type : schema.edgeTypes()) {
      if (edgeAttributes.containsKey(type.Name)) {
        // need vertex attribute meta to obtain the attribute positions
        if (!isDeclaration) lines.add(initAttributeMeta(type));
        // go through each attribute , generate code for it and
        // add to lines if the attribute is in use
        type.Attributes.stream()
            .filter(attribute -> attributeInUse(type, attribute))
            .map(attribute -> attributeIndexCpp(type, attribute, isDeclaration))
            .forEach(lines::add);
      }
    }
    return lines;
  }

  private String typeIndexName(VertexType type) {
    return vertexTypeIndex(type.Name);
  }

  private String typeIndexName(EdgeType type) {
    return edgeTypeIndex(type.Name);
  }

  private String declareTypeIndex(VertexType type) {
    return String.format("const int %s = %d;", typeIndexName(type), type.VertexId);
  }

  private String declareTypeIndex(EdgeType type) {
    return String.format("const int %s = %d;", typeIndexName(type), type.EdgeId);
  }

  private String attributeMetaName(VertexType type) {
    return String.format("VTY_%s_attrMeta", type.Name);
  }

  private String attributeMetaName(EdgeType type) {
    return String.format("ETY_%s_attrMeta", type.Name);
  }

  private String initAttributeMeta(VertexType type) {
    return String.format("topology4::AttributesMeta& %s = meta->GetVertexType(%d).attributes_;",
        attributeMetaName(type), type.VertexId);
  }

  private String initAttributeMeta(EdgeType type) {
    return String.format("topology4::AttributesMeta& %s = meta->GetEdgeType(%d).attributes_;",
        attributeMetaName(type), type.EdgeId);
  }

  private boolean attributeInUse(VertexType type, Attribute attribute) {
    return vertexAttributes
        .getOrDefault(type.Name, new HashSet<>())
        .contains(attribute.AttributeName);
  }

  private boolean attributeInUse(EdgeType type, Attribute attribute) {
    return edgeAttributes
        .getOrDefault(type.Name, new HashSet<>())
        .contains(attribute.AttributeName);
  }

  private String attributeIndexName(VertexType type, Attribute attribute) {
    return vertexAttributeIndex(type.Name, attribute.AttributeName);
  }

  private String attributeIndexName(EdgeType type, Attribute attribute) {
    return edgeAttributeIndex(type.Name, attribute.AttributeName);
  }

  private String attributeIndexName(VertexType type, EmbeddingAttribute attribute) {
    return vertexAttributeIndex(type.Name, attribute.Name);
  }

  private String attributeIndexCpp(VertexType type, Attribute attribute, boolean isDeclaration) {
    return isDeclaration
        ? declareAttributeIndex(type, attribute)
        : initAttributeIndex(type, attribute);
  }

  private String attributeIndexCpp(EdgeType type, Attribute attribute, boolean isDeclaration) {
    return isDeclaration
        ? declareAttributeIndex(type, attribute)
        : initAttributeIndex(type, attribute);
  }

  private String attributeIndexCpp(VertexType type, EmbeddingAttribute attribute) {
    return String.format("const int %s = %d;", attributeIndexName(type, attribute),
                         attribute.EmbeddingAttrId);
  }

  private String declareAttributeIndex(VertexType type, Attribute attribute) {
    return String.format("int %s = -1;", attributeIndexName(type, attribute));
  }

  private String declareAttributeIndex(EdgeType type, Attribute attribute) {
    return String.format("int %s = -1;", attributeIndexName(type, attribute));
  }

  private String initAttributeIndex(VertexType type, Attribute attribute) {
    return String.format(
        "%s = %s.GetAttributePosition(\"%s\", true);",
        attributeIndexName(type, attribute),
        attributeMetaName(type),
        attribute.AttributeName);
  }

  private String initAttributeIndex(EdgeType type, Attribute attribute) {
    return String.format(
        "%s = %s.GetAttributePosition(\"%s\", true);",
        attributeIndexName(type, attribute),
        attributeMetaName(type),
        attribute.AttributeName);
  }
}
