package com.tigergraph.engine.codegen;

import com.tigergraph.engine.util.error.SyntaxException;
import org.antlr.v4.runtime.BaseErrorListener;
import org.antlr.v4.runtime.RecognitionException;
import org.antlr.v4.runtime.Recognizer;

/**
 * Customized ANTLR listener for raising a system exception
 */
public class RaiseExceptionListener extends BaseErrorListener {

  @Override
  public void syntaxError(Recognizer<?, ?> recognizer,
                          Object offendingSymbol,
                          int line,
                          int charPositionInLine,
                          String msg,
                          RecognitionException e) {
    // line 12:34 no viable alternative at ...
    String errMsg = String.format("line %d:%d %s", line, charPositionInLine, msg);
    throw new SyntaxException(errMsg);
  }
}
