/******************************************************************************
 * Copyright (c)  2017, TigerGraph Inc.
 * All rights reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 ******************************************************************************/
package com.tigergraph.engine.codegen;

import com.tigergraph.schema.controller.QueryController;

import java.util.EnumSet;

public enum compileFlags {
  /** using UDF engine API for code generation */
  UDF_MODE,
  /** using single GPR engine API for code generation */
  SINGLE_MODE,
  /** using distributed GPR engine API for code generation */
  DIST_MODE,
  /** using interpret engine */
  INTERPRET_MODE,
  /** using OpenCypher mode */
  OPENCYPHER_MODE,
  /** flag to determine whether to display warning messages or not */
  WARNING,
  /** unit test only - for transformation test from Cypher to GSQL V3 syntax */
  CYPHER_TRANSFORM_TEST,
  /** unit test only - for transformation test converting StartNode end EndNode to vertices */
  EXPLICIT_NODE_TEST,
  /** unit test only - for transformation test converting expressions */
  EXPR_TRANSFORM_TEST,
  /** unit test only - for transformation test from table to conjunctive pattern */
  TABLE_TO_CONJUNCTIVE_TEST,
  /** unit test only - for transformation test from conjunctive to linear pattern */
  CONJUNCTIVE_TO_LINEAR_TEST,
  /** unit test only - for transformation test on pattern flipping */
  FLIP_PATTERN_TEST,
  /** unit test only - for transformation test from multi-hop to single-hop pattern */
  TRANSFORMATION_TEST,
  /** flag to determine whether to use cost-based optimizer or not */
  COST_OPTIMIZE,
  /** unit test only - for transformation test of cost-based optimization layer */
  COST_OPTIMIZE_TRANSFORM_TEST,
  /** unit test only - for transformation test to add policy into query */
  POLICY_TRANSFORM_TEST,
  /** unit test only - for query planner json output test */
  QUERY_PLANNER_TEST,
  /** unit test only - for query transformation test involving table containers */
  TABLE_CONTAINER_TEST,
  /**
   * flag indicating query info collection, see
   * {@link QueryController#getQuerySignature(String, String, boolean, String)}
   */
  GET_QUERY_INFO,
  /** flag indicating that we are compiling a GSQL function */
  GSQLFUNCTION;

  public static boolean isTransformationTest(EnumSet<compileFlags> cflags) {
    return cflags.contains(TABLE_TO_CONJUNCTIVE_TEST)
        || cflags.contains(CONJUNCTIVE_TO_LINEAR_TEST)
        || cflags.contains(FLIP_PATTERN_TEST)
        || cflags.contains(TRANSFORMATION_TEST)
        || cflags.contains(COST_OPTIMIZE_TRANSFORM_TEST)
        || cflags.contains(CYPHER_TRANSFORM_TEST)
        || cflags.contains(EXPLICIT_NODE_TEST)
        || cflags.contains(EXPR_TRANSFORM_TEST)
        || cflags.contains(POLICY_TRANSFORM_TEST);
  }
};

