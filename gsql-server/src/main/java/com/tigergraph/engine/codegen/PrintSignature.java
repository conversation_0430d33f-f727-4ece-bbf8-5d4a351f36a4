/**
 * ****************************************************************************
 * Copyright (c) 2015-2016, TigerGraph Inc.
 * All rights reserved
 * Unauthorized copying of this file, via any medium is
 * strictly prohibited
 * Proprietary and confidential
 * ****************************************************************************
 */
package com.tigergraph.engine.codegen;


import com.tigergraph.engine.parser.GSQLParser;
import com.tigergraph.engine.parser.GSQLParser.AllTupleFieldAccessContext;
import com.tigergraph.engine.parser.GSQLParser.NonLogicalExprContext;
import com.tigergraph.engine.parser.GSQLParser.PrintExprContext;
import com.tigergraph.engine.parser.GSQLParser.VSetProjContext;
import com.tigergraph.engine.parser.GSQLParserBaseListener;
import com.tigergraph.engine.typechecker.DataType;
import com.tigergraph.engine.typechecker.VarType;
import com.tigergraph.schema.topology.Attribute;
import com.tigergraph.schema.topology.Graph;
import com.tigergraph.schema.topology.VertexType;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import org.antlr.v4.runtime.ParserRuleContext;
import org.antlr.v4.runtime.tree.ParseTreeProperty;
import org.json.JSONArray;
import org.json.JSONObject;




/**
 * Extracts query output signature into a json object
 *  (1) vsetVar only:  multiple vertex type object.
 *  (2) vsetVar with projection :  multiple column list.
 * Either above case, we put the schema of output in the vSetArray,
 * as shown below "SecondNeighbors".
 *
 * Note: each print statement is an object in the results [].
 *  - If the print expr is a set, the object's value is an array.
 *  - If the print expr is a scalar value, the object's value is a primitive type.
 *  "Print result1, result2;" is different from "Print result1; Print result2;"
 *  the first is results:
 *  [{result1: [], result2:[]}] , second is results:[{result1:[]}, {result2:[]}]
 *
 * See  appendix for sample output
 *
 */
public class PrintSignature extends GSQLParserBaseListener {

  // graph schema, for attribute access
  private Graph _schema;

  // annotates statement node with pc. computed in StatementCounter.
  ParseTreeProperty<Integer> _PCs;

  String _queryName;

  // captures generated code
  private StringBuilder _printSignature;

  //a rest response contains basic meta plus a result array.
  private JSONObject responseJson = null;
  //the result array, containing one object per print statement.
  private JSONArray resultJsonArray = null;

  //for each print statment we use this object.
  private JSONObject  printStatementObj = null;

  // [GLE-2709] use JSON array to keep parameter order
  private JSONArray params = null;

  public StringBuilder getPrintSignature() {
    return _printSignature;
  }

  private Map<String, Set<String>> _vSet2Type;

  VarType _varType;

  private CommonVariable commonVariable;
  private StringBuilder _udfCpp;

  public PrintSignature(
      CommonVariable cv,
      Graph s,
      ParseTreeProperty<Integer> pc,
      VarType varType) {

    commonVariable = cv;
    _schema = s;
    _PCs = pc;
    _vSet2Type = new HashMap<String, Set<String>>();

    _varType = varType;
    _udfCpp = new StringBuilder();

    //compose the header of the result
    responseJson = new JSONObject();
    //initialize resultJsonArray, but not yet put in responseJson.
    resultJsonArray = new JSONArray();


    // init input parameter obj
    params = new JSONArray();
  }

  //extract input parameters
  @Override
  public void exitParam(GSQLParser.ParamContext ctx) {

    String pType = ctx.parType().getText();
    String pName = ctx.ident().getText();

    JSONObject pair = new JSONObject();
    pair.put(pName, pType);
    params.put(pair);
  }


  //////////////////
  // listener rules
  /////////////////
  @Override
  public void enterJob(GSQLParser.JobContext ctx) {
    _queryName = commonVariable.getQueryName();
  }


  @Override
  public void enterStatementPrint(GSQLParser.StatementPrintContext ctx) {
    //each print statement is a object in the result [], create it here.
    printStatementObj = new JSONObject();

    switch (commonVariable.JsonAPI) {
      case V2:
        runStatementPrintV2(ctx);
        break;
      default:
        //only support v2
        com.tigergraph.engine.util.Util.apiVersionError(ctx, commonVariable);
        break;
    }
  }

  @Override
  public void exitStatementSeed(GSQLParser.StatementSeedContext ctx) {
    _vSet2Type.put(ctx.lVSetVar().getText(), _varType.getTypeStringTreeNode(ctx));
  }

  @Override
  public void exitStatementTopKVecSearchRval(GSQLParser.StatementTopKVecSearchRvalContext ctx) {
    _vSet2Type.put(ctx.lVSetVar().getText(), _varType.getTypeStringTreeNode(ctx));
  }

  @Override
  public void exitStatementBlock(GSQLParser.StatementBlockContext ctx) {
    _vSet2Type.put(ctx.lVSetVar().getText(), _varType.getTypeStringTreeNode(ctx));
  }

  @Override
  public void exitStatementLVarAssignExpr(GSQLParser.StatementLVarAssignExprContext ctx) {
    String vSet = ctx.lhsIdent().getText();
    if (_varType.IsVSetVar(vSet)) {
      _vSet2Type.put(vSet, _varType.getTypeStringTreeNode(ctx));
    }
  }

  @Override
  public void exitStatementPrint(GSQLParser.StatementPrintContext ctx) {
    //add current statement to the array
    resultJsonArray.put(printStatementObj);
  }

  public void runStatementPrintV2(GSQLParser.StatementPrintContext ctx) {

    //we don't handle print to file case for print signature
    if (ctx.rhsIdent() != null) {
      return;
    }

    //print to json case, loop the print expr list.
    //printExpr could be
    // - v
    // - v[v.a, v.b]
    // - @@v
    // - "aaa".
    for (PrintExprContext pExpr : ctx.printExpr()) {
      GSQLParser.ExprContext expr = pExpr.expr();
      //check vSetVar
      boolean hasVSetAccess = _varType.IsVSetVar(pExpr.expr().getText());

      //1. print vSetVar with projection/global accum/global var
      if (hasVSetAccess) {
        String vSetVar = expr.getText();
        boolean onlyVSet = pExpr.vSetProj().size() == 0;

        if (onlyVSet) {
          //foreach vt, generate a vt schema, put in an array
          handleVSetVarOnlyPrintExpr(vSetVar);
        } else {
          //foreach vt, generate a table schema
          handleVSetWithProjExpr(pExpr, vSetVar);
        }

        //2. no vSetVar
      } else {
        String name = _varType.GetPrintKeyStr(expr) ;

        if (name != null) {
          // if name is string constant, remove the quotes
          name = name.replace("\\\"", "").replace("\"", "");
        }

        DataType dt = _varType.getType(expr);
        String type = dt.toString2();
        printStatementObj.put(name,  type);
      }

    } //end for loop of print expr

  } //end runStatementPrintV2


  /**
   * for vSetVar v, we print out schema for each possible type
   *
   *  {
   *    v: [
   *      { "v_id": int,
   *       "attributes": {
   *        "att1": string,
   *        "att2": int
   *        },
   *       "v_type": "person"
   *      },
   *      { "v_id": int,
   *        "attributes": {
   *        "att3": string,
   *        "att4": int
   *        },
   *       "v_type": "person"
   *      }
   *     ]
   *    }
   *
   */
  private void handleVSetVarOnlyPrintExpr(String vSetVar) {

    JSONArray printVSetJsonArray = new JSONArray();

    //foreach vt type, create a vt object, put in printStatment obj.
    for (String vSetTy : _vSet2Type.get(vSetVar)) {
      JSONObject printExprObj = new JSONObject();
      VertexType vt = _schema.GetVertex(vSetTy);
      printExprObj.put("v_id", "int");

      JSONObject attributesObj = new JSONObject();
      for (Attribute attr : vt.Attributes) {
        attributesObj.put(attr.AttributeName, attr.AttributeType.toString());
      }

      // write vaccs
      // get all declScopes from VarType.java for traversing all vAccs
      Set<ParserRuleContext> declScopes = _varType.getDeclScopes();
      for (ParserRuleContext declScope : declScopes) {
        if (_varType.getAccTypeMapInScope(declScope) != null) {
          // if vAccTypeMap for this scope is not null, traverse this map
          for (String vAccName : _varType.getAccTypeMapInScope(declScope).keySet()) {
            if (!_varType.IsVAccVar(vAccName)) {
              continue;
            }
            DataType dt = _varType.getAccTypeMapInScope(declScope).get(vAccName);
            String type = dt.toString();
            if (!dt.isVertex() && !dt.isEnumerator() && dt.isBaseType()
                || dt.isPrimitiveAccum() && dt.getNut().isBaseType()) {
              //vertex,int, string, ...,
              //or primitive accum case
              type  = dt.getNut().toString();
            }

            //is not generated by transformed intermediate accumulators.
            if (!commonVariable.isTransformVertexAccumName(vAccName.replaceAll("@", ""))) {
              attributesObj.put(vAccName, type);
            }
          }
        }
      }

      printExprObj.put("attributes", attributesObj);
      printExprObj.put("v_type", vt.Name);
      printVSetJsonArray.put(printExprObj);
    }

    printStatementObj.put(vSetVar, printVSetJsonArray);
  }



  /*
   * handle this  v [v.age, v.@cnt, @@abc, "efg"]
   *
   */
  private void handleVSetWithProjExpr(PrintExprContext pExprCtx, String vSetVar) {

    JSONArray printVSetJsonArray = new JSONArray();

    //foreach vt type, create a vt object containing all the
    //projExprList, put in the printStatment obj.
    for (String vSetTy : _vSet2Type.get(vSetVar)) {

      JSONObject printExprObj = new JSONObject();
      printExprObj.put("v_id", "int");

      JSONObject attributesObj = new JSONObject();

      //for each projection, add it to the attributes array.
      for (VSetProjContext vSetProj : pExprCtx.vSetProj()) {
        ParserRuleContext ctx =  ((NonLogicalExprContext) vSetProj.expr()).exprAtom();
        DataType dt = _varType.getType(ctx);

        String attrName = null;

        // v[ v.att, ..]
        if (ctx instanceof AllTupleFieldAccessContext) {
          attrName = ((AllTupleFieldAccessContext) ctx).ident().getText();
          Attribute attr = attrName != null ? _schema.getAttribute(vSetTy, attrName) : null;
          if (attr != null) {
            String type = attr.AttributeType.toString();
            //handle the AS clause.
            String name =  _varType.GetPrintKeyStr(vSetProj.expr());
            attributesObj.put(name, type);
          }
        } else {  // v[ v.@aa, @@bb, "abc"], accumulator or global variable or constant,
          attrName = _varType.GetPrintKeyStr(vSetProj.expr());
          if (attrName != null) {
            // if name is string constant, remove the quotes
            attrName = attrName.replace("\\\"", "").replace("\"", "");
          }

          String type = dt.toString();
          if (!dt.isVertex() && !dt.isEnumerator() && dt.isBaseType()
              || dt.isPrimitiveAccum() && dt.getNut().isBaseType()) {
            // vertex,int, string, ...,
            // or primitive accum case
            type  = dt.getNut().toString();
          }
          attributesObj.put(attrName, type);
        }
      }

      printExprObj.put("attributes", attributesObj);
      printExprObj.put("v_type", vSetTy);
      printVSetJsonArray.put(printExprObj);
    }

    printStatementObj.put(vSetVar, printVSetJsonArray);

  }


  //when exiting, put the result array in the responseJSon.
  @Override
  public void exitJob(GSQLParser.JobContext ctx) {
    responseJson.put("error", false);
    responseJson.put("message", "");

    responseJson.put("queryname", commonVariable.query_name);

    JSONObject version = new JSONObject();
    version.put("schema", _schema.GraphConfigVersion);
    version.put("api", commonVariable.JsonAPI);
    version.put("edition", "ENTERPRISE_EDITION");

    responseJson.put("version", version);


    responseJson.put("input", params);
    responseJson.put("output", resultJsonArray);
    //put in output string buffer.
    _udfCpp.append(responseJson.toString());
  }

  public String getQueryReturnTypeInfo() {
    return _udfCpp.toString();
  }

}
/*
 * Appendix.
 *
 * Output sample.
 *
 *
 *
 {
  "output": [
    {
      "s2": [
        {
          "v_id": "int",
          "attributes": {
            "@mmap": "MapAccum<int, string>",
            "@cnt": "int",
            "id": "UINT",
            "title": "STRING",
            "creationDate": "INT"
          },
          "v_type": "forum"
        },
        {
          "v_id": "int",
          "attributes": {
            "@mmap": "MapAccum<int, string>",
            "@cnt": "int",
            "name": "STRING",
            "id": "UINT",
            "url": "STRING"
          },
          "v_type": "country"
        },
        {
          "v_id": "int",
          "attributes": {
            "@mmap": "MapAccum<int, string>",
            "@cnt": "int",
            "browserUsed": "STRING",
            "length": "UINT",
            "locationIP": "STRING",
            "id": "UINT",
            "creationDate": "INT",
            "content": "STRING"
          },
          "v_type": "comments"
        },
        {
          "v_id": "int",
          "attributes": {
            "imageFile": "STRING",
            "@mmap": "MapAccum<int, string>",
            "@cnt": "int",
            "browserUsed": "STRING",
            "length": "UINT",
            "locationIP": "STRING",
            "id": "UINT",
            "creationDate": "INT",
            "lang": "STRING",
            "content": "STRING"
          },
          "v_type": "post"
        },
        {
          "v_id": "int",
          "attributes": {
            "birthday": "INT",
            "firstName": "STRING",
            "lastName": "STRING",
            "gender": "STRING",
            "@mmap": "MapAccum<int, string>",
            "speaks": "STRING_SET",
            "@cnt": "int",
            "browserUsed": "STRING",
            "locationIP": "STRING",
            "id": "UINT",
            "creationDate": "INT",
            "email": "STRING_SET"
          },
          "v_type": "person"
        },
        {
          "v_id": "int",
          "attributes": {
            "@mmap": "MapAccum<int, string>",
            "@cnt": "int",
            "name": "STRING",
            "id": "UINT",
            "url": "STRING"
          },
          "v_type": "tag"
        }
      ]
    },
    {
      "s1": [
        {
          "v_id": "int",
          "attributes": {
            "s1.id": "UINT",
            "aaa": "string",
            "@@cnt2": "int",
            "s1.@cnt": "int",
            "s1.creationDate": "INT"
          },
          "v_type": "comments"
        },
        {
          "v_id": "int",
          "attributes": {
            "s1.id": "UINT",
            "aaa": "string",
            "@@cnt2": "int",
            "s1.@cnt": "int",
            "s1.creationDate": "INT"
          },
          "v_type": "post"
        }
      ]
    },
    {
      "s1": [
        {
          "v_id": "int",
          "attributes": {
            "A": "UINT",
            "B": "INT",
            "C": "int",
            "D": "string",
            "E": "int"
          },
          "v_type": "comments"
        },
        {
          "v_id": "int",
          "attributes": {
            "A": "UINT",
            "B": "INT",
            "C": "int",
            "D": "string",
            "E": "int"
          },
          "v_type": "post"
        }
      ]
    },
    {
      "abc": "string"
    },
    {
      "@@cnt2": "int"
    }
  ],
  "input": {
    "a1": "int",
    "b2": "float",
    "d1": "vertex<comments>",
    "c1": "set<int>"
  },
  "queryname": "Q1",
  "error": false,
  "message": "",
  "version": {
    "schema": 0,
    "edition": "ENTERPRISE_EDITION",
    "api": "V2"
  }
}
*/
