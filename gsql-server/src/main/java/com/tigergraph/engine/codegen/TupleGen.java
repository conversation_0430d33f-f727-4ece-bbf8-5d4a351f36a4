/******************************************************************************
 * Copyright (c)  2017, TigerGraph Inc.
 * All rights reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 ******************************************************************************/
package com.tigergraph.engine.codegen;

import com.tigergraph.engine.queryplanner.Tuple;
import com.tigergraph.engine.queryplanner.Tuple.TupleField;
import com.tigergraph.engine.queryplanner.expression.Variable;
import com.tigergraph.engine.semchecker.NameChecker;
import com.tigergraph.engine.typechecker.DataType;
import com.tigergraph.engine.util.BooleanPair;
import com.tigergraph.engine.util.error.SystemException;
import com.tigergraph.utility.CodeGenUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.StringJoiner;

/**
 * This class contains helper functions for Tuple generation in GPR mode.
 */
public class TupleGen {

  /**
   * Generates C++ code for the given tuple.
   *
   * Note that in some cases, the generated tuple code does not need a template header.
   * Such tuples are generated inside a GroupByAccum struct, where the template header
   * is already present for the struct.
   *
   * Note that in some cases, the tuple may contain fields with types that are not hash-able,
   * such as {@code SetAccum}, {@code BagAccum} etc. Currently this only applies for the
   * internally generated {@code accumTuple} for {@code GroupByAccum}.
   *
   * @param tuple the tuple for which we want to generate the C++ code
   * @param cv {@code CommonVariable} instance for error report when generating json printer code
   * @param needTemplate boolean flag indicating whether a template header is needed
   * @param genHashFunction boolean flag indicating whether a hash function is needed
   * @return the generated C++ code for the given tuple
   */
  public static String generate(Tuple tuple, CommonVariable cv,
      boolean needTemplate, boolean genHashFunction) {
    String template = needTemplate ? genTemplate(tuple) : "";
    String members = genMemberDecl(tuple);
    String defaultConstr = genDefaultConstr(tuple);
    String copyConstr = genCopyConstr(tuple);
    // Directly use element type if the tuple only contains 1 element. See: GLE-2311
    String cppTupleConstr = (tuple.fields.size() > 1) ? genCppTupleConstr(tuple) : "";
    String ostream = genOstream(tuple);
    String equalOp = genEqualOp(tuple);
    String combineOp = genCombineOp(tuple);
    String lessOP = genLessOp(tuple);
    String greaterOP = genGreaterOp(tuple);
    // Directly use element type if the tuple only contains 1 element. See: GLE-2311
    String cppTupleConvertor = genCppTupleConverter(tuple, (tuple.fields.size() == 1));
    String hashFunc = genHashFunction
                    ? CodeGenUtil.getTupleHashFunction(tuple.getName(), tuple.getFields())
                    : "";
    String jsonPrinter = genJsonPrinter(tuple, cv);
    String serialize = genSerialize(tuple);

    return
        template
            + "struct " + tuple.getName() + " {\n"
            + members
            + "\n"
            + defaultConstr
            + "\n"
            + copyConstr
            + "\n"
            + cppTupleConstr
            + "\n"
            + ostream
            + "\n"
            + equalOp
            + "\n"
            + combineOp
            + "\n"
            + lessOP
            + "\n"
            + greaterOP
            + "\n"
            + cppTupleConvertor
            + "\n"
            + hashFunc
            + "\n"
            + jsonPrinter
            + "\n"
            + serialize
            + "};\n"
        ;
  }

  private static String genTemplate(Tuple tuple) {
    String template = "";
    for (Tuple.TupleField field : tuple.getFields()) {
      if (field.getType().isTemplate()) {
        template += "typename " + field.getType().toCpp() + ", ";
      }
    }
    if (!template.isEmpty()) {
      template = template.substring(0, template.length() - 2);
      return "template<" + template + ">\n";
    } else {
      return "";
    }
  }

  public static String genAllGroupByAccumStructCode(Variable variable, CommonVariable cv) {
    return genGroupByAccumStructCode(variable.dtype, cv);
  }

  public static String genGroupByAccumStructCode(DataType dtype, CommonVariable cv) {
    // MapAccum can have GroupByAccum, so recursively call this function for each child
    if ("MapAccum".equals(dtype.Name)) {
      String result = "";
      // skip the key of MapAccum that cannot be a GroupByAccum
      for (int i = 1; i < dtype.ElementTypeList.size(); i++) {
        result += genGroupByAccumStructCode(dtype.ElementTypeList.get(i), cv);
      }
      return result;
    } else if (!"GroupByAccum".equals(dtype.Name)) {
      return "";
    }

    String result = "";
    List<Tuple.TupleField> baseFields = new ArrayList<>();
    List<TupleField> accumFields = new ArrayList<>();

    for (int i = 0; i < dtype.ElementTypeNames.size(); i++) {
      String fieldName = dtype.ElementTypeNames.get(i);
      DataType fieldType = dtype.ElementTypeList.get(i);

      DataType dt = DataType.createTemplateDataType(fieldName + "_t");
      if (fieldType.isBaseType()) {
        baseFields.add(new TupleField(dt, fieldName));
      } else {
        // if the field is fixed length bitwise, set size to -1
        // to avoid call resize() function as only dynamic bitwise has resize() function
        if (fieldType.isBitwiseAccum() && !fieldType.isDynamicBitwiseAccum()) {
          dt.size = -1;
        } else {
          dt.size = fieldType.size;
        }
        accumFields.add(new TupleField(dt, fieldName));
        result += genGroupByAccumStructCode(fieldType, cv);

      }
    }

    return result + genGroupByAccum(dtype, baseFields, accumFields, cv);
  }

  private static String genMemberDecl(Tuple tuple) {
    String member = "";

    for (Tuple.TupleField field : tuple.getFields()) {
      DataType baseType = field.getType();
      String fieldName = field.getName();
      if (baseType.isChar()) {
        member += "char " + fieldName + "[" + baseType.size + "];\n";
      } else {
        member += baseType.toCpp() + " " + fieldName + ";\n";
      }
    }
    return member;
  }

  private static String genDefaultConstr(Tuple tuple) {
    String init = "";
    for (Tuple.TupleField field : tuple.getFields()) {
      DataType baseType = field.getType();
      String fieldName = field.getName();

      // For vertex and edge type fields in user defined tuple,
      // no initialization is needed.
      if (!baseType.isVertex()
          && !baseType.isEdge()
          && !baseType.isJsonObject()
          && !baseType.isJsonArray()
          && !baseType.isChar()) {
        init += fieldName + " = " + baseType.DefaultValue() + ";\n";
      }

    }
    return tuple.getName() + "() {\n"
        + init
        + "}\n"
        ;
  }

  private static String genCopyConstr(Tuple tuple) {
    String funcParam = "";
    String body = "";
    for (Tuple.TupleField field : tuple.getFields()) {
      DataType baseType = field.getType();
      String fieldName = field.getName();

      if (baseType.isChar()) {
        funcParam += "std::string& " + fieldName + "_, ";
        body += "std::memcpy(" + fieldName + ", " + fieldName + "_.c_str(), "
            + baseType.size + ");\n";
      } else {
        funcParam += baseType.toCpp() + " " + fieldName + "_, ";
        body += fieldName + " = " + fieldName + "_;\n";
      }
    }
    funcParam = funcParam.substring(0, funcParam.length() - 2);
    return tuple.getName() + "(" + funcParam + ") {\n"
        + body
        + "}\n"
        ;
  }

  // generate Tuple constructor
  private static String genCppTupleConstr(Tuple tuple) {
    StringJoiner types = new StringJoiner(", ");
    StringBuilder body = new StringBuilder();
    int index = 0;
    for (Tuple.TupleField field : tuple.getFields()) {
      DataType baseType = field.getType();
      String fieldName = field.getName();
      types.add(baseType.toCpp());
      body.append(fieldName);
      body.append(" = std::get<");
      body.append(index);
      body.append(">(" + CodeGenUtil.CPP_NAME_OTHER_ + ");\n");
      index++;
    }

    return
        tuple.getName()
            + "(const std::tuple<"
            + types.toString()
            + ">& " + CodeGenUtil.CPP_NAME_OTHER_ + ") {\n"
            + body.toString()
            + "}\n";
  }

  private static String genOstream(Tuple tuple) {
    String body = "os << \"[\";\n";
    String lastLine = "";
    for (Tuple.TupleField field : tuple.getFields()) {
      String fieldName = field.getName();
      body += lastLine;
      lastLine = "os << \"" + fieldName + " \" << " + CodeGenUtil.CPP_NAME_OTHER_ + "." + fieldName
          + " << \"|\";\n";
    }
    body += lastLine.replace("\"|\"", "\"]\"");
    return
        "friend std::ostream& operator<<(std::ostream& os, const "
            + tuple.getName()
            + "& " + CodeGenUtil.CPP_NAME_OTHER_ + ") {\n"
            + body
            + "return os;\n"
            + "}\n"
        ;
  }

  private static String genEqualOp(Tuple tuple) {
    String body = "";
    for (Tuple.TupleField field : tuple.getFields()) {
      DataType baseType = field.getType();
      String fieldName = field.getName();
      if (baseType.isChar()) {
        body += String.format("strncmp(%s, %s.%s, %d) == 0 &&\n", fieldName,
            CodeGenUtil.CPP_NAME_OTHER_, fieldName, baseType.size);
      } else {
        body += fieldName + " == " + CodeGenUtil.CPP_NAME_OTHER_ + "." + fieldName + " &&\n";
      }
    }
    body = body.substring(0, body.length() - 4) + ";\n";
    return
        "bool operator==(const "
            + tuple.getName()
            + "& " + CodeGenUtil.CPP_NAME_OTHER_ + ") const {\n"
            + "return\n"
            + body
            + "}\n"
        ;
  }

  private static String genCombineOp(Tuple tuple) {
    String body = "";
    for (Tuple.TupleField field : tuple.getFields()) {
      DataType baseType = field.getType();
      String fieldName = field.getName();
      if (baseType.isChar()) {
        body += "std::memcpy("
            + fieldName
            + ", " + CodeGenUtil.CPP_NAME_OTHER_ + "."
            + fieldName
            + ", "
            + baseType.size
            + ");\n";
      } else if (baseType.isBool()) {
        body += fieldName + " |= " + CodeGenUtil.CPP_NAME_OTHER_ + "." + fieldName + ";\n";
      } else {
        body += fieldName + " += " + CodeGenUtil.CPP_NAME_OTHER_ + "." + fieldName + ";\n";
      }
    }
    return
        tuple.getName()
            + "& operator+=(const "
            + tuple.getName()
            + "& " + CodeGenUtil.CPP_NAME_OTHER_ + ") {\n"
            + body
            + "return *this;\n"
            + "}\n"
        ;
  }

  public static String genCompareIfStatements(DataType baseType, String fieldName,
                                               boolean reverseSort, boolean forTuple) {
    StringBuilder body = new StringBuilder();
    String lessOp = reverseSort ? ">" : "<";
    String antiLessOp = reverseSort ? "<" : ">";

    String returnTrueStmt = forTuple ? "return _reverse^true;\n" : "return true;\n";
    String returnFalseStmt = forTuple ? "return _reverse^false;\n" : "return false;\n";
    String leftFieldName = forTuple ? "lhs." + fieldName : fieldName;
    String rightFieldName = forTuple ? "rhs." + fieldName : CodeGenUtil.CPP_NAME_OTHER_
        + "." + fieldName;

    if (baseType != null && baseType.isChar()) {
      body.append("if (" + String.format("strncmp(%s, %s, %d)", leftFieldName, rightFieldName,
                                         baseType.size) + " " + lessOp + " 0) " + returnTrueStmt);
      body.append("if (" + String.format("strncmp(%s, %s, %d)", leftFieldName, rightFieldName,
                                         baseType.size) + " " + antiLessOp + " 0) "
                  + returnFalseStmt);
    } else {
      body.append(
          "if (" + leftFieldName + " " + lessOp + " " + rightFieldName + ") " + returnTrueStmt);
      body.append("if (" + leftFieldName + " " + antiLessOp + " " + rightFieldName + ") "
                  + returnFalseStmt);
    }
    return body.toString();
  }

  private static String genLessOp(Tuple tuple) {
    StringBuilder body = new StringBuilder();
    for (Tuple.TupleField field : tuple.getFields()) {
      body.append(genCompareIfStatements(field.getType(), field.getName(), !field.isAsc(), false));
    }
    return
        "bool operator<(const "
            + tuple.getName()
            + "& " + CodeGenUtil.CPP_NAME_OTHER_ + ") const {\n"
            + body
            + "return false;\n"
            + "}\n"
        ;
  }

  private static String genGreaterOp(Tuple tuple) {
    StringBuilder body = new StringBuilder();
    for (Tuple.TupleField field : tuple.getFields()) {
      body.append(genCompareIfStatements(field.getType(), field.getName(), field.isAsc(), false));
    }
    return
        "bool operator>(const "
            + tuple.getName()
            + "& " + CodeGenUtil.CPP_NAME_OTHER_ + ") const {\n"
            + body
            + "return false;\n"
            + "}\n"
        ;
  }

  // generate operator from tuple to std::tuple
  private static String genCppTupleConverter(Tuple tuple, boolean isOneElement) {
    StringJoiner types = new StringJoiner(", ");
    StringJoiner members = new StringJoiner(",");
    for (Tuple.TupleField field : tuple.getFields()) {
      DataType baseType = field.getType();
      String fieldName = field.getName();
      types.add(baseType.toCpp());
      members.add(fieldName);
    }
    if (isOneElement) {
      return
        "operator " + types.toString() + "() const {\n"
            + "return " + members.toString() + ";\n"
            + "}\n";
    }
    return
        "operator std::tuple<" + types.toString()
            + ">() const {\n"
            + "return std::make_tuple("
            + members.toString()
            + ");\n"
            + "}\n";

  }


  private static String genJsonPrinter(Tuple tuple, CommonVariable cv) {
    String body = "";
    for (Tuple.TupleField field : tuple.getFields()) {
      DataType baseType = field.getType();
      String fieldName = field.getName();
      String value = fieldName;
      if (baseType.isPrimitiveAccum() && !baseType.isBitwiseAccum()) {
        value += ".get_value()";
      }
      // This piece of code is inside outer level json_printer function,
      // so use graphAPI instead of context->GraphAPI() as the paramter when invoking
      // json_printer function.
      body += CodeGenUtil.getJsonWriteCpp(cv, baseType, fieldName, value, null, false, false);
    }
    String cpp =
        "void json_printer "
            + "(gutil::JSONWriter& writer, gpelib4::EngineServiceRequest& _request,\n"
            + "gapi4::UDFGraphAPI* graphAPI, bool verbose = false) const {\n"
            + "writer.WriteStartObject();\n"
            + body
            + "writer.WriteEndObject();\n"
            + "  }\n"
        ;

    cpp += CodeGenUtil.getTupleJsonWriteNameFunction();
    return cpp;
  }

  /**
   * Generates GroupByAccum struct. Even this struct is very similar to the tuple's, there are few
   * different places, so we generate separately.
   *
   * <p>For a given GroupByAccum type, it could have m base types and n accumulator types. We
   * generate the struct with templates, but the structs are different for different (m,n). We also
   * append the name of each field to struct name, the reason is 1. when user uses name to access a
   * field we have to do a translation since we may use same struct (w/ templates) if we don't
   * append field name 2. in print, we need to print field name exactly as user gives in the
   * defination. So we generate a GroupByAccum struct for each (m,n) combination and field names.
   *
   * <p>The signature is
   *
   * <p>template <typename x_t, typename xx_t, class xxx_t, class xxxx_t> struct
   * GroupByAccum_m_x_xx_n_xxx_xxxx { struct baseTuple { x_t x; xx_t xx; } struct accumTuple { xxx_t
   * xxx; xxxx_t xxxx; } MapAccum<baseTuple, accumTuple> map;
   *
   * <p>GroupByAccum_m_n () { } GroupByAccum_m_n (x_t base_1, xx_t base_2, xxx_t accum_1, xxxx_t
   * accum_2) { map = MapAccum<baseTuple, accumTuple> (baseTuple(base_1, base_2),
   * accumTuple(accum_1, accum_2)); } }
   *
   * note: If only a single value or key is used in the GroupByAccum, the direct datatype is used
   * instead of baseTuple or accumTuple.
   */
  private static String genGroupByAccum(DataType dType, List<TupleField> base,
      List<TupleField> accum, CommonVariable cv) {
    int baseSize = base.size();
    int accumSize = accum.size();

    String name = "GroupByAccum_" + baseSize;
    // If single key or value is used, we append the datatype to the name. This is to avoid
    // duplicate GroupByAccum struct declarations in the C++ generated code.
    // i.e. "GroupByAccm<int d, SumAccum<int> s group> @@group2" and
    // "GroupByAccum<int d, AvgAccum s> @@group3" will both generate and use
    // "struct GroupByAccum_1_d_1_s". This would be okay with multiple keys and values, since
    // we use a container tuple class, "accumTuple" and "baseTuple", for the parameter datatypes
    // so the class is shared, but in the case of a single key or value, the datatypes are
    // accessed directly and this tuple is not used. So we need to generate a different struct for
    // each GroupByAccum using single key or value with different parameter types.
    if (baseSize == 1) {
      name += "_" + dType.ElementTypeList.get(0).Name;
    }
    for (TupleField tf : base) {
      name += "_" + tf.getName();
    }

    name += "_" + accumSize;
    // see comment above
    if (accumSize == 1 ) {
      name += "_" + dType.ElementTypeList.get(baseSize).Name;
      // Nested GroupByAccum will have same issue
      // i.e. GroupByAccum<int d, string s, GroupByAccum<int i, SumAccum<int> s> group> @@group2;
      //  GroupByAccum<int d, string s, GroupByAccum<int i, AvgAccum s> group> @@group3;
      if ("GroupByAccum".equals(dType.ElementTypeList.get(baseSize).Name)) {
        name += "_" + dType.ElementTypeList.get(baseSize).GroupByTypeCpp();
      } else {
        // Same issue for other accumulator types
        // i.e. GroupByAccum<INT a, SumAccum<INT> b> @@sumAccum;
        // GroupByAccum<INT a, SumAccum<UINT> b> @@sumAccum2;
        for (DataType dt : dType.ElementTypeList.get(baseSize).ElementTypeList) {
          name += "_" + dt.Name;
        }
      }
    }
    for (TupleField tf : accum) {
      name += "_" + tf.getName();
    }

    if (cv.GroupByAccumSet.contains(name)) {
      return "";
    }
    cv.GroupByAccumSet.add(name);

    String template = "";
    for (TupleField tf : base) {
      template += "typename " + tf.getName() + "_t, ";
    }
    for (TupleField tf : accum) {
      template += "class " + tf.getName() + "_t, ";
    }
    template = template.substring(0, template.length() - 2);

    // struct signature
    String code = "template<" + template + ">\n" + "struct " + name + " {\n";
    // sub-structs
    StringBuilder sb = new StringBuilder();

    String baseTupleString = "";
    if (baseSize != 1) {
      Tuple baseTuple = new Tuple("baseTuple");
      baseTuple.fields = base;
      baseTupleString = generate(baseTuple, cv, false, true);
    }

    String accumTupleString = "";
    if (accumSize != 1) {
      Tuple accumTuple = new Tuple("accumTuple");
      accumTuple.fields = accum;
      accumTupleString = generate(accumTuple, cv, false, false);
    }

    code += baseTupleString + accumTupleString;

    String keySig = "";
    String keyParam = "";

    // If single Key value is used, we use the DataType directly as the key in the map
    if (baseSize == 1) {
      keySig = dType.ElementTypeList.get(0).toCpp() + " base_0, ";
      keyParam = "base_0, ";
    } else {
      for (int i = 0; i < baseSize; ++i) {
        keySig += base.get(i).getName() + "_t base_" + i + ", ";
        keyParam += "base_" + i + ", ";
      }
    }
    keySig = keySig.substring(0, keySig.length() - 2);
    keyParam = keyParam.substring(0, keyParam.length() - 2);

    String valSig = "";
    String valParam = "";
    // If single accumulator value is used, we use the DataType directly
    // in the value field of the map.
    if (accumSize == 1) {
      // baseSize will be the index of the single accum value, since it appears after
      // the base types in 'ElementTypeList'
      valSig = dType.ElementTypeList.get(baseSize).toCpp() + " accum_0, ";
      valParam += "accum_0, ";
    } else {
      for (int i = 0; i < accumSize; ++i) {
        valSig += accum.get(i).getName() + "_t accum_" + i + ", ";
        valParam += "accum_" + i + ", ";
      }
    }
    valSig = valSig.substring(0, valSig.length() - 2);
    valParam = valParam.substring(0, valParam.length() - 2);

    String heapaccum = "";
    for (int i = 0; i < accumSize; ++i) {
      if (accum.get(i).getType().size != -1) {
        heapaccum += "accum_" + i + ".resize(" + accum.get(i).getType().size + ");\n";
      }
    }

    code += "  "
        + CodeGenUtil.genGroupByMapDefinition(baseSize, accumSize, dType)
        + "  map;\n";

    code += "  "
        + ((baseSize == 1) ? dType.ElementTypeList.get(0).toCpp() : "baseTuple")
        + " baseItem;\n";
    code += "  "
        + ((accumSize == 1) ? dType.ElementTypeList.get(baseSize).toCpp() : "accumTuple")
        + " accumItem;\n\n";

    code += "  "
        //constructor
        + name
        + "() { }\n\n"
        + "  "
        + name
        + "("
        + keySig
        + ", "
        + valSig
        + ") {\n"
        + heapaccum
        + "    map = "
        + CodeGenUtil.genGroupByMapDefinition(baseSize, accumSize, dType)
        + "(";

    code += (baseSize == 1)
        ? keyParam + ", "
        : "baseTuple(" + keyParam + "), ";
    code += (accumSize == 1)
        ? valParam + ");\n"
        : "accumTuple(" + valParam + "));\n";

    // use prefix to avoid conflict with accum name
    String archiveVar = NameChecker.RESERVED_PREFIX + "ar_";
    code += "  }\n\n"
        + "  template <class ARCHIVE>\n"
        + "  void serialize(ARCHIVE& " + archiveVar + ") {\n"
        + "    " + archiveVar + "(map, baseItem, accumItem);\n"
        + "  }\n"
        +

        //operators function
        "  friend std::ostream& operator<<"
        + "(std::ostream& os, const "
        + name
        + "& m) {\n"
        + "    os << m.map;\n"
        + "    return os ;\n"
        + "  }\n"
        + "  bool operator==("
        + name
        + " const &" + CodeGenUtil.CPP_NAME_OTHER_ + ") const {\n"
        + "    return map == " + CodeGenUtil.CPP_NAME_OTHER_ + ".map;\n"
        + "  }\n"
        + "  bool operator!=("
        + name
        + " const &" + CodeGenUtil.CPP_NAME_OTHER_ + ") const {\n"
        + "    return map != " + CodeGenUtil.CPP_NAME_OTHER_ + ".map;\n"
        + "  }\n"
        + "  "
        + name
        + " operator+ ("
        + name
        + " const &" + CodeGenUtil.CPP_NAME_OTHER_ + ") {\n"
        + "    "
        + name
        + " newG;\n"
        + "    newG += *this;\n"
        + "    newG += " + CodeGenUtil.CPP_NAME_OTHER_ + ";\n"
        + "    return newG;\n"
        + "  }\n"
        + "  void operator= ("
        + name
        + " const &" + CodeGenUtil.CPP_NAME_OTHER_ + ") {\n"
        + "    map = " + CodeGenUtil.CPP_NAME_OTHER_ + ".map;\n"
        + "  }\n"
        + "  void operator+=("
        + name
        + " const &" + CodeGenUtil.CPP_NAME_OTHER_ + ") {\n"
        + "    map += " + CodeGenUtil.CPP_NAME_OTHER_ + ".map;\n"
        + "  }\n"
        +
        //json_printer
        "  void json_printer (gutil::JSONWriter& writer,\n"
        + "    gpelib4::EngineServiceRequest& _request,\n"
        + "    gapi4::UDFGraphAPI* graphAPI, bool verbose = false) const {\n"
        + "    writer.WriteStartArray();\n"
        + "    for (auto it = map.begin(); it != map.end(); it++) {\n"
        + "      writer.WriteStartObject();\n";

    for (TupleField tf : base) {
      code +=
          "      writer.WriteName(\""
              + tf.getName()
              + "\");\n"
              + "      json_printer_util("
              + "it->first";
      if (baseSize != 1) {
        code += "." + tf.getName();
      }
      code += ", writer, _request, graphAPI, verbose);\n";
    }
    for (TupleField tf : accum) {
      code +=
          "      writer.WriteName(\""
              + tf.getName()
              + "\");\n"
              + "      json_printer_util("
              + "it->second";
      if (accumSize != 1) {
        code += "." + tf.getName();
      }
      code += ", writer, _request, graphAPI, verbose);\n";
    }

    code +=
        "      writer.WriteEndObject();\n"
            + "    }\n"
            + "    writer.WriteEndArray();\n"
            + "  }\n";

    // get function
    // add const for get and constainskey to resolve warnings
    code += "  "
        + ((accumSize == 1)
        ? dType.ElementTypeList.get(baseSize).toCpp()
        : "accumTuple");
    code += " get ("
            + keySig
            + ") const {\n";
    code += CodeGenUtil.genGroupByMapReturn(baseSize, keyParam, "get")
            + "  }\n"
            // size function
            + "  int size () const {\n"
            + "    return map.size();\n"
            + "  }\n"
            +

            // containsKey function
            "  bool containskey ("
            + keySig
            + ") const {\n"
            + CodeGenUtil.genGroupByMapReturn(baseSize, keyParam, "containskey")
            + "  }\n"
            +

            // clear function
            "  void clear () {\n"
            + "    map.clear();\n"
            + "  }\n"
            +

            // remove functions
            "  void remove ("
            + keySig
            + ") {\n";

    code += (baseSize == 1)
        ? "    map.remove(" + keyParam + ");\n"
        : "    map.remove(baseTuple(" + keyParam + "));\n";

    code += "  }\n"
            + "  void remove ("
            + name
            + " const &" + CodeGenUtil.CPP_NAME_OTHER_ + ") {\n"
            + "    map.remove(" + CodeGenUtil.CPP_NAME_OTHER_ + ".map);\n"
            + "  }\n"
            +

            // begin end functions
            "  decltype(map.begin()) begin () {\n"
            + "    return map.begin();\n"
            + "  }\n"
            + "  decltype(map.end()) end () {\n"
            + "    return map.end();\n"
            + "  }\n"
            + "};\n\n";
    return code;
  }

  private static String genSerialize(Tuple tuple) {
    String field_list = "";
    for (Tuple.TupleField field : tuple.getFields()) {
      field_list += field.getName() + ", ";
    }
    field_list = field_list.substring(0, field_list.length() - 2);
    // use prefix to avoid conflict with accum name
    String archiveVar = NameChecker.RESERVED_PREFIX + "ar_";
    return
        "template <class ARCHIVE>\n"
            + "void serialize(ARCHIVE& " + archiveVar + ") {\n"
            + archiveVar + "(" + field_list + ");\n"
            + "}\n"
        ;
  }

  public static String genComparator(Variable variable, Set<String> comparatorSet) {
    return genComparator(variable.dtype, comparatorSet);
  }

  public static String genComparator(DataType dt, Set<String> comparatorSet) {
    String func = dt.heapComparatorFunction;
    if (func == null || func.isEmpty() || comparatorSet.contains(func)) {
      return "";
    }
    comparatorSet.add(func);

    // only heap accum reach here
    String tupleName = dt.ElementTypeList.get(0).Name;
    Tuple tuple = new Tuple(tupleName, dt.ElementTypeList.get(0));

    if (tuple == null) {
      throw new SystemException("System Error!! Cannot find tuple " + tupleName);
    }

    List<String> fields = new ArrayList<>();
    List<DataType> dts = new ArrayList<>();
    List<Boolean> ascs = new ArrayList<>();
    for (BooleanPair sortKey : dt.heapSortKey) {
      Tuple.TupleField field = null;
      boolean asc = true;
      for (Tuple.TupleField f : tuple.getFields()) {
        if (f.getName().equals(sortKey.getKey())) {
          field = f;
          asc = sortKey.getValue();
          break;
        }
      }
      if (field == null) {
        throw new SystemException("System Error!! Cannot find field " + sortKey);
      }

      fields.add(field.getName());
      dts.add(field.getType());
      ascs.add(asc);
    }
    return genComparator(func, fields, dts, ascs);
  }

  public static String genComparator(String funcName, List<String> fields,
                                     List<DataType> dts, List<Boolean> ascs) {
    StringBuilder body = new StringBuilder();
    for (int i = 0; i < dts.size(); ++i) {
      body.append(genCompareIfStatements(dts.get(i), fields.get(i), !ascs.get(i), true));
    }
    return
        "template <typename TUPLE_t>\n"
            + "class " + funcName + " {\n"
            + "bool _reverse;\n"
            + "public: \n"
            + funcName + "() : _reverse(false) {}\n"
            + funcName + "(bool reverse) : _reverse(reverse) {}\n"
            + "bool operator() (const TUPLE_t& lhs, const TUPLE_t& rhs) const {\n"
            + body
            + "return false;\n}\n};\n"
        ;
  }
}
