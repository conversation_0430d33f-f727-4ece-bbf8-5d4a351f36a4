/**
 * ****************************************************************************
 * Copyright (c) 2015-2016, TigerGraph Inc.
 * All rights reserved
 * Unauthorized copying of this file, via any medium is
 * strictly prohibited
 * Proprietary and confidential
 * ****************************************************************************
 */
package com.tigergraph.engine.codegen;

import com.tigergraph.engine.optim.PlanInfo;
import com.tigergraph.engine.parser.GSQLParser;
import com.tigergraph.engine.parser.GSQLParser.*;
import com.tigergraph.engine.parser.GSQLParserBaseListener;
import com.tigergraph.engine.typechecker.DataType;
import com.tigergraph.engine.typechecker.VarType;
import com.tigergraph.engine.util.Util;
import com.tigergraph.schema.plan.query.Argument;
import com.tigergraph.schema.plan.query.QueryInfo;
import com.tigergraph.schema.plan.query.QuerySignature;
import com.tigergraph.schema.topology.Graph;
import java.util.*;

import org.antlr.v4.runtime.Token;

/***
 * Extracts query signature into a Querysignature object (needed for endpoint generation).
 ***/
public class QuerySignatureExtractor extends GSQLParserBaseListener {
  private QuerySignature _querySig;
  private Graph _schema;

  private Map<String, QueryInfo> _queryList;
  private Map<String, DataType> _tupleMap;
  private VarType _varType;

  // saving the vertex types of each vertex set
  private Map<String, Set<String>> _vSetVar2TypesMap;

  private CommonVariable commonVariable;

  // GLE-2455, for read-only handler, we shouldn't modify any in-memory object
  private boolean fromReadOnlyHandler = false;

  public QuerySignatureExtractor(Graph schema, CommonVariable cv,
      Map<String, DataType> tupleMap,
      VarType varType, QuerySignature querySignature, List<PlanInfo> plans) {
    commonVariable = cv;
    _querySig = querySignature;
    // NOTE: The QuerySignature fields need to be set through functions to support references to the
    // parent class Signature elsewhere.
    _querySig.setName(cv.getQueryName());
    _querySig.setUsedSessionProperties(cv.UsedSessionProperties);
    _querySig.setGraphUpdate(cv.useGraphUpdate);
    _querySig.setReadDataList(cv.readDataList);
    _querySig.setCreateDataList(cv.createDataList);
    _querySig.setUpdateDataList(cv.updateDataList);
    _querySig.setDeleteDataList(cv.deleteDataList);
    _querySig.setPotentialEntityAccess(cv.potentialEntityAccess);
    _querySig.setNeedExprFunctions(cv.needExprFunctions);
    _querySig.setNeedTgExprFunctions(cv.needTgExprFunctions);
    _querySig.setDirectCallGSQLFunctions(cv.directCallGSQLFunctions);
    _querySig.setPolicyCallGSQLFuncNames(cv.policyCallGSQLFuncNames);
    // convert LinkedHashSet to ArrayList, keeping insert order
    _querySig.setIsGrantedToConstantRoleNames(new ArrayList<>(cv.isGrantedToConstantRoleNames));
    _querySig.setIndividualReqCtxt(cv.individualReqCtxt);
    _querySig.setAggregatedReqCtxt(cv.aggregatedReqCtxt);
    _querySig.plans.addAll(plans);
    _schema = schema;
    _queryList = cv.QueryMap;
    _tupleMap = tupleMap;
    _varType = varType;
    _vSetVar2TypesMap = new HashMap<String, Set<String>>();

    fromReadOnlyHandler = com.tigergraph.utility.RuntimeUtil.checkCallerHelper(
        "com.tigergraph.schema.handler.CodeCheckHandler");
  }

  public QuerySignature getQuerySig() {
    return _querySig;
  }


  //////////////////
  // listener rules
  /////////////////

  @Override
  public void exitStatementSeed(GSQLParser.StatementSeedContext ctx) {
    _vSetVar2TypesMap.put(ctx.lVSetVar().getText(), _varType.getTypeStringTreeNode(ctx));
  }

  @Override
  public void exitStatementBlock(GSQLParser.StatementBlockContext ctx) {
    _vSetVar2TypesMap.put(ctx.lVSetVar().getText(), _varType.getTypeStringTreeNode(ctx));
  }

  @Override
  public void exitStatementLVarAssignExpr(GSQLParser.StatementLVarAssignExprContext ctx) {
    String vSet = ctx.lhsIdent().getText();
    if (_varType.IsVSetVar(vSet)) {
      _vSetVar2TypesMap.put(vSet, _varType.getTypeStringTreeNode(ctx));
    }
  }

  @Override
  public void exitReturnClause(GSQLParser.ReturnClauseContext ctx) {
    for (ReturnTypeContext typeCtx : ctx.returnType()) {
      DataType dt;
      // when it is accum type of returning type
      if (typeCtx instanceof ReturnAccTypeContext) {
        dt = _varType.getType(typeCtx);
        // when it is type like query parameter type, call getDataTypeFromTypeStr
      } else if (typeCtx instanceof ReturnAnonymousTupleTypeContext) {
        dt = DataType.createAnonymousTupleDataType(
            ((ReturnAnonymousTupleTypeContext)typeCtx).anonymousTupleType(), null);
      } else {
        dt = DataType.getDataTypeFromTypeStr(typeCtx.getText(), _schema,
                typeCtx.getStart(), commonVariable);
      }
      _querySig.addReturnTypes(dt);
    }
  }

  @Override
  public void enterJob(GSQLParser.JobContext ctx) {
    _querySig.setName(commonVariable.getQueryName());
    //graph name
    _querySig.setGraphName(commonVariable.getGraphName());

    //SEM-5: semantic check of graph name
    //see error code in
    //semchecker/CheckVSetVarDependency.java
    if (!_querySig.GraphName.equals(_schema.GraphName)) {
      String msg = "The graph " + _querySig.GraphName
          + " does not exist in catalog!";
      //runtime exception will be thrown
      Util.error(ctx.graphName().getStart(), msg, 5,
          Util.ErrorType.SEMANTIC_ERROR, commonVariable);
    }

    _querySig.setArgs(new ArrayList<Argument>());
    _querySig.setApiVersion(commonVariable.JsonAPI);
    _querySig.setSyntaxV(commonVariable.SyntaxV);
  }

  @Override
  public void exitStatementReturn(GSQLParser.StatementReturnContext ctx) {
    // when query does not return
    if (_querySig.ReturnTypes.size() == 0 && ctx.expr() != null) {
      String msg = "The query '" + commonVariable.getQueryName()
          + "' with no return type cannot return a value.";
      Util.error(ctx.expr().getStart(), msg, 806,
          Util.ErrorType.SEMANTIC_ERROR, commonVariable);
    }

    if (ctx.expr() != null) {
      DataType returnDt = _varType.getType(ctx.expr());
      DataType signatureDT = _querySig.ReturnTypes.get(0);
      // In the case of returning vSetVar and query is returning set<vertex>/SetAccum<vertex>,
      // the returning vSetVar can only contain query signature specified vertex type.
      if ((returnDt.isVSetVar() && signatureDT.isVertexNut() == true
          && signatureDT.isSet() == true)) {
        String queryReturnVertexNutVertexType = signatureDT.getNut().Name;
        Set<String> vSetVertexTypes = _vSetVar2TypesMap.get(
            Util.stripExtraParens(ctx.expr()).getText());
        if (queryReturnVertexNutVertexType != null && vSetVertexTypes != null ) {
          for (String vertexType: vSetVertexTypes) {
            if (!vertexType.equals(queryReturnVertexNutVertexType)) {
              // type is mismatch
              String msg = "Type mismatch: vertex set " + ctx.expr().getText()
                  + " contains vertex type(s) " + vSetVertexTypes + ", but query return type is "
                  + signatureDT + ".";
              Util.error(ctx.expr().getStart(), msg, 806,
                  Util.ErrorType.TYPE_ERROR, commonVariable);
            }
          }
        }
      } else if (DataType.CompatibilityCheck(signatureDT, "=", returnDt) == null) {
        // type is mismatch
        String msg = "Type mismatch: cannot convert " + returnDt + " to query return type "
            + signatureDT + ".";
        Util.error(ctx.expr().getStart(), msg, 806, Util.ErrorType.TYPE_ERROR, commonVariable);
      }
      // return signature is anonymous tuple or collection of anonymous tuple
      if (signatureDT.isAnonymousTuple()) {
        signatureDT.anonymousTupleRealType = returnDt;
      } else if (signatureDT.Type.equals(DataType.Category.Accum)) {
        if ((signatureDT.Name.equals("SetAccum")
                || signatureDT.Name.equals("BagAccum")
                || signatureDT.Name.equals("ListAccum"))
                && signatureDT.ElementTypeList.get(0).isAnonymousTuple()) {
          signatureDT.ElementTypeList.get(0).anonymousTupleRealType
                  = returnDt.ElementTypeList.get(0);
        } else if (signatureDT.Name.equals("MapAccum")) {
          for (int idx = 0; idx < signatureDT.ElementTypeList.size(); idx++) {
            if (signatureDT.ElementTypeList.get(idx).isAnonymousTuple()) {
              signatureDT.ElementTypeList.get(idx).anonymousTupleRealType
                      = returnDt.ElementTypeList.get(idx);
            }
          }
        } else if (signatureDT.ElementTypeList.get(0).isAnonymousTuple()) {
          // block other accumulators
          String msg = "Only support SetAccum, BagAccum and ListAccum and MapAccum"
                  + "for collection of anonymous tuple.";
          Util.error(ctx.expr().getStart(), msg, 806,
                  Util.ErrorType.TYPE_ERROR, commonVariable);
        }
      }
    }
  }

  @Override
  public void exitFuncRval(GSQLParser.FuncRvalContext ctx) {
    String queryName = ctx.funcName().getText();
    // not function
    // not tuple
    // then query
    // make sure not null
    if (_varType.getBuiltInFunction(queryName) == null
        && _tupleMap.get(queryName) == null
        && _queryList.get(queryName) != null
        && commonVariable != null) {
      // add sub-query name and its signature
      _querySig.addSubQuery(queryName, _queryList.get(queryName).getQuerySignature());
      // the name of the caller query should not be null or empty string
      // for example, when the caller query is an interpreted one
      // in this case, we will not add the caller query
      if (!Util.EmptyOrNullString(_querySig.Name)) {
        if (!fromReadOnlyHandler) {
          // TODO: GLE-2455, for CodeCheckHandler and other read-only handlers from graphstudio,
          // we should not modify any in-memory object
          _queryList.get(queryName).CallerQueries.add(_querySig.Name);
        }
      }
      commonVariable.callQueries = true;
    }

    // If it is calling the query itself, need check the signature consistent with the calling.
    if (queryName.equals(commonVariable.getQueryName())) {
      QueryInfo queryInfo = new QueryInfo();
      queryInfo.GraphName = _schema.GraphName;
      queryInfo.QueryName = queryName;
      queryInfo.Arguments = _querySig.Args;
      queryInfo.ApiVersion = _querySig.ApiVersion;
      queryInfo.SyntaxV = _querySig.SyntaxV;
      queryInfo.UsedSessionProperties = commonVariable.UsedSessionProperties;
      queryInfo.batchMode = commonVariable.isBatchMode();

      queryInfo.ReturnTypeList = _querySig.ReturnTypes;
      queryInfo.CallerQueries = new HashSet<>();
      queryInfo.generateEndPointFileName();

      Util.checkQueryCallingQuerySignature(ctx, queryInfo, queryName, commonVariable, _varType);
    }
  }

  @Override
  public void exitParam(GSQLParser.ParamContext ctx) {
    String pName = new String(ctx.ident().getText());
    String pTy = ctx.parType().getText();

    String defaultVal = null;

    if (ctx.constant() != null) {
      defaultVal = Util.getDefaultValueInConstant(ctx.constant(), pTy);
    }

    Argument arg;
    boolean isSet = false;
    String containerName = null;
    if (pTy.startsWith("set") || pTy.startsWith("bag")) {
      isSet = true;
      containerName = pTy.substring(0, pTy.indexOf("<"));
      pTy   = pTy.substring(pTy.indexOf("<") + 1, pTy.lastIndexOf(">"));
    }

    boolean isList = false;
    if (pTy.toLowerCase().startsWith("list")) {
      isList = true;
      pTy = pTy.substring(pTy.indexOf("<") + 1, pTy.lastIndexOf(">"));
      // set flag for using list query parameter
      commonVariable.setUseListQueryParameterFlag();
    }

    String vName = null;
    if (pTy.startsWith("vertex") && pTy.indexOf("<") != -1) {
      vName = pTy.substring(pTy.indexOf("<") + 1,
          pTy.lastIndexOf(">"));
    }

    String eName = null;
    if (pTy.startsWith("edge") && pTy.indexOf("<") != -1) {
      eName = pTy.substring(pTy.indexOf("<") + 1,
          pTy.lastIndexOf(">"));
    }

    // special handling of map due to it has multiple argument types
    if (pTy.toLowerCase().startsWith("map")) {
      String keyParamTypeName = pTy.substring(pTy.indexOf("<") + 1, pTy.indexOf(","));
      String valParamTypeName = pTy.substring(pTy.indexOf(",") + 1, pTy.lastIndexOf(">"));
      // [GLE-8217]: temporarily block usage of 'vertex' as key or val type
      //             block usage of 'edge' as key or val type
      if (keyParamTypeName.toLowerCase().startsWith("vertex")
          || keyParamTypeName.toLowerCase().startsWith("edge")) {
        String msg = "Key type of Map query parameter can not be VERTEX/EDGE type.";
        Util.error(ctx.getStart(), msg, 806,
            Util.ErrorType.TYPE_ERROR, commonVariable);
      }
      if (valParamTypeName.toLowerCase().startsWith("vertex")
          || valParamTypeName.toLowerCase().startsWith("edge")) {
        String msg = "Value type of Map query parameter can not be VERTEX/EDGE type.";
        Util.error(ctx.getStart(), msg, 806,
            Util.ErrorType.TYPE_ERROR, commonVariable);
      }

      // set flag for using map query parameter
      commonVariable.setUseMapQueryParameterFlag();
      arg = new Argument(pName, 
          getArgType(keyParamTypeName, ctx.getStart()), 
          getArgType(valParamTypeName, ctx.getStart()),
          null, null, defaultVal, false, false, true, _varType.GetParamType(pName));
    } else {
      arg = new Argument(pName,
          getArgType(pTy, ctx.getStart()), null,
          vName, eName, defaultVal, isSet, isList, false, _varType.GetParamType(pName));
    }
    
    //SEM-6: check uniquness of params
    for (Argument a : _querySig.Args) {
      if (a.Name.equals(arg.Name)) {
        String msg = "The query parameter name \"" + a.Name
            + "\" is not unique!";
        //runtime exception will be thrown
        Util.error(ctx.getStart(), msg, 6,
            Util.ErrorType.SEMANTIC_ERROR, commonVariable);
      }
    }
    _querySig.addArgs(arg);

    //ANY type vertex
    if (pTy.startsWith("vertex") && vName == null) {
      arg = new Argument(pName + ".type",
          Argument.ArgType.STRING, null, null, null,
          null, isSet, isList, false, _varType.GetParamType(pName));
      _querySig.addArgs(arg);
    }

    // GLE-3042 EDGE type if not fully supported yet. Forbid user to create query
    if (pTy.startsWith("edge")) {
      String msg;
      if (isSet) {
        msg = String.format("Argument type '%s<EDGE>' is not supported yet!",
            containerName.toUpperCase());
      } else {
        msg = "Argument type 'EDGE' is not supported yet!";
      }
      Util.error(ctx.getStart(), msg, 11, Util.ErrorType.TYPE_ERROR, commonVariable);
    }

    // FILE parameter.
    if (pTy.toLowerCase().startsWith("file")) {
      commonVariable.usingFileOutput = true;
    }
  }

  @Override
  public void exitTupleTypeName(GSQLParser.TupleTypeNameContext ctx) {
    _querySig.addTupleName(ctx.getText());
  }

  @Override
  public void exitBitwiseAccType(GSQLParser.BitwiseAccTypeContext ctx) {
    //Only collect predefined typdef accumulator names
    if (ctx.accumSizeParam() == null) {
      _querySig.addGlobalTypedefAccumNames(ctx.bitwiseAccumType().getText());
    }
  }

  @Override
  public void exitGeneralAccType(GSQLParser.GeneralAccTypeContext ctx) {
    //Only collect predefined typdef accumulator names
    if (ctx.typeParams() == null) {
      _querySig.addGlobalTypedefAccumNames(ctx.ident().getText());
    }
  }

  @Override
  public void exitFileVar(GSQLParser.FileVarContext ctx) {
    // Gather deterministic file paths from the query.
    // The FILE variable declaration is supposed to have a single argument.
    // This is checked in OtherTypeCheck.exitFileVar method.
    ExprContext expr = ctx.funcParams().expr(0);
    if (expr instanceof NonLogicalExprContext
        && ((NonLogicalExprContext) expr).exprAtom() instanceof ExprConstantContext) {
      // Checking against the policy is already done in SemChecker.exitFileVar method.
    } else {
      commonVariable.usingFileOutput = true;
    }
  }

  // helper function
  private Argument.ArgType getArgType(String name, Token tok) {

    Argument.ArgType type = Argument.ArgType.toArgType(name);

    if (type == null) {
      String msg = "An unexpected argument type " + name;
      Util.error(tok, msg, 0,
          Util.ErrorType.SYSTEM_ERROR, commonVariable);
    }
    return type;
  }

  @Override
  public void enterSelectVertexFromFile(GSQLParser.SelectVertexFromFileContext ctx) {
    // flag for code gen
    commonVariable.usingFileInput = true;
  }

  @Override
  public void enterLoadAccmFromFile(GSQLParser.LoadAccmFromFileContext ctx) {
    // flag for code gen
    commonVariable.usingFileInput = true;
  }
}
