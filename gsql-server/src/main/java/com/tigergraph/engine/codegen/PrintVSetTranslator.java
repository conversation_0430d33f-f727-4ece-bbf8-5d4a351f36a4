/**
 * ****************************************************************************
 * Copyright (c) 2015-2016, TigerGraph Inc.
 * All rights reserved
 * Unauthorized copying of this file, via any medium is
 * strictly prohibited
 * Proprietary and confidential
 * ****************************************************************************
 */
package com.tigergraph.engine.codegen;

import com.tigergraph.engine.parser.GSQLParser;
import com.tigergraph.engine.parser.GSQLParser.*;
import com.tigergraph.engine.parser.GSQLParserBaseListener;
import com.tigergraph.engine.semchecker.DagController;
import com.tigergraph.engine.typechecker.DataType;
import com.tigergraph.engine.typechecker.VarType;
import com.tigergraph.engine.util.Pair;
import com.tigergraph.engine.util.UDFSettings;
import com.tigergraph.engine.util.Util;
import com.tigergraph.schema.topology.Attribute;
import com.tigergraph.schema.topology.Graph;
import com.tigergraph.schema.topology.VertexType;
import com.tigergraph.utility.CodeGenUtil;
import com.tigergraph.utility.StringUtil;

import java.util.*;

import org.antlr.v4.runtime.ParserRuleContext;
import org.antlr.v4.runtime.tree.ParseTreeProperty;

/** * Extracts query signature into a Querysignature object (needed for endpoint generation). * */
public class PrintVSetTranslator extends GSQLParserBaseListener {
  // the PRINT statement cpp code is associated with its PC (stnt level).
  // The PRINT that will be combined to same vertex map has same PC.
  //private Map<Integer, String> _PrintCppMap;
  private ParseTreeProperty<String> _PrintCppMap;

  // associates cpp code with expressions. computed previously
  ParseTreeProperty<String> _cppCode;

  // graph schema, for attribute access
  private Graph _schema;

  // annotates statement node with pc. computed in StatementCounter.
  ParseTreeProperty<Integer> _PCs;
  Integer _pc;

  // captures generated code
  private StringBuilder _udfCpp;

  public StringBuilder getPrintCpp() {
    return _udfCpp;
  }

  private Map<String, String> _writeCpp;

  // get from ExprAndCondTranslator
  private ParseTreeProperty<Map<String, Map<String, DataType>>> _AttLvarDeclMapTree;
  private ParseTreeProperty<Map<String, Map<String, String>>> _AttLvarAssignMapTree;
  // get from ExprAndCondTranslator BuildTypeMap, stores v/e types for each ctx
  private ParseTreeProperty<Map<String, Set<String>>> _canonical2Types;

  VarType _varType;

  // print in order if the DML block has OrderBy
  private Map<String, Integer> _printInOrderMap;

  private Map<Integer, Boolean> _needPrintInOrder;

  public Map<Integer, Boolean> getNeedPrintInOrder() {
    return _needPrintInOrder;
  }

  // UDFSettings
  private ParseTreeProperty<UDFSettings> _settings;

  // end print cpp code
  private Map<Integer, String> _EndPrintCpp;

  // map enum -> type cpp
  private Map<String, String> _gvEnum2Type;

  private DagController _dagController;

  private CommonVariable commonVariable;

  public PrintVSetTranslator(
      CommonVariable cv,
      Graph s,
      ParseTreeProperty<String> c,
      ParseTreeProperty<Integer> pc,
      ExprAndCondTranslator transl,
      ParseTreeProperty<UDFSettings> setting,
      VarType varType,
      Map<String, String> gvEnum2Type,
      DagController dagController) {

    commonVariable = cv;
    _schema = s;
    _cppCode = c;
    _PCs = pc;
    _settings = setting;
    _gvEnum2Type = gvEnum2Type;

    _PrintCppMap = new ParseTreeProperty<String>();
    _udfCpp = new StringBuilder();
    _writeCpp = new HashMap<String, String>();
    _printInOrderMap = new HashMap<String, Integer>();
    _needPrintInOrder = new HashMap<Integer, Boolean>();
    _EndPrintCpp = new HashMap<Integer, String>();

    _AttLvarDeclMapTree = transl.getAttLocalVarDeclMapTree();
    _AttLvarAssignMapTree = transl.getAttLocalVarAssignMapTree();
    _canonical2Types = transl.getCanonical2Types();

    _varType = varType;

    _dagController = dagController;
  }

  public ParseTreeProperty<String> getPrintCppMap() {
    return _PrintCppMap;
  }

  //////////////////
  // listener rules
  /////////////////
  @Override
  public void exitJob(GSQLParser.JobContext ctx) {

    this.genFixedWriteFunct(commonVariable.getQueryName());
    for (Map.Entry<String, String> entry : _writeCpp.entrySet()) {
      this.genWriteFunct(entry.getKey(), entry.getValue());
    }

    this.genEndPrintFunct();
    this.genMonitorWriteFunct();
  }

  @Override
  public void exitStatementBlock(GSQLParser.StatementBlockContext ctx) {
    String vSet = ctx.lVSetVar().getText();

    if (ctx.block().orderClause() == null) return;

    _printInOrderMap.put(vSet, _PCs.get(ctx));
    _needPrintInOrder.put(_PCs.get(ctx), false);
  }

  @Override
  public void exitStatementPrint(GSQLParser.StatementPrintContext ctx) {
    switch (commonVariable.JsonAPI) {
      case V2:
        runStatementPrintV2(ctx);
        break;
      default:
        Util.apiVersionError(ctx, commonVariable);
        break;
    }
  }

  public void runStatementPrintV2(GSQLParser.StatementPrintContext ctx) {
    _pc = _PCs.get(ctx);

    boolean hasVSetAccess =
        Util.MinusSet(
                    _dagController.GetStatementGraph().get(_pc).GetNeededVSet(),
                    _dagController.GetStatementGraph().get(_pc).GetParameterizedVSet())
                .size()
            > 0;

    // check if we have any assignment in _AttLvarAssignMapTree.get(ctx).get("write")
    // if true, let handleMultiTypes() not create assignment to avoid redeclaration
    // else, the assignment should be generated in handleMultiTypes()
    Map<String, String> attLvarAssignMap = _AttLvarAssignMapTree.get(ctx).get("write");
    boolean hasAssignment = (attLvarAssignMap != null && !attLvarAssignMap.isEmpty());

    // *******************************************
    // print to file
    // *******************************************
    if (ctx.rhsIdent() != null) {
      // use a flag to indicate the ostream has been declared
      boolean declared = false;
      for (int i = 0; i < ctx.printExpr().size(); ++i) {
        PrintExprContext pExpr = ctx.printExpr(i);

        // Separate form print2File, used for when the user wants to specifically
        // print a vSet attribute to file
        boolean printVSetAttrToCSV = Util.isPrintVSetAttrToCSV(ctx, pExpr, _varType);
        if (printVSetAttrToCSV) {
          runStatementPrintVSetAttrToCSV(ctx, hasVSetAccess, hasAssignment);
        } else {
          GSQLParser.ExprContext expr = pExpr.expr();
          // writeFunctionCpp is the cpp code that will be put into write functions
          String writeFunctionCpp = "";
          // beforeIterationCpp is the cpp code that will be put into BeforeIteration
          String beforeIterationCpp = "";
          String ostream = "";
          if (_varType.IsVSetVar(expr.getText())) {
            ostream = "(*ostream[i_ostream])";
            String vSetVar = expr.getText();
            boolean isFirst = true;
            for (VSetProjContext vSetProj : pExpr.vSetProj()) {
              if (isFirst) {
                isFirst = false;
              } else {
                writeFunctionCpp += ostream + " << file_sep_;\n";
              }

              String projCpp = _cppCode.get(vSetProj);
              DataType dt = _varType.getType(vSetProj);

              // when exprCpp is null handle multiple type is needed
              if (projCpp == null) {
                // special case, allow different data types
                writeFunctionCpp += handleMultiTypes(vSetProj.expr(), null, ostream, true,
                    hasAssignment);
              } else {
                // normal case
                writeFunctionCpp += printExprToFile(ostream, projCpp, dt, true) + ";\n";
              }
            }

            writeFunctionCpp = "    " + writeFunctionCpp + ostream + " << std::endl;\n"
                + "  i_ostream++;\n";

            saveWriteCpp(ctx, pExpr, _pc + "_" + _varType.GetPrintKeyStr(expr), writeFunctionCpp);

            String print = _PrintCppMap.get(pExpr);
            beforeIterationCpp = (print != null) ? print : "";
          } else {
            ostream = "ofs_"
                + Integer.toString(ctx.rhsIdent().getText().hashCode()).replace("-", "_");

            if (declared == false) {
              // only declare the ostream for once
              writeFunctionCpp +=
                  "gutil::GOutputStream "
                      + ostream
                      + "("
                      + _cppCode.get(ctx.rhsIdent())
                      + ".get());\n"
              ;
              declared = true;
            }
            String exprCpp = _cppCode.get(expr);
            DataType dt = _varType.getType(expr);
            writeFunctionCpp += printExprToFile(ostream, exprCpp, dt, true) + ";\n";

            if (i + 1 < ctx.printExpr().size()) {
              writeFunctionCpp = "    " + writeFunctionCpp + ostream + " << file_sep_;\n";
            } else {
              writeFunctionCpp = "    " + writeFunctionCpp + ostream + " << std::endl;\n";
            }

            beforeIterationCpp = writeFunctionCpp;
            writeFunctionCpp = "";
          }
          _PrintCppMap.put(pExpr, beforeIterationCpp);

        }
      }
      // *******************************************
      // print to json
      // *******************************************
    } else {
      for (PrintExprContext pExpr : ctx.printExpr()) {

        ParserRuleContext expr = Util.stripExtraParens(pExpr.expr());

        // writeFunctionCpp is the cpp code that will be put into write functions
        String writeFunctionCpp = "";
        // beforeIterationCpp is the cpp code that will be put into BeforeIteration
        String beforeIterationCpp = "";


        if (_varType.GetParamType(expr.getText()) != null
            && _varType.GetParamType(expr.getText()).isFile()
            || _varType.IsFile(expr.getText())) {
          // so far when print a file, we only print the file name, not the file content
          String value = "_" + (_varType.IsParameter(expr.getText()) ? expr.getText()
              : _varType.getCodeGenName(expr.getText(), ctx, "File")) + "->GetFileName()";
          boolean useStdOptional = _varType.getType(expr).isNullable();
          beforeIterationCpp += CodeGenUtil.getJsonWriteCpp(DataType.STRING_TYPE,
              _varType.GetPrintKeyStr(pExpr.expr()),value, ctx.getStart(),
              commonVariable, useStdOptional);
        } else if (!_varType.IsVSetVar(expr.getText())) {
          // normal case
          // Use `pExpr.expr()` for the print key because `expr` has its parentheses stripped.
          boolean useStdOptional = _varType.getType(expr).isNullable();
          beforeIterationCpp +=
              CodeGenUtil.getJsonWriteCpp(
                  _varType.getType(expr), _varType.GetPrintKeyStr(pExpr.expr()),
                  _cppCode.get(expr), ctx.getStart(), commonVariable, useStdOptional);
        } else {
          String vSetVar = expr.getText();
          boolean onlyVSet = pExpr.vSetProj().size() == 0;
          if (onlyVSet) {
            writeFunctionCpp = getWriteFunctionForVSet(vSetVar, ctx);

            if (_printInOrderMap.get(vSetVar) != null) {

              writeFunctionCpp =
                  "  gutil::JSONWriter jwriter;\n"
                      + "  gutil::JSONWriter& writer = "
                      + "context->GlobalVariable_GetValue<int32_t>(GV_SYS_"
                      + vSetVar
                      + "_ORDERBY) != -1"
                      + "? jwriter : *__GQUERY__local_writer;\n"
                      + writeFunctionCpp
                      +

                      // put json to hash map as string
                      "  if (context->GlobalVariable_GetValue<int32_t>(GV_SYS_"
                      + vSetVar
                      + "_ORDERBY) != -1) {\n"
                      + "    context->GlobalVariable_GetValue<"
                      + "MapAccum<VERTEX,string> >("
                      + "GV_SYS_"
                      + vSetVar
                      + "_Map).data_[v] = writer.str();\n"
                      + "    context->GlobalVariable_GetValue<SetAccum<VertexLocalId_t> >("
                      + "GV_SYS_output_vids).insert("
                      + "writer.mark_vids().begin(), writer.mark_vids().end());\n"
                      + "  }\n";

              // need print in order, set true then we will generate corresponding code
              _needPrintInOrder.put(_printInOrderMap.get(vSetVar), true);

              // generate endPrint code
              genEndPrint(vSetVar);

              beforeIterationCpp +=
                  "    context->GlobalVariable_GetValue<"
                      + "MapAccum<VERTEX,string> >("
                      + "GV_SYS_"
                      + vSetVar
                      + "_Map).clear();\n";

            } else {
              writeFunctionCpp =
                  "  gutil::JSONWriter& writer = *__GQUERY__local_writer;\n" + writeFunctionCpp;
            }

            // when more than one vSet is printing then it should be catched
            // by dagController. Else just remain the defaul format
            // default format: &UDF_p115::Write_3
            // dag-use format: &UDF_p115::Write_3_VSetName
            //saveWriteCpp(ctx, _pc + "_" + vSetVar, writeFunctionCpp);
            saveWriteCpp(ctx, pExpr, _pc + "_" + _varType.GetPrintKeyStr(expr), writeFunctionCpp);
          } else {
            for (VSetProjContext vSetProj : pExpr.vSetProj()) {
              String projCpp = _cppCode.get(vSetProj);
              Set<String> types = new HashSet<>();
              String ident = "";
              if (vSetProj.expr() instanceof NonLogicalExprContext) {
                NonLogicalExprContext nonCtx =  (NonLogicalExprContext)vSetProj.expr();
                if (nonCtx.exprAtom() instanceof  AllTupleFieldAccessContext) {
                  AllTupleFieldAccessContext tupleContext =
                      (AllTupleFieldAccessContext) nonCtx.exprAtom();
                  types = getTypeFromMap(vSetVar, tupleContext.exprAtom());
                  ident = tupleContext.ident().getText();
                }
              }
              // when exprCpp is null handle multiple type is needed
              if (projCpp == null) {
                // special case, allow different data types
                writeFunctionCpp += handleMultiTypes(vSetProj.expr(),
                    _varType.GetPrintKeyStr(vSetProj.expr()), null, false,
                    hasAssignment);
              } else {
                Boolean useStdOptional = false;
                Boolean isMultidefine = false;
                if (!types.isEmpty()) {
                  Pair<Boolean, Boolean> result =
                      DataType.checkifMultipleDefinitionRequired(_schema, types, ident);
                  isMultidefine = result.left;
                  useStdOptional = result.right;
                }
                // attribute var declaration has suffix _optional from 
                // ExprAndCondTranslator.buildAttLocalVarMap but projCpp is saved before
                // calling buildAttLocalVarMap.
                // suffix _optional is used because same attribute name and type can be defined as
                // both nullable and non nullable inside different vertex types
                if (isMultidefine) {
                  // projCpp will be generated as ( ts_set_SetAccumint64_t)
                  String varname = projCpp.replace("(", "").replace(")","");
                  String optionalVarname = varname + "_optional";
                  writeFunctionCpp += " if (" + varname + "_flag) {\n";
                  // normal case
                  writeFunctionCpp += CodeGenUtil.getJsonWriteCpp(_varType.getType(vSetProj),
                  _varType.GetPrintKeyStr(vSetProj.expr()),
                  varname, vSetProj.getStart(), commonVariable, false)
                      + "\n";
                  writeFunctionCpp += "}\n";
                  // generate else block for optional variable
                  writeFunctionCpp += " else if (" + optionalVarname + "_flag) {\n";
                  writeFunctionCpp += CodeGenUtil.getJsonWriteCpp(_varType.getType(vSetProj),
                  _varType.GetPrintKeyStr(vSetProj.expr()),
                  optionalVarname, vSetProj.getStart(), commonVariable, true);

                  writeFunctionCpp += " }";
                } else if (useStdOptional) {
                  // projCpp will look like  ( ts_set_SetAccumint64_t)
                  // LPAREN and RPAREN are added in ExprAndCondTranslator.exitNonLogicalExpr
                  String optionalVarname = projCpp.replace("(", "").replace(")","") + "_optional";
                  writeFunctionCpp += CodeGenUtil.getJsonWriteCpp(_varType.getType(vSetProj),
                  _varType.GetPrintKeyStr(vSetProj.expr()),
                  optionalVarname, vSetProj.getStart(), commonVariable, true)
                      + "\n";
                } else {
                  // normal case
                  writeFunctionCpp += CodeGenUtil.getJsonWriteCpp(_varType.getType(vSetProj),
                      _varType.GetPrintKeyStr(vSetProj.expr()),
                      projCpp, vSetProj.getStart(), commonVariable, false)
                          + "\n";
                }
              }
            }

            writeFunctionCpp =
                "   writer.WriteStartObject ();\n"
                    +

                    // write vertex id
                    "   writer.WriteName (\"v_id\").WriteMarkVId (v);\n"
                    + "   _request.output_idservice_vids.push_back (v);\n"
                    +

                    // write vertex type
                    CodeGenUtil.getJsonWriteCpp(
                        DataType.STRING_TYPE,
                        "v_type",
                        "context->GraphAPI()->GetVertexTypeName(v)",
                        ctx.getStart(), commonVariable, false)
                    + "\n"
                    + "\n"
                    + "   writer.WriteName (\"attributes\");\n"
                    + "   writer.WriteStartObject ();\n"
                    + "   "
                    + writeFunctionCpp
                    + "\n"
                    + "   writer.WriteEndObject ();\n"
                    + "   writer.WriteEndObject ();\n";

            if (_printInOrderMap.get(vSetVar) != null) {

              writeFunctionCpp =
                  "\n"
                      + "  gutil::JSONWriter jwriter;\n"
                      + "  gutil::JSONWriter& writer = "
                      + "context->GlobalVariable_GetValue<int32_t>(GV_SYS_"
                      + vSetVar
                      + "_ORDERBY) != -1"
                      + "? jwriter : *__GQUERY__local_writer;\n"
                      + writeFunctionCpp
                      +

                      // put json to hash map as string
                      "  if (context->GlobalVariable_GetValue<int32_t>(GV_SYS_"
                      + vSetVar
                      + "_ORDERBY) != -1) {\n"
                      + "    context->GlobalVariable_GetValue<MapAccum<VERTEX,string> >("
                      + "GV_SYS_"
                      + vSetVar
                      + "_Map).data_[v] = writer.str();\n"
                      + "    context->GlobalVariable_GetValue<SetAccum<VertexLocalId_t> >("
                      + "GV_SYS_output_vids).insert("
                      + "writer.mark_vids().begin(), writer.mark_vids().end());\n"
                      + "  }\n";

              // need print in order, set true then we will generate corresponding code
              _needPrintInOrder.put(_printInOrderMap.get(vSetVar), true);

              // generate endPrint code
              genEndPrint(vSetVar);

              beforeIterationCpp +=
                  "    context->GlobalVariable_GetValue<"
                      + "MapAccum<VERTEX,string> >("
                      + "GV_SYS_"
                      + vSetVar
                      + "_Map).clear();\n";

            } else {
              writeFunctionCpp =
                  "\n"
                      + "   gutil::JSONWriter& writer = *__GQUERY__local_writer;\n"
                      + writeFunctionCpp;
            }

            saveWriteCpp(ctx, pExpr, _pc + "_" + _varType.GetPrintKeyStr(expr), writeFunctionCpp);
          }
        }
        _PrintCppMap.put(pExpr, beforeIterationCpp);
      }
    }
  }


  private Set<String> getTypeFromMap(String alias, ParserRuleContext ctx) {
    if (StringUtil.isNullOrEmptyString(alias)) {
      return null;
    }

    // if in print, we should get "v"
    // o/w get origin normName: "src" or "tgt"
    String norm = !Util.InPrint(ctx) ? _varType.getNormalizeName(ctx, alias).getIsSrcOrTgt()
        : _varType.getNormalizeName(ctx, alias).getName();

    Map<String, Set<String>> map = _canonical2Types.get(ctx);
    if (map == null) return null;
    return map.get(norm);
  }

  /**
   * vSet projection is not supported when printing 'TO_CSV' so we
   * generate separate print code to support this when user wishes
   * to print vSet attributes
   *
   * @param ctx
   * @param hasVSetAccess
   * @param hasAssignment
   */
  private void runStatementPrintVSetAttrToCSV(
      StatementPrintContext ctx,
      boolean hasVSetAccess,
      boolean hasAssignment) {
    // writeFunctionCpp is the cpp code that will be put into write functions
    String writeFunctionCpp = "";
    // beforeIterationCpp is the cpp code that will be put into BeforeIteration
    String beforeIterationCpp = "";

    // *******************************************
    // print to file
    // *******************************************
    String ostream = "";

    if (hasVSetAccess) {
      ostream = "(*ostream[i_ostream])";
    } else {
      ostream =
          "ofs_" + Integer.toString(ctx.rhsIdent().getText().hashCode()).replace("-", "_");
      writeFunctionCpp +=
          "gutil::GOutputStream "
              + ostream
              + "("
              + _cppCode.get(ctx.rhsIdent())
              + ".get());\n";
    }

    int cnt = 0;
    for (int i = 0; i < ctx.printExpr().size(); i++) {
      PrintExprContext context = ctx.printExpr(i);
      if (_varType.IsVSetVar(context.expr().getText()) && ctx.rhsIdent() != null) {
        // when print vset to_csv f;
        // generate all the cpp code using all the attributes
        String varName = context.expr().getText();
        Set<String> typeSet = _varType.getVSetTypes(ctx, varName);
        for (String vertexType : typeSet) {
          for (Attribute attribute : commonVariable._schema.getVertexAttributes(vertexType)) {
            DataType dt = DataType.createAttributeDataType(attribute, commonVariable);
            String exprCpp = Util.getLVarName(varName, attribute.AttributeName, dt);
            if (cnt != 0) {
              writeFunctionCpp += ostream + " << file_sep_;\n";
            }
            cnt++;
            writeFunctionCpp += printExprToFile(ostream, exprCpp, dt, true) + ";\n";
          }
        }
        continue;
      }

      if (i != 0) {
        writeFunctionCpp += ostream + " << file_sep_;\n";
      }

      String exprCpp = _cppCode.get((ExprContext) ctx.printExpr(i).expr());
      DataType dt = _varType.getType(ctx.printExpr(i).expr());
      // when exprCpp is null handle multiple type is needed
      if (exprCpp == null) {
        // special case
        exprCpp = handleMultiTypes(ctx.printExpr(i).expr(), null, ostream, true,
            hasAssignment);
        writeFunctionCpp += exprCpp;
      } else {
        // normal case
        writeFunctionCpp += printExprToFile(ostream, exprCpp, dt, true) + ";\n";
      }
    } // end for

    writeFunctionCpp =
        "    "
            + writeFunctionCpp
            + ostream
            + " << std::endl;\n"
            + (hasVSetAccess ? "  i_ostream++;\n" : "");

    if (hasVSetAccess) {
      saveWriteCpp(ctx, null, _pc.toString(), writeFunctionCpp);

      String print = _PrintCppMap.get(ctx);
      beforeIterationCpp = (print != null) ? print : "";

    } else {
      beforeIterationCpp = writeFunctionCpp;
      writeFunctionCpp = "";
    }
    // if no write function was generated
    if (writeFunctionCpp.equals("")) {
      // add condition cpp to printing cpp
      if (ctx.condition() != null) {
        beforeIterationCpp =
            "\n   if ( "
                + _cppCode.get(ctx.condition())
                + " ) {\n"
                + beforeIterationCpp
                + "      }\n\n";
      }
      beforeIterationCpp = getAttDeclAssignCpp(ctx, null) + beforeIterationCpp;
    }

    _PrintCppMap.put(ctx, beforeIterationCpp);
  }

  /** ****************** helper functions * ****************** */
  private void genWriteFunct(String string, String cpp) {
    String write =
        "\n"
            + "\n"
            + "void Write_"
            + string
            + " ("
            + "gvector<gutil::GOutputStream*>&   ostream,\n"
            + this.getSpace(14)
            + "const VERTEX& v,\n"
            + this.getSpace(14)
            + "V_ATTR*           v_attr,\n"
            + this.getSpace(14)
            + "const V_VALUE&     v_val,\n"
            + this.getSpace(14)
            + "gpelib4::GlobalVariableContext* context) "
            + "{\n"
            + "  "
            + commonVariable.LOG_EngineHigh
            + "\"Enter function Write_"
            + string
            + " v: \" << v << std::endl;\n";

    if (cpp.contains("i_ostream")) {
      write += "  uint32_t i_ostream = 0;\n";
    }

    write +=
        cpp
            + "  "
            + commonVariable.LOG_EngineHigh
            + "\"Exit function Write_"
            + string
            + " v: \" << v << std::endl;\n"
            + "}\n";

    _udfCpp.append(write);
  }

  private void genFixedWriteFunct(String qname) {
    String write =
        "\n"
            + "\n"
            + "void (UDF_"
            + commonVariable.getQueryName()
            + "::*write) ("
            + "gvector<gutil::GOutputStream*>&   ostream,\n"
            + this.getSpace(22)
            + "const VERTEX& v,\n"
            + this.getSpace(22)
            + "V_ATTR*           v_attr,\n"
            + this.getSpace(22)
            + "const V_VALUE&     v_val,\n"
            + this.getSpace(22)
            + "gpelib4::GlobalVariableContext* context);\n";

    write +=
        "\n"
            + "\n"
            + "ALWAYS_INLINE\nvoid Write("
            + "gvector<gutil::GOutputStream*>&   ostream,\n"
            + this.getSpace(12)
            + "const VertexLocalId_t& v,\n"
            + this.getSpace(12)
            + "V_ATTR*           v_attr,\n"
            + this.getSpace(12)
            + "const V_VALUE&     v_val,\n"
            + this.getSpace(12)
            + "gpelib4::GlobalVariableContext* context) {\n"
            + this.getSpace(4)
            + "// ignore all PRINTs in the monitor mode\n"
            + this.getSpace(4)
            + "if (context->GlobalVariable_GetValue<bool> (MONITOR)) {\n"
            + this.getSpace(6)
            + "return;\n"
            + this.getSpace(4)
            + "}\n"
            + this.getSpace(4)
            + "(this->*(write))(ostream, VERTEX(v), v_attr, v_val, context);"
            + "\n}\n";
    _udfCpp.append(write);
  }

  private void genMonitorWriteFunct() {
    String write =
        "void WriteSummaryVis () {\n"
            + "  pthread_mutex_lock(&jsonWriterLock);\n"
            + "  auto final_code = finally([&] { pthread_mutex_unlock(&jsonWriterLock); });\n"
            + "  timer_.PrintVisualizeSummary(*__GQUERY__local_writer);\n"
            + "}\n";
    _udfCpp.append(write);
  }

  /** The EndPrint function is for print vSet in order. */
  private void genEndPrintFunct() {
    if (_EndPrintCpp.isEmpty()) return;

    String endPrint =
        "\n"
            + "\n"
            + "void EndPrint(MasterContext* context,\n"
            + "              gvector<std::string>& outputpaths) {\n"
            + "  uint32_t PC = context->GlobalVariable_GetValue<uint32_t> (GV_SYS_PC);\n"
            + "  switch (PC) {\n";

    for (Map.Entry<Integer, String> entry : _EndPrintCpp.entrySet()) {
      endPrint += "    case " + entry.getKey() + ":\n" + entry.getValue() + "    break;\n";
    }

    endPrint += "  }\n" + "}\n";

    _udfCpp.append(endPrint);
    _udfCpp.append(genEndPrintHelper());
  }

  /** generate part of EndPrint function */
  private void genEndPrint(String vSetVar) {

    String orderBy = "context->GlobalVariable_GetValue<int32_t>(GV_SYS_" + vSetVar + "_ORDERBY)";

    String cpp = _EndPrintCpp.get(_pc);
    if (cpp == null) cpp = "";

    cpp +=
        "                "
            + "if (context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) == \""
            + vSetVar
            + "\") {\n"
            + "                "
            + "  if ("
            + orderBy
            + " == -1)\n"
            + "     { break; }\n"
            + "  switch ("
            + orderBy
            + ") {\n";

    // since we don't know the type of HeapAccum, we switch all HeapAccum types print V;
    // the V is runtime since there may be override V block in if-else,
    // so the heap is also runtime
    // we get the global heap enum from a global var, and we switch it
    for (Map.Entry<String, String> entry : _gvEnum2Type.entrySet()) {
      if (!entry.getValue().startsWith("HeapAccum")) continue;

      String theHeap =
          "(context->GlobalVariable_GetValue<" + entry.getValue() + " > (" + entry.getKey() + "))";

      cpp +=
          "    case "
              + entry.getKey()
              + ":\n"
              + "      HF_EndPrint(context, GV_SYS_"
              + vSetVar
              + "_Map, "
              + theHeap
              + ", *__GQUERY__local_writer);\n"
              + "      break;\n";
    }

    cpp += "  }\n}\n";

    _EndPrintCpp.put(_pc, cpp);
  }

  /** EndPrint helper function */
  private String genEndPrintHelper() {

    String theMap = "(context->GlobalVariable_GetValue<MapAccum<VERTEX, string> >(pos))";
    String theVidSet = "(context->GlobalVariable_GetValue<SetAccum<VertexLocalId_t> >"
        + "(GV_SYS_output_vids))";

    return "template <typename HEAP>\n"
        + "void HF_EndPrint(MasterContext* context, uint32_t pos,\n"
        + "              HEAP& theHeap, gutil::JSONWriter& writer) {\n"
        + "  for(auto it = theHeap.begin(); it != theHeap.end(); ++it){\n"
        + "     const string& s = "
        + theMap
        + ".get(it->vid);\n"
        + "     if (s.empty()) continue;\n"
        + "     writer.WriteJSONContent(s, true);\n"
        + "   }\n"
        + "   _request.output_idservice_vids.insert"
        + "<decltype(" + theVidSet + ".begin())>"
        + "(_request.output_idservice_vids.end(),"
        + theVidSet
        + ".begin(), "
        + theVidSet
        + ".end());\n"
        + theVidSet
        + ".clear();\n"
        + "}\n\n";
  }

  private String getSpace(int n) {
    String str = new String();
    for (int i = 0; i < n; ++i) str += " ";
    return str;
  }

  /**
   * This function helps to handle a sepcial case for print exprList.
   *
   * <p>for this case, print X.att1, we allow X.att1 to be different data types. we generate
   * different code for different vertex types in a switch statement.
   *
   * @param ostream null for json output; o/w file output
   */
  private String handleMultiTypes(ParserRuleContext ctx, String keyName,
      String ostream, boolean goutputstream, boolean hasAssignment) {
    if (!(ctx instanceof NonLogicalExprContext)) {
      // should not hit
      throw new NullPointerException();
    }
    ctx = ((NonLogicalExprContext) ctx).exprAtom();
    // get the v/e types
    Set<String> types = _varType.getTypeStringTreeNode(ctx);
    String exprCpp = _cppCode.get(ctx);
    DataType dt = _varType.getType(ctx);
    String cpp = "";

    // avoid redeclaration
    if (!hasAssignment) {
      cpp = "\nint v_typeIDVar = context->GraphAPI()->GetVertexType(v);\n";
    }
    int isFirst = 0;
    for (String type : types) {
      String attrName = null;
      if (ctx instanceof AllTupleFieldAccessContext) {
        attrName = ((AllTupleFieldAccessContext) ctx).ident().getText();
      } else if (ctx instanceof VSetProjContext) {
        GSQLParser.ExprContext expr = ((VSetProjContext)ctx).expr();
        ExprAtomContext exprAtom = null;
        if (expr instanceof GSQLParser.NonLogicalExprContext) {
          exprAtom = ((GSQLParser.NonLogicalExprContext)expr).exprAtom();
        }
        if (exprAtom != null && exprAtom instanceof AllTupleFieldAccessContext) {
          attrName = ((AllTupleFieldAccessContext)exprAtom).ident().getText();
        }
      }
      boolean useStdOptional = true;
      if (attrName != null) {
        Attribute attr = _schema.getAttribute(type, attrName);
        useStdOptional = (attr != null) ? attr.IsNullable() : true;
        dt = DataType.createAttributeDataType(attr, commonVariable);
        exprCpp = dt.GetAttrCpp(attrName, type, true, commonVariable, useStdOptional);
      }

      cpp += (isFirst++ == 0 ? "  if (" : "else if (")
          + "v_typeIDVar == " + Util.GetVertexTypeVarName(type, commonVariable)
          + " ) {\n";

      if (ostream == null) {
        cpp += CodeGenUtil.getJsonWriteCpp(
            dt, keyName, exprCpp, ctx.getStart(), commonVariable, useStdOptional) + "\n";
      } else {
        cpp += printExprToFile(ostream, exprCpp, dt, goutputstream) + ";\n";
      }

      cpp += "  }";
    }
    cpp += "\n";

    return cpp.replace("\n", "\n  ");
  }

  /**
   * print expr to file, if the type is VERTEX and use GOutputStream, translate to exid
   */
  private String printExprToFile(String ostream, String expr, DataType dt, boolean goutputstream) {
    if (dt.isVertex() && goutputstream) {
      return ostream + ".WriteVertexId(" + expr + ")";
    } else if (dt.isDatetime() && goutputstream) {
      return ostream + " << gutil::GtimeConverter::seconds_to_datetime_string("
          + expr
          + ");\n";
    } else {
      return ostream + " << " + expr;
    }
  }

  /**
   * generate the variable name that retrieves value from attribute.
   * uint64_t ts_year_uint64_t or std::optional<uint64_t> ts_year_uint64_t_optional
   */
  private String convertToStdOptional(String key, DataType val) {
    boolean useStdOptional = key.contains("_optional");
    String elemCpp = val.toCpp();
    if (useStdOptional) return "std::optional<" + elemCpp + ">";
    return elemCpp;
  }

  /**
   * This function generates the attrbutes' local variables declaration and assignments c++ code.
   * @param ctx the StatementPrint context
   * @param pExpr one expression in StatementPrint context
   */
  private String getAttDeclAssignCpp(StatementPrintContext ctx, PrintExprContext pExpr) {

    // get the maps from property trees
    Map<String, DataType> _AttLvarDeclMap = _AttLvarDeclMapTree.get(ctx).get("write");
    Map<String, String> _AttLvarAssignMap = _AttLvarAssignMapTree.get(ctx).get("write");

    // validate check, return empty string if we
    // cannot find from the tree, or if no attribute is needed
    if (_AttLvarDeclMap == null || _AttLvarAssignMap == null
        // use the whole StatementPrintContext if pExpr is not provided
        || _settings.get(pExpr == null ? ctx : pExpr).PrintSetting == 0) {
      return "";
    }

    /** ** 1. declaration code generation ***** */
    String attDeclCpp = "\n   //attributes' local var declaration\n";
    // loop Map(tgt_att1_int --> DataType)
    for (Map.Entry<String, DataType> entry : _AttLvarDeclMap.entrySet()) {
      // type tgt_att1_type = default;
      // bool tgt_att1_type_flag = false;
      attDeclCpp += "   " + convertToStdOptional(entry.getKey(), entry.getValue())
          + " " + entry.getKey();
      String initVal = entry.getValue().getInitVal();
      if (initVal != null) {
        attDeclCpp += " = " + initVal;
      }
      attDeclCpp += ";\n";
      attDeclCpp += "   bool " + entry.getKey() + "_flag = false" + ";\n";
    }

    /** ** 2. assignments code generation ***** */
    String attAssignCpp = "\n";
    // define a Map(src --> Map(V1 --> c++ code) )
    Map<String, Map<String, String>> attMap = new HashMap<String, Map<String, String>>();

    // loop Map (src|V1 --> c++ code)
    for (Map.Entry<String, String> entry : _AttLvarAssignMap.entrySet()) {
      String[] tmp = entry.getKey().split("\\|", -1);
      String normName = tmp[0]; // src/e/tgt
      String vName = tmp[1]; // V1/V2

      Map<String, String> tmpMap = new HashMap<String, String>();
      // if src/e/tgt has a map in attMap already
      if (attMap.get(normName) != null) tmpMap = attMap.get(normName);

      // append code that already in the map
      String oldCpp = tmpMap.get(vName);
      if (oldCpp == null) oldCpp = "";

      tmpMap.put(vName, oldCpp + entry.getValue());
      attMap.put(normName, tmpMap);
    }

    // loop the map we just built, and generate the if/else statements for src/e/tgt
    for (Map.Entry<String, Map<String, String>> entry : attMap.entrySet()) {
      boolean isEdge = entry.getKey().equals("e");
      attAssignCpp += "   //get " + entry.getKey() + "'s attribute\n";
      String typeAPI = isEdge
              ? "e_attr->type()"
              : "context->GraphAPI()->GetVertexType(" + entry.getKey() + ")";
      //typeIDVarName is a temporal name for typeAPI used in the folliwng if/else if statement
      String typeIDVarName = Util.getAttrTypeIDVar(entry.getKey());
      attAssignCpp += "   int " + typeIDVarName  + " = "  + typeAPI + ";\n";
      int isFirst = 0;
      for (Map.Entry<String, String> codeEntry : entry.getValue().entrySet()) {
        // for getAttr(), no schema id is needed and key is empty
        if (codeEntry.getKey().isEmpty()) {
          attAssignCpp += (codeEntry.getValue()).replace("\n", "\n     ");
          continue;
        }
        String typeIdx = isEdge
            ? Util.GetEdgeTypeVarName(codeEntry.getKey(), commonVariable)
            : Util.GetVertexTypeVarName(codeEntry.getKey(), commonVariable);
        attAssignCpp += ( isFirst++ == 0 ? "     if (" : " else if (" )
                     + typeIDVarName
                     + " == "
                     + typeIdx + ") {\n       ";
        // get the assign code and format with space
        attAssignCpp += (codeEntry.getValue()).replace("\n", "\n     ") + "}";
      }
    }

    return attDeclCpp + attAssignCpp + "\n";
  }

  private String getAttrJsonWriterFields(String vSetVar, ParserRuleContext ctx) {
    String writeCpp = "";
    switch (commonVariable.JsonAPI) {
      case V2:
        writeCpp +=
              // write attributes
              "writer.WriteName (\"attributes\");\n"
              + "writer.WriteStartObject ();\n";
        break;
      default:
        Util.apiVersionError(ctx, commonVariable);
        break;
    }
    return writeCpp;
  }

  private String getWriteFunctionForVSet(String vSetVar, StatementPrintContext ctx) {
    String typeIDVar = Util.getAttrTypeIDVar(vSetVar);
    String result = "";
    int isFirst = 0;
    for (String vSetTy : _varType.getVSetTypes(ctx, vSetVar)) {
      VertexType vt = _schema.GetVertex(vSetTy);

      String writeCppForOneType = "";

      writeCppForOneType +=
          "writer.WriteStartObject();\n"
          +
          // write vertex name and id
          CodeGenUtil.getJsonWriteCpp(
              DataType.VERTEX_TYPE, "v_id", "v", ctx.getStart(), commonVariable, false)
          + "\n"
          +
          // write vertex type
          CodeGenUtil.getJsonWriteCpp(
              DataType.STRING_TYPE,
              "v_type",
              "context->GraphAPI()->GetVertexTypeName(v)",
              ctx.getStart(), commonVariable, false)
          + "\n"
          + getAttrJsonWriterFields(vSetVar, ctx);

      if (vt.Attributes != null) {
        for (Attribute att : vt.Attributes) {
          if (!att.internalAttribute()) {
            String attName = att.AttributeName;
            //Only print Attribute if this attribute is valid with the schema
            writeCppForOneType += "if ("
                + Util.GetVertexAttrVarName(vt.Name, attName, commonVariable)
                + " != -1) {\n";
            DataType dtype = DataType.createAttributeDataType(att, commonVariable);

            writeCppForOneType +=
                CodeGenUtil.getJsonWriteCpp(dtype, attName,
                    dtype.GetAttrCpp(attName, vt.Name, true, commonVariable, att.IsNullable()),
                    ctx.getStart(), commonVariable, att.IsNullable());
            //close the inner if statement for current attribute
            writeCppForOneType += "}\n";
          }
        }
      }

      // write vaccs
      // get all vAccNames saved in VarType.java and generate code for all vAccs in scope
      for (String vAccName : _varType.getVAccNames()) {
        if (!commonVariable.isTransformVertexAccumName(vAccName.replaceAll("@", ""))) {
          String originName = vAccName.replaceAll("@", "");
          // skip when vAccName is not declared in scope
          ParserRuleContext scopeCtx = _varType.GetAccScope(vAccName, ctx);
          if (scopeCtx == null) {
            continue;
          }

          String codeGenName = _varType.getCodeGenName(originName, ctx, "VACC");

          writeCppForOneType +=
              "writer.WriteName(\""
                  + vAccName
                  + "\");\nv_val"
                  + commonVariable.VValueAccessor()
                  + codeGenName
                  + ".json_printer(writer, _request, context->GraphAPI(), true);\n";
        }
      }

      writeCppForOneType += "writer.WriteEndObject ();\n" + "writer.WriteEndObject ();\n";
      String typeIDName = Util.GetVertexTypeVarName(vSetTy, commonVariable);
      result += (isFirst++ == 0 ? "if (" : " else if(")
              + typeIDName + " != -1 && "//typeIDName should be a valid type with schema
              + typeIDVar
              + " == "
              + typeIDName
              + ")  {"
              + ("\n" + writeCppForOneType).replace("\n", "\n     ")
              + "\n}";
    }

    result =
        "  int " + typeIDVar + " =  context->GraphAPI()->GetVertexType(v);\n"
            + ("\n" + result).replace("\n", "\n    ")
            + "\n";

    return result;
  }

  /**
   * Construct Write_pc or Write_pc_key function
   * @param ctx the StatementPrint context
   * @param pExpr one expression in StatementPrint context
   * @param key the write function name key
   * @param writeCpp
   */
  private void saveWriteCpp(StatementPrintContext ctx,
                            PrintExprContext pExpr,
                            String key,
                            String writeCpp) {
    if (ctx.condition() != null) {
      writeCpp = "\n   if ( " + _cppCode.get(ctx.condition()) + " ) {\n" + writeCpp + "      }\n\n";
    }
    boolean needLock = writeCpp.contains("__GQUERY__local_writer");
    String preWriteCpp = getAttDeclAssignCpp(ctx, pExpr);
    if (needLock) {
      preWriteCpp += "  pthread_mutex_lock(&jsonWriterLock);\n"
          + "  auto final_code = finally([&] { pthread_mutex_unlock(&jsonWriterLock); });\n";
    }
    writeCpp = preWriteCpp + writeCpp;

    _writeCpp.put(key, writeCpp);
  }
}

/**
 * ************ Appendix [Print]
 *
 * <p>The print is leveraging PC to print activate vertices from last iteration.
 *
 * <p>E.g.
 *
 * <p>X = SELECT V FROM ... //pc = 0 PRINT X.exp, X.@exp, @@A; //pc = 1 PRINT X.exp, X.@exp, @@A;
 * //pc = 1
 *
 * <p>while (condition ) limit k { //pc = 2 X2 = SELECT V FROM ... ; //pc = 3
 *
 * <p>PRINT X2.exp, X2.@exp,@@B; //pc = 4 }
 *
 * <p>PRINT X2.exp, X2.@exp, @@C; //pc = 5
 *
 * <p>We use beforeIteration to switch on PCs.
 *
 * <p>BeforeIteration(){
 *
 * <p>case 0 : { } //goto case 1 case 1 : { print } //goto case 2 case 2 : { } //decide 3 or 5
 * depending on runtime value of condition. case 3 : { } //goto case 4 case 4 : { print } //goto
 * case 2 case 5 : { print } //goto case 6 case 6 : { Stop(); }
 *
 * <p>}
 *
 * <p>In above, sequential print stmts that have vSet access have same pc. When we AddPrintJob, we
 * assign a write funtion pointer, and then do the printing.
 *
 * <p>Appendix [Print in order]
 *
 * <p>X = SELECT V FROM ... ORDER BY xxx; PRINT X; PRINT X.att, X.@accum;
 *
 * <p>For above statements, the DML block has an order by clause, so we need to print X and its
 * attributes in order. Since engine doen't support ordered bitset, we have to do the ordering
 * ourselves.
 *
 * <p>The trick is that in Write function, we print to a new JSONWriter instead of
 * _request.outputwriter_, and at the end we save the json to a hash map as a string. Then in
 * EndPrint we loop the heap and put the json string to request such that it has the order.
 *
 * <p>Since the WriteRaw doesn't has any format info, so we have to do WriteStartObject and
 * WriteEndObject in EndPrint not in Write function (as what is done by normal print).
 *
 * <p>_needPrintInOrder is an optimization, if a DML block has order by, but user doesn't print
 * the vSet out, then order by is useless, we will not generate the heap and so on, this will some
 * how improve the performanc.
 */
