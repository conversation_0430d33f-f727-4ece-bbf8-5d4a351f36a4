/**
 * ****************************************************************************
 * Copyright (c) 2015-2016, TigerGraph Inc.
 * All rights reserved
 * Unauthorized copying of this file, via any medium is
 * strictly prohibited
 * Proprietary and confidential
 * ****************************************************************************
 */
package com.tigergraph.engine.codegen;

import static com.tigergraph.engine.codegen.AccumTranslator.LIMIT_TUPLE_NAME_PREFIX;

import com.tigergraph.engine.parser.GSQLParser;
import com.tigergraph.engine.parser.GSQLParserBaseListener;
import com.tigergraph.engine.queryplanner.Tuple;
import com.tigergraph.engine.queryplanner.Tuple.TupleField;
import com.tigergraph.engine.typechecker.DataType;
import com.tigergraph.engine.typechecker.VarType;
import com.tigergraph.engine.util.Util;
import com.tigergraph.utility.CodeGenUtil;
import com.tigergraph.utility.StringUtil;

import java.util.*;

import org.antlr.v4.runtime.Token;
import org.antlr.v4.runtime.tree.ParseTreeProperty;

/**
 * Generates c++ struct definitions for tuple define in UDF mode
 * This includes both the explicit and implicit tuple define for top-k query.
 *
 * <p>We define two types tuple.
 *
 * <p>1. User-defined tuple: tuple is from the user's definition.
 *
 * <p>2. Order by clause + limit k clause needed implicit tuple: tuple name is
 * block_PC_hashCode(block_PC) with struct (uint vid, key1, key2, exp1, exp2..) fields, sort by
 * key1, and key2, followed by print statements using exp1,exp2 as base expression.
 *
 * <p>3. Limit k clause only needed implicit tuple: tuple name is block_PC_hashCode(block_PC) with
 * struct (uint vid, float randomValue, exp1, exp2) fields, sort by randomValue, followed by print
 * statements using exp1,exp2 as base expression.
 *
 * <p>See appendix to top-k query algorithm.
 */
public class TypeDefTranslator extends GSQLParserBaseListener {
  
  private StringBuilder _udfCpp;
  private ParseTreeProperty<Integer> _pcs;
  //only vertex Type tuple will be recorded by _tupleName2FieldList
  private Map<String, List<TupleField>> _tupleName2FieldList;

  public StringBuilder getStructDeclCpp() {
    return _udfCpp;
  }

  //all tuple related info including ddl and gquery typedefine tuples
  VarType _varType;

  private CommonVariable commonVariable;

  public TypeDefTranslator(
      CommonVariable cv,
      ParseTreeProperty<Integer> pcs,
      VarType varType) {
    commonVariable = cv;
    _pcs = pcs;
    _varType = varType;
    _udfCpp = new StringBuilder();
    _tupleName2FieldList = new HashMap<String, List<TupleField>>();
  }

  //////////////////
  // listener rules
  /////////////////
  @Override
  public void enterJob(GSQLParser.JobContext ctx) {
    //scan all collected tuple DataType from Query and DDL
    Map<String, DataType> tupleName2DataType = _varType.getTupleName2DTInSchemaAndQuery();
    for (Map.Entry<String, DataType> entry: tupleName2DataType.entrySet()) {
      String tupleName = entry.getKey();
      DataType tupleDt = entry.getValue();
      //skip the code generation if it is a global tuple
      if (tupleDt.isGlobalTuple()) {
        continue;
      }
      List<TupleField> fields = new ArrayList<TupleField>();
      int sz = tupleDt.ElementTypeList.size();
      for (int i = 0; i < sz; ++i) {
        fields.add(new TupleField(tupleDt.ElementTypeList.get(i),
            tupleDt.ElementTypeNames.get(i)));
      }
      AddTupleDefine(null, tupleName, fields, _udfCpp, true, true);
    }
  }

  /**
   * If existing order clause, construct the fields for the implicity limit k tuple for global heap.
   */
  @Override
  public void exitOrderClause(GSQLParser.OrderClauseContext ctx) {
    //implicit tuple name is : tupleLimitK_(hashCode)_(block_pc)
    String tupleName = LIMIT_TUPLE_NAME_PREFIX + _pcs.get(ctx.getParent().getParent());

    List<GSQLParser.OrderKeyContext> keyList = ctx.orderKey();

    List<TupleField> fields = new ArrayList<TupleField>();

    int i = 0;

    //order by tuple struct always have vid field.
    fields.add(new TupleField(DataType.VERTEX_TYPE, "vid", "asc"));

    for (GSQLParser.OrderKeyContext key : keyList) {
      // oder by can be "int" or XAccum<int>
      DataType baseType = _varType.getType(key.exprAtom()).getNut();
      String fieldName = "key_" + i;
      String direction = "asc";

      if (key.direction() != null) {
        direction = key.direction().getText();
      }

      fields.add(new TupleField(baseType, fieldName, direction));
      i++;
    }

    _tupleName2FieldList.put(tupleName, fields);

    // if no limit clause, generate tuple here
    if (((GSQLParser.BlockContext) ctx.getParent()).limitClause() == null) {
      AddTupleDefine(ctx.getStart(), tupleName);
      AddFunctor(tupleName);
    }
  }

  @Override
  public void exitLimitClause(GSQLParser.LimitClauseContext ctx) {
    //implicit tuple name is : tupleLimitK_(hashCode)_(block_pc)
    String tupleName = LIMIT_TUPLE_NAME_PREFIX + _pcs.get(ctx.getParent().getParent());

    //add fields.
    List<TupleField> fields = _tupleName2FieldList.get(tupleName);

    //does not have "order by" clause?
    if (fields == null) {
      fields = new ArrayList<TupleField>();
      fields.add(new TupleField(DataType.VERTEX_TYPE, "vid", "asc"));
      fields.add(new TupleField(DataType.FLOAT_TYPE, "randomValue", "asc"));
      _tupleName2FieldList.put(tupleName, fields);
    }

    //add tuple
    AddTupleDefine(ctx.getStart(), tupleName);
    AddFunctor(tupleName);
  }

  /** generate GroupByAccum struct */
  @Override
  public void exitGeneralAccType(GSQLParser.GeneralAccTypeContext ctx) {
    if (!"GroupByAccum".equals(ctx.ident().getText())) {
      return;
    }

    boolean baseTuple = true;
    List<TupleField> baseFields = new ArrayList<TupleField>();
    List<TupleField> accumFields = new ArrayList<TupleField>();
    // first build base tuple field
    List<TupleField> fields = baseFields;

    for (GSQLParser.TypeParamContext tpCtx : ctx.typeParams().typeParam()) {
      if (baseTuple
          && (tpCtx.accType() != null
            ||
            // AvgAccum is recognized as tupleTypeName
            tpCtx.tupleTypeName() != null
            &&
            (DataType.isImplicitAccumName(tpCtx.tupleTypeName().getText())
             //heapAccum may be nested in GroupByAccum/MapAccum
             || _varType.getTypedefAccumInSchemaAndQuery()
             .containsKey(tpCtx.tupleTypeName().getText())))) {
        //when first reach Accum Type
        baseTuple = false;
        //start to build accum tuple field
        fields = accumFields;
      }

      String fieldName = tpCtx.ident().getText();
      DataType dt = DataType.createTemplateDataType(fieldName + "_t");

      //for HeapAccum, we need to get heap size from the  the typeDef of the heap.
      //e.g.  we get heapsize 20 from commentHeap1
      //Typedef HeapAccum <commentTuple> (20, ts desc, length desc) commentHeap1;
      //GroupByAccum<int yy,  commentHeap1 topk_latest_comment> @@groupby_yy;
      if (!baseTuple && tpCtx.tupleTypeName() != null
          && _varType.getTypedefAccumInSchemaAndQuery()
          .containsKey(tpCtx.tupleTypeName().getText())) {
        dt.size =
          _varType.getTypedefAccumInSchemaAndQuery().get(tpCtx.tupleTypeName().getText()).size;
      }

      fields.add(new TupleField(dt, fieldName));
    }
    _udfCpp.append(genGroupByAccum(ctx, baseFields, accumFields));
  }

  //////////////////
  // helper functions
  /////////////////
  private void AddTupleDefine(Token tok, String tupleName) {
    List<TupleField> fields = _tupleName2FieldList.get(tupleName);
    AddTupleDefine(tok, tupleName, fields, _udfCpp, true, true);
  }

  // function override
  /**
   * Generates C++ code for a tuple with the given name and fields.
   * <p>
   * Note that a hash function for the tuple should not be generated if any field is not
   * hash-able. Currently this only applies to the internally generated {@code AccumTuple}
   * for {@code GroupByAccum} since it may contain container types such as {@code SetAccum},
   * {@code BagAccum} etc.
   *
   * @param tok parser token used for error reporting
   * @param tupleName name of the tuple to generate
   * @param fields list of fields defined within the tuple
   * @param builder instance of {@code StringBuilder} used for collecting the generated code
   * @param needTemplate boolean flag indicating whether a template header is needed
   * @param genHashFunction boolean flag indicating whether a hash function is needed
   */
  private void AddTupleDefine(Token tok, String tupleName, List<TupleField> fields,
      StringBuilder builder, boolean needTemplate, boolean genHashFunction) {

    if (fields == null) {
      String msg = "Tuple fields cannot be found! ";
      Util.error(tok, msg, 0, Util.ErrorType.SYSTEM_ERROR, commonVariable);
    }

    //gen template declaration
    if (needTemplate) {
      String template = "";
      for (TupleField field : fields) {
        if (field.getType().isTemplate()) {
          template += "typename " + field.getType().toCpp() + ", ";
        }
      }
      if (!template.isEmpty()) {
        template = template.substring(0, template.length() - 2);
        builder.append("template<" + template + ">\n");
      }
    }

    builder.append("struct " + tupleName + " {\n");

    //constructor header and body
    String init_body = "";

    String constr_header = "  " + tupleName + "(";
    String constr_body = "";

    String ostream_body = "";

    String equal_body = "";

    String cppTupleConvertor = "";

    String cppTupleConstr = "";

    // generate hash function for tuple
    String hashFunction = genHashFunction
                        ? CodeGenUtil.getTupleHashFunction(tupleName, fields)
                        : "";
    String jsonWriteNameFunction = CodeGenUtil.getTupleJsonWriteNameFunction();

    String combine_body = "";

    String json_body = "";

    String less_body = "";
    String greater_body = "";

    // list field separated with comma
    String field_list = "";

    for (int i = 0; i < fields.size(); i++) {
      DataType baseType = fields.get(i).getType();
      String fieldName = fields.get(i).getName();

      // For vertex and edge type fields in user defined tuple,
      // no initialization is needed.
      if (!baseType.isVertex()
          && !baseType.isEdge()
          && !baseType.isJsonObject()
          && !baseType.isJsonArray()
          && !baseType.isChar()) {
        init_body += "    " + fieldName + " = " + baseType.DefaultValue() + ";\n";
      }

      if (i == 0) {
        ostream_body = "    os<<\"[\";\n";
        equal_body = "    return\n";
      }

      if (baseType.isChar()) {
        // for UDT attribute char type
        constr_header += "std::string& " + fieldName + "_, ";
        constr_body +=
          "    std::memcpy("
            + fieldName
            + ", "
            + fieldName
            + "_.c_str(), "
            + baseType.size
            + ");\n";

        equal_body += StringUtil.padLeftSpaces(6,
            "strcmp(" + fieldName + ", " + CodeGenUtil.CPP_NAME_OTHER_
                + "." + fieldName + ") == 0");
        less_body += StringUtil.padLeftSpaces(6,
            "if (strcmp("
                + fieldName
                + ", " + CodeGenUtil.CPP_NAME_OTHER_ + "."
                + fieldName
                + ") < 0) return true;\n"
                + "      if (strcmp("
                + fieldName
                + ", " + CodeGenUtil.CPP_NAME_OTHER_ + "."
                + fieldName
                + ") > 0) return false;\n");
        greater_body += StringUtil.padLeftSpaces(6,
            "if (strcmp("
                + fieldName
                + ", " + CodeGenUtil.CPP_NAME_OTHER_ + "."
                + fieldName
                + ") > 0) return true;\n"
                + "      if (strcmp("
                + fieldName
                + ", " + CodeGenUtil.CPP_NAME_OTHER_ + "."
                + fieldName
                + ") < 0) return false;\n");
        combine_body += StringUtil.padLeftSpaces(6,
            "std::memcpy("
                + fieldName
                + ", " + CodeGenUtil.CPP_NAME_OTHER_ + "."
                + fieldName
                + ", "
                + baseType.size
                + ");\n");

      } else {
        constr_header += baseType.toCpp() + " " + fieldName + "_, ";
        constr_body += "    " + fieldName + " = " + fieldName + "_;\n";

        equal_body +=
            StringUtil.padLeftSpaces(6, fieldName + " == " + CodeGenUtil.CPP_NAME_OTHER_
                + "." + fieldName);
        if (baseType.Type.equals(DataType.Category.Primitive)) {
          less_body += StringUtil.padLeftSpaces(6,
              "if ("
                  + fieldName
                  + " < " + CodeGenUtil.CPP_NAME_OTHER_ + "."
                  + fieldName
                  + ") return true;\n"
                  + "      if ("
                  + fieldName
                  + " > " + CodeGenUtil.CPP_NAME_OTHER_ + "."
                  + fieldName
                  + ") return false;\n");
          greater_body += StringUtil.padLeftSpaces(6,
              "if ("
                  + fieldName
                  + " > " + CodeGenUtil.CPP_NAME_OTHER_ + "."
                  + fieldName
                  + ") return true;\n"
                  + "      if ("
                  + fieldName
                  + " < " + CodeGenUtil.CPP_NAME_OTHER_ + "."
                  + fieldName
                  + ") return false;\n");
        }

        combine_body += StringUtil.padLeftSpaces(6,
            fieldName
                + (baseType.isBool() ? " |" : (baseType.isVertex() ? "" : " +"))
                + "= " + CodeGenUtil.CPP_NAME_OTHER_ + "."
                + fieldName
                + ";\n");
      }

      String value = fieldName;
      if (baseType.isPrimitiveAccum() && !baseType.isBitwiseAccum()) {
        value += ".get_value()";
      }

      // This piece of code is inside outer level json_printer function,
      // so use graphAPI instead of context->GraphAPI() as the paramter when invoking
      // json_printer function.
      json_body +=
          CodeGenUtil.getJsonWriteCpp(commonVariable, baseType, fieldName, value, tok, false, false)
              .replace("\n", "\n      ");

      if (i < fields.size() - 1) {
        ostream_body += "    os<<\"" + fieldName + " \"<<m." + fieldName + "<<\"|\";\n";
        equal_body += " &&\n";
        field_list += fieldName + ", ";
      } else {
        ostream_body += "    os<<\"" + fieldName + " \"<<m." + fieldName + "<<\"]\";\n";
        equal_body += ";\n";
        less_body += "      return false;\n";
        greater_body += "      return false;\n";
        field_list += fieldName;
      }

      if (baseType.isChar()) {
        builder.append("  char " + fieldName + "[" + baseType.size + "];\n");
      } else {
        builder.append("  " + baseType.toCpp() + " " + fieldName + ";\n");
      }
    } //end for

    constr_header = constr_header.substring(0, constr_header.length() - 2) + "){\n";

    constr_body += "  }\n";

    builder.append("\n");
    //empty constructor
    builder.append("  " + tupleName + "() {\n" + init_body + "  }\n\n");

    //parameterized constructor
    builder.append(constr_header);
    builder.append(constr_body);

    // operator from tuple to std::tuple
    cppTupleConvertor = genCppTupleConverter(fields, (fields.size() == 1));
    builder.append(cppTupleConvertor);

    // copy constructor for std::tuple
    cppTupleConstr = (fields.size() > 1) ? genCppTupleConstr(tupleName, fields) : "";
    builder.append(cppTupleConstr);

    // goutputstream function, this can translate vertex id
    builder.append(
        "\n  friend gutil::GOutputStream& operator<<"
            + "(gutil::GOutputStream& os, const "
            + tupleName
            + "& m) {\n");
    builder.append(ostream_body + "      return os ;\n");
    builder.append("  }\n\n");
    // ostream function
    builder.append(
        "\n  friend std::ostream& operator<<"
            + "(std::ostream& os, const "
            + tupleName
            + "& m) {\n");
    builder.append(ostream_body + "      return os ;\n");
    builder.append("  }\n\n");

    // equal function
    builder.append("\n  bool operator==(" + tupleName + " const &"
        + CodeGenUtil.CPP_NAME_OTHER_ + ") const {\n");
    builder.append(equal_body);
    builder.append("  }\n\n");

    // combine function
    builder.append("\n  " + tupleName + "& operator+=( const " + tupleName
        + "& " + CodeGenUtil.CPP_NAME_OTHER_ + ") {\n");
    builder.append(combine_body);
    builder.append("    return *this;\n");
    builder.append("  }\n\n");

    // hash function (with 2-space indentation)
    if (!hashFunction.isEmpty()) {
      builder.append(StringUtil.padLeftSpacesBatch(2, hashFunction));
    }

    // json_printer function
    builder.append(
        "\n  void json_printer "
            + "(gutil::JSONWriter& writer, gpelib4::EngineServiceRequest& _request,\n"
            + "gapi4::UDFGraphAPI* graphAPI, bool verbose = false) const {\n");
    builder.append("\n    writer.WriteStartObject();\n");
    builder.append(json_body);
    builder.append("\n    writer.WriteEndObject();\n");
    builder.append("  }\n\n");

    builder.append(StringUtil.padLeftSpacesBatch(2, jsonWriteNameFunction));

    // less function
    builder.append("\n  bool operator<(" + tupleName + " const &"
        + CodeGenUtil.CPP_NAME_OTHER_ + ") const {\n");
    builder.append(less_body);
    builder.append("  }\n\n");
    // greater function
    builder.append("\n  bool operator>(" + tupleName + " const &"
        + CodeGenUtil.CPP_NAME_OTHER_ + ") const {\n");
    builder.append(greater_body);
    builder.append("  }\n\n");

    // serialize function
    builder.append(
        "\n  template <class ARCHIVE>\n"
            + "   void serialize(ARCHIVE& " + CodeGenUtil.toCppName("ar") + ") {\n"
            + "     " + CodeGenUtil.toCppName("ar") + " ("
            + field_list
            + ");\n"
            + "   }\n\n");

    builder.append("};\n\n");
  }

  private static String genCppTupleConverter(List<TupleField> fields, boolean isOneElement) {
    StringJoiner types = new StringJoiner(", ");
    StringJoiner members = new StringJoiner(",");
    for (Tuple.TupleField field : fields) {
      DataType baseType = field.getType();
      String fieldName = field.getName();
      types.add(baseType.toCpp());
      members.add(fieldName);
    }
    if (isOneElement) {
      return
          "  operator " + types.toString() + "() const {\n"
              + "    return " + members.toString() + ";\n"
              + "}\n\n";
    }
    return
        "  operator std::tuple<" + types.toString()
            + ">() const {\n"
            + "    return std::make_tuple("
            + members.toString()
            + ");\n"
            + "  }\n\n";
  }

  private static String genCppTupleConstr(String tupleName, List<TupleField> fields) {
    StringJoiner types = new StringJoiner(", ");
    StringBuilder body = new StringBuilder();
    int index = 0;
    for (Tuple.TupleField field : fields) {
      DataType baseType = field.getType();
      String fieldName = field.getName();
      types.add(baseType.toCpp());
      body.append("    ");
      body.append(fieldName);
      body.append(" = std::get<");
      body.append(index);
      body.append(">(" + CodeGenUtil.CPP_NAME_OTHER_ + ");\n");
      index++;
    }

    return
        "  " + tupleName
            + "(const std::tuple<"
            + types.toString()
            + ">& " + CodeGenUtil.CPP_NAME_OTHER_ + ") {\n"
            + body.toString()
            + "  }\n";
  }

  /**
   * Only Limit k clause will call this function. For explicit global heap of tuple, we generate
   * comparison functor in AccumTranslator.java
   */
  private void AddFunctor(String tupleName) {

    List<TupleField> fields = _tupleName2FieldList.get(tupleName);
    _udfCpp.append("\ntemplate <typename TUPLE_t>\n");
    _udfCpp.append("class " + tupleName + "Compare {\n");
    _udfCpp.append("  public: \n");
    // a fake constructor, for reverse compare, but order by doesn't use
    _udfCpp.append("    " + tupleName + "Compare(bool = false) {} \n");
    _udfCpp.append("    bool operator() (const TUPLE_t& lhs, const TUPLE_t& rhs) const {\n");

    boolean first = true;
    for (TupleField field : fields) {
      //skip vid field
      if (first) {
        first = false;
      } else {
        // default is asc, op is '<'; desc op is '>'
        String op = field.isAsc() ? " < " : " > ";
        String reverseOp = field.isAsc() ? " > " : " < ";
        String fieldName = field.getName();
        _udfCpp.append("      if (lhs." + fieldName + op + "rhs." + fieldName + ") {\n");
        _udfCpp.append("        return true; \n");
        _udfCpp.append("      }\n");
        _udfCpp.append("      if (lhs." + fieldName + reverseOp + "rhs." + fieldName + ") {\n");
        _udfCpp.append("        return false; \n");
        _udfCpp.append("      }\n");
      }
    }

    _udfCpp.append("     return false;\n");
    _udfCpp.append("    }\n");
    _udfCpp.append("};\n\n");
  }

  /**
   * Generates GroupByAccum struct. Even this struct is very similar to the tuple's, there are few
   * different places, so we generate separatley.
   *
   * <p>For a given GroupByAccum type, it could have m base types and n accumulator types. We
   * generate the struct with templates, but the structs are different for different (m,n). We also
   * append the name of each field to struct name, the reason is 1. when user uses name to access a
   * field we have to do a translation since we may use same struct (w/ templates) if we don't
   * append field name 2. in print, we need to print field name exactly as user gives in the
   * defination. So we generate a GroupByAccum struct for each (m,n) combination and field names.
   *
   * <p>The signature is
   *
   * <p>template <typename x_t, typename xx_t, class xxx_t, class xxxx_t> struct
   * GroupByAccum_m_x_xx_n_xxx_xxxx { struct baseTuple { x_t x; xx_t xx; } struct accumTuple { xxx_t
   * xxx; xxxx_t xxxx; } MapAccum<baseTuple, accumTuple> map;
   *
   * <p>GroupByAccum_m_n () { } GroupByAccum_m_n (x_t base_1, xx_t base_2, xxx_t accum_1, xxxx_t
   * accum_2) { map = MapAccum<baseTuple, accumTuple> (baseTuple(base_1, base_2),
   * accumTuple(accum_1, accum_2)); } }
   *
   * note: If only a single value or key is used in the GroupByAccum, the direct datatype is used
   * instead of baseTuple or accumTuple.
   */
  private String genGroupByAccum(
      GSQLParser.GeneralAccTypeContext ctx, List<TupleField> base, List<TupleField> accum) {

    DataType groupByDataType = _varType.getType(ctx);
    int baseSize = base.size();
    int accumSize = accum.size();

    String name = "GroupByAccum_" + baseSize;
    // If single key or value is used, we append the datatype to the name. This is to avoid
    // duplicate GroupByAccum struct declarations in the C++ generated code.
    // i.e. "GroupByAccm<int d, SumAccum<int> s group> @@group2" and
    // "GroupByAccum<int d, AvgAccum s> @@group3" will both generate and use
    // "struct GroupByAccum_1_d_1_s". This would be okay with multiple keys and values, since
    // we use a container tuple class, "accumTuple" and "baseTuple", for the parameter datatypes
    // so the class is shared, but in the case of a single key or value, the datatypes are
    // accessed directly and this tuple is not used. So we need to generate a different struct for
    // each GroupByAccum using single key or value with different parameter types.
    if (baseSize == 1) {
      name += "_" + groupByDataType.ElementTypeList.get(0).Name;
    }
    for (TupleField tf : base) {
      name += "_" + tf.getName();
    }

    name += "_" + accumSize;
    // See comment above
    if (accumSize == 1) {
      name += "_" + groupByDataType.ElementTypeList.get(baseSize).Name;
      // Nested GroupByAccum will have same issue
      // i.e. GroupByAccum<int d, string s, GroupByAccum<int i, SumAccum<int> s> group> @@group2;
      //  GroupByAccum<int d, string s, GroupByAccum<int i, AvgAccum s> group> @@group3;
      if ("GroupByAccum".equals(groupByDataType.ElementTypeList.get(baseSize).Name)) {
        name += "_" + groupByDataType.ElementTypeList.get(baseSize).GroupByTypeCpp();
      } else {
        // Same issue for other accumulator types
        // i.e. GroupByAccum<INT a, SumAccum<INT> b> @@sumAccum;
        // GroupByAccum<INT a, SumAccum<UINT> b> @@sumAccum2;
        for (DataType dt : groupByDataType.ElementTypeList.get(baseSize).ElementTypeList) {
          name += "_" + dt.Name;
        }
      }
    }
    for (TupleField tf : accum) {
      name += "_" + tf.getName();
    }

    if (commonVariable.GroupByAccumSet.contains(name)) {
      return "";
    }
    commonVariable.GroupByAccumSet.add(name);

    String template = "";
    for (TupleField tf : base) {
      template += "typename " + tf.getName() + "_t, ";
    }
    for (TupleField tf : accum) {
      template += "class " + tf.getName() + "_t, ";
    }
    template = template.substring(0, template.length() - 2);

    // struct signature
    String code = "template<" + template + ">\n" + "struct " + name + " {\n";
    // sub-structs
    StringBuilder sb = new StringBuilder();
    if (baseSize != 1) {
      // no need to generate the template header since it already exists for GroupByAccum
      AddTupleDefine(ctx.getStart(), "baseTuple", base, sb, false, true);
    }
    if (accumSize != 1) {
      // note that hash function should not be generated for accumTuple
      // no need to generate the template header since it already exists for GroupByAccum
      AddTupleDefine(ctx.getStart(), "accumTuple", accum, sb, false, false);
    }
    code += sb.toString();

    String keySig = "";
    String keyParam = "";

    // If single Key value is used, we use the DataType directly as the key in the map
    if (baseSize == 1) {
      keySig = groupByDataType.ElementTypeList.get(0).toCpp() + " base_0, ";
      keyParam = "base_0, ";
    } else {
      for (int i = 0; i < baseSize; ++i) {
        keySig += base.get(i).getName() + "_t base_" + i + ", ";
        keyParam += "base_" + i + ", ";
      }
    }
    keySig = keySig.substring(0, keySig.length() - 2);
    keyParam = keyParam.substring(0, keyParam.length() - 2);

    String valSig = "";
    String valParam = "";
    // If single Accumulator value is used, we use the DataType directly
    // in the value field of the map.
    if (accumSize == 1) {
      // baseSize will be the index of the single accum value, since it appears after
      // the base types in 'ElementTypeList'
      valSig = groupByDataType.ElementTypeList.get(baseSize).toCpp() + " accum_0, ";
      valParam += "accum_0, ";
    } else {
      for (int i = 0; i < accumSize; ++i) {
        valSig += accum.get(i).getName() + "_t accum_" + i + ", ";
        valParam += "accum_" + i + ", ";
      }
    }
    valSig = valSig.substring(0, valSig.length() - 2);
    valParam = valParam.substring(0, valParam.length() - 2);


    //resize the generated heapAccum
    String heapaccum = "";
    for (int i = 0; i < accumSize; ++i) {
      if (accum.get(i).getType().size != -1) {
        heapaccum += "accum_" + i + ".resize(" + accum.get(i).getType().size + ");\n";
      }
    }

    code += "  "
        + CodeGenUtil.genGroupByMapDefinition(baseSize, accumSize, groupByDataType)
        + "map;\n\n";

    code += "  "
        //constructor
        + name
        + "() { }\n\n"
        + "  "
        + name
        + "("
        + keySig
        + ", "
        + valSig
        + ") {\n"
        + heapaccum
        + "    map = ";

    code += CodeGenUtil.genGroupByMapDefinition(baseSize, accumSize, groupByDataType)
        + " (";

    code += (baseSize == 1)
        ? keyParam + ", "
        : "baseTuple(" + keyParam + "), ";
    code += (accumSize == 1)
        ? valParam + ");\n"
        : "accumTuple(" + valParam + "));\n";

    code += "  }\n\n"
        // serialize function
        + "  template <class ARCHIVE>\n"
            + "  void serialize(ARCHIVE& " + CodeGenUtil.toCppName("ar") + ") {\n"
            + "    " + CodeGenUtil.toCppName("ar") + " (map);\n"
            + "  }\n"
            +

            //operators function
            "  friend std::ostream& operator<<"
            + "(std::ostream& os, const "
            + name
            + "& m) {\n"
            + "    os << m.map;\n"
            + "    return os ;\n"
            + "  }\n"
            + "  bool operator==("
            + name
            + " const &" + CodeGenUtil.CPP_NAME_OTHER_ + ") const {\n"
            + "    return map == " + CodeGenUtil.CPP_NAME_OTHER_ + ".map;\n"
            + "  }\n"
            + "  bool operator!=("
            + name
            + " const &" + CodeGenUtil.CPP_NAME_OTHER_ + ") const {\n"
            + "    return map != " + CodeGenUtil.CPP_NAME_OTHER_ + ".map;\n"
            + "  }\n"
            + "  "
            + name
            + " operator+ ("
            + name
            + " const &" + CodeGenUtil.CPP_NAME_OTHER_ + ") {\n"
            + "    "
            + name
            + " newG;\n"
            + "    newG += *this;\n"
            + "    newG += " + CodeGenUtil.CPP_NAME_OTHER_ + ";\n"
            + "    return newG;\n"
            + "  }\n"
            + "  void operator= ("
            + name
            + " const &" + CodeGenUtil.CPP_NAME_OTHER_ + ") {\n"
            + "    map = " + CodeGenUtil.CPP_NAME_OTHER_ + ".map;\n"
            + "  }\n"
            + "  void operator+=("
            + name
            + " const &" + CodeGenUtil.CPP_NAME_OTHER_ + ") {\n"
            + "    map += " + CodeGenUtil.CPP_NAME_OTHER_ + ".map;\n"
            + "  }\n"
            +
            //json_printer
            "  void json_printer (gutil::JSONWriter& writer,\n"
            + "    gpelib4::EngineServiceRequest& _request,\n"
            + "    gapi4::UDFGraphAPI* graphAPI, bool verbose = false) const {\n"
            + "    writer.WriteStartArray();\n"
            + "    for (auto it = map.begin(); it != map.end(); it++) {\n"
            + "      writer.WriteStartObject();\n";

    for (TupleField tf : base) {
      code +=
          "      writer.WriteName(\""
              + tf.getName()
              + "\");\n"
              + "      json_printer_util("
              + "it->first";
      if (baseSize != 1) {
        code += "." + tf.getName();
      }
      code += ", writer, _request, graphAPI, verbose);\n";
    }
    for (TupleField tf : accum) {
      code +=
          "      writer.WriteName(\""
              + tf.getName()
              + "\");\n"
              + "      json_printer_util("
              + "it->second";
      if (accumSize != 1) {
        code += "." + tf.getName();
      }
      code += ", writer, _request, graphAPI, verbose);\n";
    }

    code +=
        "      writer.WriteEndObject();\n"
            + "    }\n"
            + "    writer.WriteEndArray();\n"
            + "  }\n";


    // get function
    code += "  const "
        + ((accumSize == 1)
        ? groupByDataType.ElementTypeList.get(baseSize).toCpp()
        : "accumTuple");
    code += "& get ("
        + keySig
        + ") {\n";
    code += CodeGenUtil.genGroupByMapReturn(baseSize, keyParam, "get")
            + "  }\n"
            // size function
            + "  int size () const {\n"
            + "    return map.size();\n"
            + "  }\n"
            +

            // containsKey function
            "  bool containskey ("
            + keySig
            + ") {\n"
            + CodeGenUtil.genGroupByMapReturn(baseSize, keyParam, "containskey")
            + "  }\n"
            +

            // clear function
            "  void clear () {\n"
            + "    map.clear();\n"
            + "  }\n"
            +

            // remove functions
            "  void remove ("
            + keySig
            + ") {\n";

    code += (baseSize == 1)
        ? "    map.remove(" + keyParam + ");\n"
        : "    map.remove(baseTuple(" + keyParam + "));\n";

    code += "  }\n"
            + "  void remove ("
            + name
            + " const &" + CodeGenUtil.CPP_NAME_OTHER_ + ") {\n"
            + "    map.remove(" + CodeGenUtil.CPP_NAME_OTHER_ + ".map);\n"
            + "  }\n"
            +

            // begin end functions
            "  decltype(map.begin()) begin () {\n"
            + "    return map.begin();\n"
            + "  }\n"
            + "  decltype(map.end()) end () {\n"
            + "    return map.end();\n"
            + "  }\n"
            + "};\n\n";
    return code;
  }
}
/**
 * [APPENDIX] top-k
 *
 * <p>This is the flow to generate topk global heap.
 *
 * <p>- TypeDefTranslator.java 1. generate cpp tuple{} definition as before.
 *
 * <p>- AccumTranslator.java 2. exitGlobalAccDecl(): generate tuple comparison functor.
 *
 * <p>- MapReduceGenerator.java 3. Within DML block GACC assignment. EdgeMap_i (Using GV->Reduce()
 * for accumulator accum or assignment).
 *
 * <p>- BeforeIteration.java 4. Statment-level GACC assignment. That is, between DML blocks. Using
 * GV->Reduce(). This is only for user-declared gv heap tuple, not for "order by limit k."
 *
 * <p>- GlobalVariableTranslator.java 5. Declare enum for GVs. enum GVs{ }
 */
