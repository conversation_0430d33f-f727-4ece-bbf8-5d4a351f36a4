/**
 * ****************************************************************************
 * Copyright (c) 2015-2016, TigerGraph Inc.
 * All rights reserved
 * Unauthorized copying of this file, via any medium is
 * strictly prohibited
 * Proprietary and confidential
 * ****************************************************************************
 */
package com.tigergraph.engine.codegen;

import static com.tigergraph.engine.codegen.ExprAndCondTranslator.FLAG_SUFFIX;

import com.tigergraph.engine.parser.GSQLParser;
import com.tigergraph.engine.parser.GSQLParserBaseListener;
import com.tigergraph.engine.queryplanner.QueryPlanner;
import com.tigergraph.engine.queryplanner.expression.AttributeDynamicExpression;
import com.tigergraph.engine.queryplanner.expression.Variable;
import com.tigergraph.engine.typechecker.DataType;
import com.tigergraph.engine.typechecker.VarType;
import com.tigergraph.engine.util.CodeBuilder;
import com.tigergraph.engine.util.Pair;
import com.tigergraph.engine.util.Util;
import com.tigergraph.schema.CatalogManager;
import com.tigergraph.schema.plan.function.FunctionInfo;
import com.tigergraph.schema.plan.packages.Packages;
import com.tigergraph.schema.plan.query.QuerySignature;
import com.tigergraph.schema.security.fileinputoutput.FileInputOutputPolicy;
import com.tigergraph.utility.StringUtil;

import javax.annotation.Nullable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Generate C++ UDF constructor, which initializes private member variables from request object, and
 * private member variable declarations.
 * <p>
 * This class generates C++ UDF constructor for both GPR and UDF code generation.
 */
public class UdfConstructorGenerator extends GSQLParserBaseListener {
  private QueryPlanner queryPlanner;
  // the class name
  private String className;

  public UdfConstructorGenerator(QueryPlanner qp, CommonVariable cv) {
    commonVariable = cv;
    queryPlanner = qp;
    className = "GPR_" + queryPlanner.queryName;
  }

  public Pair<CodeBuilder, CodeBuilder> getConstrCode(boolean includeProfiler) {
    CodeBuilder hpp = new CodeBuilder();
    CodeBuilder cpp = new CodeBuilder();
    // if there is a graph update, the graph update commit should run in run(),
    // so the manager should define it as a class member.
    String managerAssign = "";
    if (commonVariable.isBatchMode() && queryPlanner.hasGraphUpdate()) {
      managerAssign = ", manager(_serviceapi, &_request)";
    }
    StringBuilder signature = new StringBuilder();
    signature.append("GPR_" + queryPlanner.queryName + " (gpelib4::EngineServiceRequest& request, ")
        .append("gperun::ServiceAPI& serviceapi, bool is_worker = false)");
    hpp.append(signature + ";");
    cpp.append(signature + ": "
        + "_request(request), _serviceapi(&serviceapi)"
        + managerAssign + " {\n"
        + "isQueryCalled = false;\n"
    );
    if (includeProfiler) {
      // Only enable profiler in MAIN query, not SUB query.
      cpp.append("profiler_enabled_ = _request.IsProfilerActivated();\n");
      if (!managerAssign.isEmpty()) {
        cpp.append("if (profiler_enabled_) {\n"
            + "  manager.SetProfilerEnabled();\n"
            + "}\n");
      }
    }

    // Set the value for "jsonAPIVersion_" here, then no need to worry about it later.
    cpp.append("_request.SetJSONAPIVersion(\"" + commonVariable.JsonAPI.getVersion() + "\");\n");

    // TODO monitoring parameter
    // TODO dynamic evaluator parameter

    // for file input policy
    if (commonVariable.usingFileInput) {
      cpp.append(FileInputOutputPolicy.getInputPolicy().generateFilePolicyConstructorCpp());
    }
    // for file output policy
    if (commonVariable.usingFileOutput) {
      cpp.append(FileInputOutputPolicy.getOutputPolicy().generateFilePolicyConstructorCpp());
    }

    // the index of vertex/edge types and attributes
    //assume that constructor always use "serviceapi" as the input argument
    //name of gperun::ServiceAPI& type, _setIndexValueStr has this assumption
    // NOTICE: all update to UsedSchemaTypeNameSet should be made before this.
    cpp.append(commonVariable.getSchemaInitialization());

    // query parameters
    for (Variable param : queryPlanner.params) {
      final String paramDefault = param.initialValue != null
          ? param.initialValue.toCpp(null, commonVariable)
          : null;
      cpp.append(initQueryParamClassMember(param.name, param.dtype, paramDefault));
    }

    // context functions:
    // current_roles(): private member _current_roles initialization
    if (commonVariable.aggregatedReqCtxt.isNeedCurrentRoles()) {
      cpp.append(getCurrentRolesValidationCode());
    }
    // is_granted_to_current_roles(): private member caches initialization
    if (!commonVariable.isGrantedToCacheNameMap.isEmpty()) {
      cpp.append(getIsGrantedToCacheConstrCode());
    }

    // for file obj separator
    cpp.append(
        "if (request.jsoptions_.isMember (\"__FILE_OBJ_SEP__\")) {\n"
            + "  file_sep_ = request.jsoptions_[\"__FILE_OBJ_SEP__\"][0].asString ()[0];\n"
            + "}\n");

    // when it is a query been called redirect its json message to nowhere
    cpp.append("\n__GQUERY__local_writer = _request.outputwriter_;\n");
    cpp.append("enumMapper_ = serviceapi.GetEnumMappers();\n");
    cpp.append(GprLogUtil.genConstrCode());
    cpp.append("__disable_filter_ = request.jsoptions_.isMember(\"__disable_filter__\");\n");
    cpp.append(genVertexTypeSet());

    // add attribute position info map for getAttr() if applicable
    if (!commonVariable.VertexTypesGetAttrConst.isEmpty()
        || !commonVariable.EdgeTypesGetAttrConst.isEmpty())
      cpp.append(getAttrPosVarInitCpp(commonVariable));

    // [GLE-4544] initialize package library info map in GPR main query constructor
    if (commonVariable.hasCalledGSQLFunction()) {
      cpp.append(genPKGLibMapInitCpp());
      cpp.append(genAppliedGSQLFuncPtrAndCache(commonVariable));
    }

    cpp.append("}");

    return new Pair<>(hpp, cpp);
  }

  public static String genVertexTypeSet() {
    String cpp =
        "if (request.jsoptions_.isMember (\"__GQUERY__\")\n"
            + "    && request.jsoptions_[\"__GQUERY__\"].isMember (\"vTypeIdList\")) {\n"
            + "  for (uint32_t i=0; i < request.jsoptions_[\"__GQUERY__"
            + "\"][\"vTypeIdList\"].size(); i++) {\n"
            + "    __GQUERY__vts_ += (uint32_t)request.jsoptions_[\"__GQUERY__"
            + "\"][\"vTypeIdList\"][i].asUInt();\n"
            + "  }\n"
            + "} else {\n"
            + "  addAllVertexTypesToSet(__GQUERY__vts_);\n"
            + "}\n";
    return cpp;
  }

  /**
   * Generates C++ code for initializing GPR/UDF class member variables
   * corresponding to query parameters in the GPR/UDF class constructor.
   *
   * Supported query parameter types are file, vertex, primitive types,
   * or set/bag/list whose element type is vertex or primitive types
   * or map whose key/val element type is vertex or primitive types.
   *
   * The supported primitive types are int, uint, float, double, string, datetime, bool.
   *
   * @param paramName    name of the query parameter
   * @param paramType    GSQL data type of the query parameter
   * @param paramDefault default value of the query parameter (optional)
   * @return the generated C++ code
   */
  private String initQueryParamClassMember(
      String paramName,
      DataType paramType,
      @Nullable String paramDefault) {
    // handle file parameters
    if (paramType.isFile()) return initFileParamClassMember(paramName);
    // get the inner types (map parameter has two inner types)
    final boolean isSetOrList = paramType.isSet() || paramType.isList();
    final boolean isMap = paramType.isMap();
    List<DataType> innerDataTypes = new ArrayList<>();
    if (isMap) {
      innerDataTypes.add(paramType.ElementTypeList.get(0));
      innerDataTypes.add(paramType.ElementTypeList.get(1));
    }
    if (isSetOrList) {
      innerDataTypes.add(paramType.ElementTypeList.get(0));
    } else {
      innerDataTypes.add(paramType);
    }

    // handles primitive, vertex, set/bag/list of primitive or vertex
    // 1. parameter type is vertex or set<vertex> / bag<vertex> / list<vertex> 
    if (innerDataTypes.size() == 1 && innerDataTypes.get(0).isVertex()) {
      return initVertexParamClassMember(paramName, innerDataTypes.get(0).Name, isSetOrList);
    }
    // parameter type is map<vertex, primitive>, map<primitive, vertex>, map<vertex, vertex>
    /* comment out since this milestone will not implement
    if (innerDataTypes.size() == 2 
        && (innerDataTypes.get(0).isVertex() || innerDataTypes.get(1).isVertex())) {
      // TODO: special handle of such map entry
      // return initMapParamWithVertexClassMember(paramName, innerDataTypes);
    }
    */

    // 2. parameter type is primitive or set<primitive> / bag<primitive> / list<primitive>
    //   parameter type is map<primitive, primitive>
    boolean hasDefault = (paramDefault != null);
    String initialValue = null;
    if (!isSetOrList && !isMap) initialValue = hasDefault ? paramDefault : paramType.DefaultValue();
    return initPrimitiveParamClassMember(
        paramName, innerDataTypes, isSetOrList, isMap, hasDefault, initialValue);
  }

  /**
   * Helper function to generate cpp code to validate `_request.current_role_infos_`.
   * Precondition: caller need to check `needCurrentRoles` flag in `commonVariable`.
   * 
   * @return cpp: cpp code for initialization of _current_roles
   */
  protected String getCurrentRolesValidationCode() {
    String cpp = "";
    // [GLE-3762] context function: current_roles()
    cpp +=  "// context function that requires current roles\n"
          + String.format("if (%s == nullptr) {\n", ExprGen.REQUEST_CURRENT_ROLES_SHARED_PTR)
          + "  std::string msg(\"Missing current roles information!\");\n"
          + "  HF_set_error(request, msg, true);\n"
          + "  return;\n"
          + "}\n";
    return cpp;
  }
  // [GLE-3763] context function: is_granted_to_current_roles(roleName)
  // initialize cache bool variable in GPR mode
  protected String getIsGrantedToCacheConstrCode() {
    String cpp = "// context function: is_granted_to_current_roles cache\n";
    // [GLE-3763] context function is_granted_to_current_roles()
    // initialize caches for is_granted_to_current_roles(<string_literal>)
    for (String cacheVarName : Util.asSortedList(commonVariable.isGrantedToCacheNameMap.keySet())) {
      String roleNameCppStr = commonVariable.isGrantedToCacheNameMap.get(cacheVarName);
      cpp += String.format("%s = "
          + "(%s->find(%s) != %s->end());",
          cacheVarName, ExprGen.REQUEST_CURRENT_ROLES_SHARED_PTR, roleNameCppStr,
          ExprGen.REQUEST_CURRENT_ROLES_SHARED_PTR);
    }
    return cpp;
  }

  public String getDestructorCode() {
    return "~" + className + " () {}\n\n";
  }

  // variable type
  private VarType _varType;

  // declaration of private attributes corresponding to job parameters
  String _privateAttribDeclCpp;

  // initialization code run in UDF constructor to set these attributes
  private String _privateAttribInitCpp;

  // this is where generated code goes
  StringBuilder _udfCpp;

  Map<String, DataType> _paramTyMap;

  private Map<String, String> _globalParsedExpressions;

  // cpp code of UDF members holding the dynamic expressions
  private String _dynamicExprDeclCpp;
  // cpp code for initializing each dynamic expression in UDF constructor
  private String _dynamicExprInitCpp;

  String _queryName;

  private QuerySignature _querySignature;

  String _publicAttribDeclCpp;

  // initialization code run in UDF query call constructor
  String _queryCallAttribInitCpp;

  // for the query calling interface all parameter shall pass as argument
  String _queryCallParamCpp;
  CommonVariable commonVariable;

  public UdfConstructorGenerator(CommonVariable cv,
                                 StringBuilder u,
                                 QuerySignature querySignature,
                                 VarType varType,
                                 Map<String, String> globalParsedExpressions) {

    commonVariable = cv;
    _varType = varType;
    _privateAttribDeclCpp = "";
    _privateAttribInitCpp = "";
    _udfCpp = u;
    //_setIndexValueStr = enumGen.getIndexValueStr();
    //_setIndexValueStr = "";
    _paramTyMap = varType.getParamTyMap();
    _globalParsedExpressions = new HashMap<String, String>(globalParsedExpressions);
    _dynamicExprDeclCpp = "";
    _dynamicExprInitCpp = "";

    _publicAttribDeclCpp = "";
    _queryCallAttribInitCpp = "";
    _queryCallParamCpp = "";

    _querySignature = querySignature;
  }

  public String getPrivAttrDeclCpp() {
    return _privateAttribDeclCpp;
  }

  public String getPublicAttrDeclCpp() {
    return _publicAttribDeclCpp;
  }
  //////////////////
  // listener rules
  /////////////////
  @Override
  public void enterJob(GSQLParser.JobContext ctx) {
    _queryName = commonVariable.getQueryName();
    _privateAttribDeclCpp = "private:\n";
  }

  @Override
  public void exitJob(GSQLParser.JobContext ctx) {
    _privateAttribDeclCpp += "\n   gpelib4::EngineServiceRequest& _request;\n";

    _privateAttribDeclCpp += "\n   gperun::ServiceAPI* _serviceapi;\n";

    // If contains graph update, declare shared_ptr<GraphUpdates> graphupdates
    // as private member.
    if (commonVariable.useGraphUpdate || commonVariable.callQueries) {
      _privateAttribDeclCpp += "\n   topology4::GraphUpdatesPointer graphupdates;\n";
    }

    _privateAttribDeclCpp += "   bool isQueryCalled;// true if this is a sub query\n";
    _privateAttribDeclCpp += "   pthread_mutex_t jsonWriterLock;\n";
    _privateAttribDeclCpp += "   string raisedErrorMsg_;\n";
    _privateAttribDeclCpp += "   gse2::EnumMappers* enumMapper_;\n";
    _privateAttribDeclCpp += "   bool monitor_;\n";
    _privateAttribDeclCpp += "   bool monitor_all_;\n";
    _privateAttribDeclCpp += "   char file_sep_ = ',';\n";
    _privateAttribDeclCpp += "   __GQUERY__Timer timer_;\n";
    _privateAttribDeclCpp += "   bool __GQUERY__all_vetex_mode;\n";
    _privateAttribDeclCpp += "   gutil::JSONWriter* __GQUERY__local_writer;\n";
    _privateAttribDeclCpp += "   gutil::JSONWriter __GQUERY__local_writer_instance;\n";
    // vertex type ids get from restpp
    _privateAttribDeclCpp += "   SetAccum<uint32_t> __GQUERY__vts_;\n";
    // define all schema usage as int variable to boost the access of map[]
    _privateAttribDeclCpp += commonVariable.getSchemaDeclaration();
    _privateAttribDeclCpp += "   V_VALUE* vvalptr = nullptr;\n";
    // declare these for simplifying foreach item definition
    _privateAttribDeclCpp += this._varType.generateForeachReturnTypeDefinition();

    // [GLE-4544] add private attribute `_pkglibs_map` when there are gsql function
    // called by the query.
    if (commonVariable.hasCalledGSQLFunction()) {
      _privateAttribDeclCpp += "   // Package library map. "
                            + "key: package name, val: pointer to package object\n";
      _privateAttribDeclCpp += "   ghash_map<std::string, PKGLib*> _pkglibs_map;\n";
      for (String gsqlFuncName : commonVariable.getAllCalledGSQLFunctionNames()) {
        _privateAttribDeclCpp += String.format("   %s %s = nullptr;\n",
            StringUtil.getGSQLFuncPtrTypeName(gsqlFuncName),
            StringUtil.getGSQLFuncCppPtrName(gsqlFuncName));
        _privateAttribDeclCpp += String.format("   gvector<bool>* %s = nullptr;\n",
            StringUtil.getGSQLFuncCppIsGrantedCache(gsqlFuncName));
      }
    }

    _privateAttribInitCpp +=
        "  if (request.jsoptions_."
            + "isMember(\"__GQUERY__USING_ALL_ACTIVE_MODE\") "
            + "&& request.jsoptions_[\"__GQUERY__USING_ALL_ACTIVE_MODE\"][0]."
            + "asString()[0] == 't') {\n"
            + "    __GQUERY__all_vetex_mode = true;\n"
            + "  } else {\n"
            + "    __GQUERY__all_vetex_mode = false;\n"
            + "  }\n";

    _queryCallAttribInitCpp += "__GQUERY__all_vetex_mode = false;\n";

    // add the member values for returning if this query has return value
    if (_querySignature.ReturnTypes != null & _querySignature.ReturnTypes.size() != 0) {
      _publicAttribDeclCpp += "public:\n";
      for (int i = 0; i < _querySignature.ReturnTypes.size(); i++) {
        _publicAttribDeclCpp +=
            _querySignature.ReturnTypes.get(i).toCpp() + " __GQUERY__rtnVal_" + i + ";\n";
      }
    }

    _privateAttribDeclCpp += _dynamicExprDeclCpp;

    // declare members for getAttr() if necessary
    _privateAttribDeclCpp += getAttrPosVarDeclareCpp(commonVariable);


    // [GLE-3763] delcare prviate member in UDF class for is_granted cache
    if (!commonVariable.isGrantedToCacheNameMap.isEmpty()) {
      for (String cacheVarName :
           Util.asSortedList(commonVariable.isGrantedToCacheNameMap.keySet())) {
        _privateAttribDeclCpp += "   bool " + cacheVarName + " = false;\n";
      }
    }
    // declare file input policy
    if (commonVariable.usingFileInput) {
      _privateAttribDeclCpp += "   " + FileInputOutputPolicy.getInputPolicy().generateDeclrCpp();
    }
    // declare file output policy
    if (commonVariable.usingFileOutput) {
      _privateAttribDeclCpp += "   " + FileInputOutputPolicy.getOutputPolicy().generateDeclrCpp();
    }

    // this will be printed out now
    String ret = genUdfConstr("UDF_" + _queryName);
    _udfCpp.append(ret);
  }

  @Override
  public void exitFileVar(GSQLParser.FileVarContext ctx) {
    String varName = ctx.ident().getText();
    // if fileName is not a parameter, get its codeGenName for current ctx
    if (!_varType.IsParameter(varName)) {
      varName = _varType.getCodeGenName(varName, ctx, "File");
    }
    _privateAttribDeclCpp += "gshared_ptr<gutil::GOutputStreamBase> _" + varName + ";\n";
  }

  @Override
  public void exitParam(GSQLParser.ParamContext ctx) {

    String pName = ctx.ident().getText();

    DataType dt = _paramTyMap.get(pName);
    String pTy = dt.toCpp();

    // collect query calling constructor param information
    // 1. parameter of constructor
    _queryCallParamCpp += ", " + dt.toCpp() + " " + pName;
    // 2. value assignment statements
    _queryCallAttribInitCpp += "_" + pName + " = " + pName + ";\n";
    // for subquery call, the parameter is always given
    _queryCallAttribInitCpp += pName + "_flag = true;\n";
    String defaultVal = ctx.constant() != null ? Util.getConstantCpp(ctx.constant()) : null;
    _privateAttribInitCpp += initQueryParamClassMember(pName, dt, defaultVal);

    if (dt.isSet()) {
      // set parameters
      DataType element = dt.ElementTypeList.get(0);
      pTy = element.toCpp();
      _privateAttribDeclCpp += "   " + dt.Name + "<" + pTy + "> _" + pName + ";\n";
      // parameter flag, indicate whether it is null or not
      _privateAttribDeclCpp += "   bool " + pName + FLAG_SUFFIX + ";\n";
    } else {
      //non-set parameters
      // For each job param 'x',
      // there will be a private attribute '_x' in the UDF class.
      _privateAttribDeclCpp += "   " + pTy + " _" + pName + ";\n";
      // parameter flag, indicate whether it is null or not
      _privateAttribDeclCpp += "   bool " + pName + FLAG_SUFFIX + ";\n";
    }
  }

  @Override
  public void exitFuncRval(GSQLParser.FuncRvalContext ctx) {

    String funcName = ctx.funcName().getText();

    // for evaluate function, and the expression string is string literal or parameter,
    // we parse the expression globally.
    if (funcName.toLowerCase().equals("evaluate")) {
      String paramStr = ctx.funcParams().expr(0).getText();
      if (_globalParsedExpressions.containsKey(paramStr)) {
        GSQLParser.ExprAtomContext exprAtom0 = null;
        // The dynamic expression member variable name
        String exprName = _globalParsedExpressions.get(paramStr);
        // Declaration code
        _dynamicExprDeclCpp += "DynamicExpression " + exprName + ";\n";
        // Initialize code
        _dynamicExprInitCpp += "{\n" + "  bool flag;\n" + "  std::string errMsg;\n";

        if (ctx.funcParams().expr(0) instanceof GSQLParser.NonLogicalExprContext) {
          exprAtom0 = ((GSQLParser.NonLogicalExprContext) ctx.funcParams().expr(0)).exprAtom();
        }

        if (exprAtom0 != null && exprAtom0 instanceof GSQLParser.ExprConstantContext) {
          _dynamicExprInitCpp += "string exprStr = " + paramStr + ";\n";
        }

        _dynamicExprInitCpp += ""
            + "  " + exprName + " = DynamicExpression ("
                + (_varType.IsParameter(paramStr) ? ("_" + paramStr) : "exprStr")
                + ", flag, errMsg);\n"
            + "  if (!flag) {\n"
            + "    std::string msg(\"Runtime Error: \" + errMsg);\n"
            + "    HF_set_error(_request, msg, true);\n"
            + "    return;\n"
            + "  }\n"
            + "}\n";

        _globalParsedExpressions.remove(paramStr);
      }
    }
  }

  //////////////
  // helper fns
  /////////////
  /**
   * Generate C++ code for both constructors of a query:
   * 1. the constructor for querydispatcher to call
   * 2. the constructor for for query calling, has all the parameter as input parameter of
   *    the constructor and an additional boolean type parameter to separate this constructor from
   *    the the first one when a query does not have any parameter.
   * We keep them in private members for now and later combine with extra initialization
   * determined in ExprAndCondTranslator.
   * @param udfName Name of the Query
   */
  private String genUdfConstr(String udfName) {

    // the common part cpp code for both of the constructors
    String commonPart =
        // monitoring parameter
        "      if (request.jsoptions_.isMember (\"__GQUERY__monitor\")) {\n"
            + "         char m = request.jsoptions_[\"__GQUERY__monitor\"][0].asString ()[0];\n"
            + "         if (m == \'A\') {\n"
            + "           monitor_all_ = true;\n"
            + "           monitor_ = true;\n"
            + "         } else if (m == \'t\') {\n"
            + "           monitor_all_ = false;\n"
            + "           monitor_ = true;\n"
            + "         } else {\n"
            + "           monitor_all_ = false;\n"
            + "           monitor_ = false;\n"
            + "         }\n"
            + "      } else {\n"
            + "         monitor_all_ = false;\n"
            + "         monitor_ = false;\n"
            + "      }\n"
            + (commonVariable.useEvaluator ? "HF_initCannonicalNameMap();\n" : "")
            + "       enumMapper_ = serviceapi.GetEnumMappers();\n"
            + "       EnableSharedVertexBucket();\n"
        ;

    // for file obj separator
    commonPart +=
        "if (request.jsoptions_.isMember (\"__FILE_OBJ_SEP__\")) {\n"
            + "  file_sep_ = request.jsoptions_[\"__FILE_OBJ_SEP__\"][0].asString ()[0];\n"
            + "}\n"
    ;

    // for file input policy
    if (commonVariable.usingFileInput) {
      commonPart += FileInputOutputPolicy.getInputPolicy().generateFilePolicyConstructorCpp();
    }

    // for file input policy
    if (commonVariable.usingFileOutput) {
      commonPart += FileInputOutputPolicy.getOutputPolicy().generateFilePolicyConstructorCpp();
    }

    // get the vertex types from restpp (common part)
    commonPart +=
        "    if (request.jsoptions_.isMember (\"__GQUERY__\")\n"
            + "    && request.jsoptions_[\"__GQUERY__\"].isMember (\"vTypeIdList\")) {\n"
            + "          for (uint32_t i=0; i < request.jsoptions_[\"__GQUERY__"
            + "\"][\"vTypeIdList\"].size(); i++) {\n"
            + "                __GQUERY__vts_ += (uint32_t)request.jsoptions_[\"__GQUERY__"
            + "\"][\"vTypeIdList\"][i].asUInt();\n"
            + "          }\n"
            + "    } else {\n"
            + "      addAllVertexTypesToSet(__GQUERY__vts_);\n"
            + "    }\n";

    // [GLE-4544] Initialize package library info for UDF class
    if (commonVariable.hasCalledGSQLFunction()) {
      commonPart += genPKGLibMapInitCpp();
      commonPart += genAppliedGSQLFuncPtrAndCache(commonVariable);
    }

    // [GLE-3762] if current_roles() needed, set UDF class private member
    //            from json options in EngineServiceRequest
    if (commonVariable.aggregatedReqCtxt.isNeedCurrentRoles()) {
      commonPart += getCurrentRolesValidationCode();
    }
    // [GLE-3763] if is_granted_to_current_roles(<string_literal>) is used,
    //            initialize bool cache variable in UDF class private member
    if (!commonVariable.isGrantedToCacheNameMap.isEmpty()) {
      commonPart += getIsGrantedToCacheConstrCode();
    }

    // cpp for the first constructor
    String normalConstructorCpp =
        "\n\n\n   "
            + udfName
            + " (gpelib4::EngineServiceRequest& "
            + "request, gperun::ServiceAPI& serviceapi, gpelib4::EngineProcessingMode activateMode)"
            + " :\ngpelib4::BaseUDF"
            + "(activateMode, INT_MAX), _request(request), _serviceapi(&serviceapi) {\n"
            + "isQueryCalled = false;\n"
            // UDF will abort once it meets unexpected error. That means there are many places
            // that the UDF may abort.
            // Set the value for "jsonAPIVersion_" here, then no need to worry about it later.
            + "  _request.SetJSONAPIVersion(\"" + commonVariable.JsonAPI.getVersion() + "\");\n"

            //assume that constructor always use "serviceapi" as the input argument
            //name of gperun::ServiceAPI& type, _setIndexValueStr has this assumption
            // NOTICE: all update to UsedSchemaTypeNameSet should be made before this.
            + commonVariable.getSchemaInitialization()
            +
            // when it is a query been called redirect its json message to nowhere
            "__GQUERY__local_writer = _request.outputwriter_;\n"
            + commonPart
            + _privateAttribInitCpp
            + _dynamicExprInitCpp
            // initialize the map for getAttr()
            + getAttrPosVarInitCpp(commonVariable)
            +
            // If query contains graph update, construct the graphupdate in UDF constructor.
            (commonVariable.useGraphUpdate || commonVariable.callQueries
                ? "      graphupdates = serviceapi.CreateGraphUpdates(&_request);\n"
                : "")
            + "\n";

    normalConstructorCpp += "}\n\n";

    // cpp for the second constructor, used when run as a subquery
    String queryCallConstructor = "";
    queryCallConstructor =
        "\n\n\n   "
            + udfName
            + " (gpelib4::EngineServiceRequest& "
            + "request, gperun::ServiceAPI& serviceapi, "
            + "topology4::GraphUpdatesPointer _graphupdates_ "
            + _queryCallParamCpp
            + ", gpelib4::EngineProcessingMode activateMode, bool isQueryCalled_) :\n"
            + "gpelib4::BaseUDF(activateMode, INT_MAX),"
            + " _request(request), _serviceapi(&serviceapi) {\n"
            + "isQueryCalled = isQueryCalled_;\n"
            // UDF will abort once it meets unexpected error. That means there are many places
            // that the UDF may abort.
            // Set the value for "jsonAPIVersion_" here, then no need to worry about it later.
            + "  _request.SetJSONAPIVersion(\"" + commonVariable.JsonAPI.getVersion() + "\");\n"

            //assume that constructor always use "serviceapi" as the input argument
            //name of gperun::ServiceAPI& type, _setIndexValueStr has this assumption
            // NOTICE: all update to UsedSchemaTypeNameSet should be made before this.
            + commonVariable.getSchemaInitialization()
            + "__GQUERY__local_writer = &__GQUERY__local_writer_instance;\n"
            + commonPart
            + _queryCallAttribInitCpp
            // initialize the map for getAttr()
            + getAttrPosVarInitCpp(commonVariable)
            +
            // If query contains graph update, construct the graphupdate in UDF constructor.
            (commonVariable.useGraphUpdate || commonVariable.callQueries
                ? "      graphupdates = _graphupdates_;\n"
                : "")
            + "\n";

    queryCallConstructor += "}\n\n";

    // put everything together as result
    return normalConstructorCpp + queryCallConstructor + "   ~" + udfName + " () { "
        + "if (vvalptr != nullptr) delete vvalptr; vvalptr = nullptr; }\n\n\n";
  }

  public static String getAttrPosVarDeclareCpp(CommonVariable cv) {
    String cpp = "";
    for (Map.Entry<String, Set<String>> e: cv.VertexTypesGetAttrConst.entrySet()) {
      cpp += "   int " + AttributeDynamicExpression.getConstPosMapName(false, e.getKey())
          + "[" + CatalogManager.getGlobalGraph().LatestVertexId + "];\n";
    }
    for (Map.Entry<String, Set<String>> e: cv.EdgeTypesGetAttrConst.entrySet()) {
      cpp += "   int " + AttributeDynamicExpression.getConstPosMapName(true, e.getKey())
          + "[" + CatalogManager.getGlobalGraph().LatestEdgeId + "];\n";
    }
    return cpp;
  }

  /**
   * Generate C++ code to initialize attribute position variable used in constructor.
   */
  public static String getAttrPosVarInitCpp(CommonVariable cv) {
    return getAttrPosVarInitCpp(cv.VertexTypesGetAttrConst, false, cv)
        + getAttrPosVarInitCpp(cv.EdgeTypesGetAttrConst, true, cv);
  }

  private static String getAttrPosVarInitCpp(Map<String, Set<String>> callers,
      boolean isEdge, CommonVariable cv) {
    if (callers == null) return "";
    String cpp = "";
    int size = isEdge ? CatalogManager.getGlobalGraph().LatestEdgeId
        : CatalogManager.getGlobalGraph().LatestVertexId;
    for (String key : callers.keySet().stream().sorted().collect(Collectors.toList())) {
      String attrNameCpp = AttributeDynamicExpression.getAttrParamCpp(key);
      String mapName = AttributeDynamicExpression.getConstPosMapName(isEdge, key);
      cpp += "for (unsigned i = 0; i < " + size + " ; i++) " + mapName + "[i] = -1;\n";
      for (String type : callers.get(key).stream().sorted().collect(Collectors.toList())) {
        String typeIdCpp = isEdge ? Util.GetEdgeTypeVarName(type, cv)
            : Util.GetVertexTypeVarName(type, cv);
        String rValue = String.format("GSQL_UTIL::HF_GetAttrPosInSchema(&serviceapi, %s, %s, %b)",
            typeIdCpp, attrNameCpp, isEdge);
        cpp += mapName + "[" + typeIdCpp + "] = " + rValue + ";\n";
      }
    }
    return cpp;
  }



  /**
   * Generates a C++ boolean expression to check whether an argument
   * is provided for a query parameter or not.
   * @param paramName GSQL name of the query parameter
   * @return the generated C++ boolean expression
   */
  private String parameterCheck(String paramName) {
    return String.format("request.jsoptions_.isMember(\"%s\")", paramName);
  }

  /**
   * Generates a C++ boolean expression to check whether the file argument
   * is provided for a query parameter or not. and also check if the file argument is empty.
   * @param paramName GSQL name of the query parameter
   * @return the generated C++ boolean expression
   */
  private String fileParameterCheck(String paramName) {
    // Check if paramName is a member of request.jsoptions_
    String memberCheck = String.format("request.jsoptions_.isMember(\"%s\")", paramName);

    // Check if request.jsoptions_[paramName]["s%"][0].asString() equals an empty string
    String emptyCheck = String.format("request.jsoptions_[\"%s\"][0].asString() != \"\"",
        paramName);

    // Return the concatenated string
    return memberCheck + " && " + emptyCheck;
  }

  /**
   * Generates C++ code for initializing GPR/UDF class member variables corresponding
   * to a file-type query parameter in the GPR/UDF class constructor.
   * @param paramName name of the query parameter
   * @return the generated C++ code
   */
  private String initFileParamClassMember(String paramName) {
    final String fileNameCpp
        = GetParamFromJsonOptions(paramName, Arrays.asList(DataType.STRING_TYPE), "0", false);
    final String fileCreation = ExprGen.fileCreation(paramName,
        fileNameCpp, true, commonVariable);
    final String nullParamError = "std::string msg(\"Runtime Error: Parameter %s is NULL.\");";
    CodeBuilder builder = new CodeBuilder();
    builder.append(String.format("if (%s) {", fileParameterCheck(paramName)))
        .append(fileCreation)
        .append("} else {")
        .append(String.format(nullParamError, paramName))
        .append("HF_set_error(_request, msg, true);")
        .append("return;")
        .append("}");
    return builder.toString();
  }

  /**
   * Generates C++ code gor initializing GPR/UDF class member variables corresponding
   * to a primitive-type query parameter in the GPR/UDF class constructor. The query
   * parameter can also be a set or a bag of primitive type.
   *
   * @param paramName       name of the query parameter
   * @param primitiveType   the GSQL primitive type or primitive element type
   * @param isSet           true if the query parameter is a set/bag, false otherwise
   * @param hasInitialValue true if a default value is given in the GSQL query, false otherwise
   * @param defaultCpp    default value of the query parameter in C++ (optional)
   * @return the generated C++ code
   */
  private String initPrimitiveParamClassMember(
      String paramName,
      List<DataType> primitiveTypes,
      boolean isSetOrList,
      boolean isMap,
      boolean hasInitialValue,
      @Nullable String defaultCpp) {
    String paramValue
        = GetParamFromJsonOptions(paramName, primitiveTypes,
                                  (isSetOrList || isMap) ? "i" : "0", isMap);
    CodeBuilder builder = new CodeBuilder();

    // open if block for parameter check
    builder.append(String.format("if (%s) {", parameterCheck(paramName)));
    if (isSetOrList) {
      String containerParameterSize = "request.jsoptions_[\"" + paramName + "\"].size()";
      builder
          .append("for (uint32_t i = 0; i < " + containerParameterSize + "; i++) {")
          .append("_" + paramName + " += " + paramValue + ";")
          .append("}");
    } else if (isMap) {
      String containerParameterSize = "request.jsoptions_[\"" + paramName + "\"][0].size()";
      String classFieldParamName = "_" + paramName;
      String mapKeyCpp
          = GetPrimitiveValueFromJsonOptions(
                String.format("request.jsoptions_[\"%s\"][0][i]", paramName),
                    primitiveTypes.get(0));
      builder
          .append(String.format("for (uint32_t i = 0; i < %s; i++) {", containerParameterSize))
          .append(String.format("  if (%s.containskey(%s)) {", classFieldParamName, mapKeyCpp))
          // this is to make sure that later value of same key will overwrite previous value
          // instead of getting combined.
          .append(String.format("    %s.remove(%s);", classFieldParamName, mapKeyCpp))
          .append("  }")
          .append(String.format("  %s += %s;", classFieldParamName, paramValue))
          .append("}");
    } else {
      builder.append("_" + paramName + " = " + paramValue + ";");
    }
    // set parameter flag in the if-branch
    builder.append(paramName + FLAG_SUFFIX + " = true;");

    // else block for jsoptions membership check
    builder.append("} else {")
        .append("// parameter is not given (null case)\n");
    if (!hasInitialValue && commonVariable.isOpenCypherMode()) {
      builder.append("std::string msg(\"Runtime Error: OpenCypher parameter cannot be empty "
              + "without default value.\");")
          .append("throw gutil::GsqlException(msg, gutil::error_t::E_PARAM_NULL);");
    }
    if (defaultCpp != null) {
      builder.append("_" + paramName + " = " + defaultCpp + ";");
    }
    builder.append(paramName + FLAG_SUFFIX + " = " + hasInitialValue + ";")
        .append("}");
    return StringUtil.padLeftSpacesBatch(6, builder.toString());
  }

  /**
   * Generate C++ code to process a (set of) VERTEX argument in constructor.
   *
   * @param pName The name of the VRETEX parmater
   * @param vTy   The name of the type of {@code pName}. {@code null} when it's generic VERTEX type.
   * @param isSetOrList {@code true} if {@code pName} is a SET/BAG; {@code false} otherwise
   * @return C++ code to process a (set of) VERTEX argument in constructor
   */
  private String initVertexParamClassMember(
      String pName, String vTy, boolean isSetOrList) {
    String vtx = "request.jsoptions_[\"" + pName + "\"][" + (isSetOrList ? "i" : "0") + "]";
    String vtxId = vtx + "[\"id\"].asString()";
    String vtxType = vtx + "[\"type\"].asString()";
    String pGlobal = "_" + pName;
    CodeBuilder cb = new CodeBuilder();
    // check "id": begin
    cb.append(String.format("if (%s) {", parameterCheck(pName)));
    cb.append("VertexLocalId_t localId;");
    if (isSetOrList) {
      // for loop: begin
      cb.append("for (auto i = 0; i < request.jsoptions_[\"" + pName + "\"].size(); i++) {");
    }
    // check no_translation_eid_to_iid: begin
    cb.append("if (request.jsoptions_.isMember(\"no_translation_eid_to_iid\") && "
        + "request.jsoptions_[\"no_translation_eid_to_iid\"][0].asString() == \"true\") {");
    cb.append(pGlobal
        + (isSetOrList ? " += " : " = VERTEX") + "(std::atoll(" + vtxId + ".c_str()));");
    // check no_translation_eid_to_iid: end
    cb.append(commonVariable.isGprInstallMode() ? "} else if (!is_worker) {" : "} else {");
    // translate eid to iid: begin
    cb.append("std::stringstream ss;");
    if (vTy != null) {
      cb.append("ss << %s;", commonVariable.addVertexTypeIndex(vTy));
    } else {
      cb.append("ss << serviceapi.GetTopologyMeta()->GetVertexTypeId(" + vtxType + ", "
          + "_request.graph_id_);");
    }
    cb.append("ss << \"_\" << " + vtxId + ";");
    // check UIdtoVId: begin
    cb.append("if (serviceapi.UIdtoVId (request, ss.str(), localId, false)) {");
    // pGlobal is a SetAccum<VERTEX> when pName is a SET/BAG, so use += operator
    cb.append(pGlobal + ( isSetOrList ? " += " : " = ") + "VERTEX(localId);");
    cb.append("} else {");
    if (isSetOrList) {
      // print warning when pName is a SET/BAG
      cb.append("std::string msg(\"Warning: vertex set or list " + pName
          + " contains invalid vertex ids. They will be removed from the set or list.\");");
      cb.append("HF_set_error(request, msg, false);");
    } else {
      // return immediately otherwise
      cb.append("std::string msg(\"Failed to convert user vertex id for parameter " + pName
          + "\");");
      cb.append("HF_set_error(request, msg, true);");
      cb.append("return;");
    }
    // check UIdtoVId: end
    cb.append("}");
    // translate eid to iid: end
    cb.append("}");
    if (isSetOrList) {
      // for loop: end
      cb.append("}");
    }
    // need to set flag
    cb.append(pName + FLAG_SUFFIX + " = true;");
    // check "id": end

    cb.append("} else {");
    // if pName is a single VERTEX parameter, need to set default value and flag
    if (!isSetOrList) {
      cb.append(pGlobal + " = VERTEX(-1);");
    }
    cb.append(pName + FLAG_SUFFIX + " = false;");
    cb.append("}");

    return StringUtil.padLeftSpacesBatch(4, cb.toString());
  }

  /* [GLE-4544] Generate the package library map for private attribute `_pkglibs_map`*/
  protected static String genPKGLibMapInitCpp() {
    StringBuilder sb = new StringBuilder();
    sb.append("// initialize package name to pointer map\n");
    sb.append("if (request.pkg_names_.size() != request.pkg_libs_.size()) {\n")
        .append("  std::string msg(\"package name and pointer list size differs!\\n\");\n")
        .append("  msg.append(\"Size of package name list is \")\n")
        .append("     .append(std::to_string(request.pkg_names_.size()))\n")
        .append("     .append(\".\\n\");\n")
        .append("  msg.append(\"Size of package pointer list is \")\n")
        .append("     .append(std::to_string(request.pkg_libs_.size()))\n")
        .append("     .append(\".\\n\");\n")
        .append("  msg.append(\"The package name list: \");\n")
        .append("  for (const auto& pkg_name : request.pkg_names_) {\n")
        .append("    msg.append(pkg_name).append(\",\");\n")
        .append("  }\n")
        .append("  HF_set_error(request, msg, true);\n")
        .append("  return;\n")
        .append("}\n");
    sb.append("for (size_t i = 0; i < request.pkg_names_.size(); ++i) {\n")
        .append("  _pkglibs_map[request.pkg_names_[i]] = request.pkg_libs_[i];\n")
        .append("}\n");
    return sb.toString();
  }

  protected static String genAppliedGSQLFuncPtrAndCache(CommonVariable commonVariable) {
    
    StringBuilder sb = new StringBuilder();
    sb.append("// initialize the function pointer and is_granted_cache "
        + "for all GSQL functions used as policy\n");
    Packages inMemPackages = CatalogManager.getGlobalCatalog().getInMemoryPackages();

    for (String gsqlFuncName : commonVariable.getAllCalledGSQLFunctionNames()) {
      // 1. convert gsqlFuncName to get function pointer cache var name
      //    convert gsqlFuncName to package library filename
      String gsqlFuncPtr = StringUtil.getGSQLFuncCppPtrName(gsqlFuncName);
      sb.append(gsqlFuncPtr);
      sb.append(" = ");
      // 2. generate packge getFunction call
      sb.append("_pkglibs_map[std::string(\"")
          .append(StringUtil.getLibPKGFileName(gsqlFuncName)).append("\")]")
          .append(String.format("->GetGSQLFunction<%s>(",
              StringUtil.getGSQLFuncPtrTypeName(gsqlFuncName)))
          .append("std::string(\"")
          .append(StringUtil.getGSQLFuncSymbolName(gsqlFuncName))
          .append("\")")
          .append(");\n");
      // 3. validate pointer for package function
      sb.append(String.format("if (%s == nullptr) {\n", gsqlFuncPtr))
        .append("  std::string msg(\"Un-initialized gsql package function "
            + gsqlFuncName + "\");\n")
        .append("  throw gutil::GsqlException(msg, gutil::error_t::E_GV_NOT_EXIST);\n")
          .append("}\n");
      // 4. initialize pointer to cached result of is_granted_to_current_roles()
      FunctionInfo fi = inMemPackages.getFunction(gsqlFuncName);
      List<String> funcIsGrantedConstRoles = fi.getIsGrantedToConstantRoleNames();
      if (!funcIsGrantedConstRoles.isEmpty()) {
        StringJoiner constRoleSJ = new StringJoiner(", ");
        for (String constRole : funcIsGrantedConstRoles) {
          constRoleSJ.add("\"" + constRole + "\"");
        }
        String isGrantedCacheVar = StringUtil.getGSQLFuncCppIsGrantedCache(gsqlFuncName);
        sb.append(String.format(
            "%s = _request.GetCache4IsGrantedToCurrentRoles(\"%s\", {%s});\n",
            isGrantedCacheVar, gsqlFuncName, constRoleSJ.toString()));
        // validate pointer to cached result of is_granted_cache
        sb.append(String.format("if (%s == nullptr) {\n", isGrantedCacheVar))
            .append("  std::string msg(\"Un-initialized is_granted_cache for gsql function "
                  + gsqlFuncName + "\");\n")
            .append("  throw gutil::GsqlException(msg, gutil::error_t::E_GV_NOT_EXIST);\n")
            .append("}\n");
      }

      sb.append("\n");
    }
    return sb.toString();
  }

  /**
   * Generates C++ code for parsing the argument to a primitive type query parameter from
   * the json data which is part of the engine service request {@code request.jsoptions_}.
   *
   * The json data is structured in the following manners:
   * Each primitive type query parameter is represented by its name as the key and
   * a json array of int64_t, uint64_t, float, double, string, or bool as the value.
   * If the GSQL type of the parameter is primitive, the json array's size is 1.
   * If the GSQL type of the parameter is set or bag, the json array's size depends on user input.
   *
   * @param paramName name of the query parameter
   * @param innerType the inner primitive GSQL type
   * @param paramIndex index used to access the specific element in the json array
   * @return
   */
  private String GetParamFromJsonOptions(String paramName, List<DataType> innerTypes,
      String paramIndex, boolean isMap) {
    if (isMap) {
      // complex case of a map entry which its key and value are both primitive.
      String keyRef = "request.jsoptions_[\"" + paramName + "\"][0][" + paramIndex + "]";
      String valRef = "request.jsoptions_[\"" + paramName + "\"][1][" + paramIndex + "]";
      String keyCpp = GetPrimitiveValueFromJsonOptions(keyRef, innerTypes.get(0));
      String valCpp = GetPrimitiveValueFromJsonOptions(valRef, innerTypes.get(1));
      return "std::pair(" + keyCpp + ", " + valCpp + ")";
    } else {
      // simple case of primitive parameter or a list/bag/set of primitive parameter
      String ref = "request.jsoptions_[\"" + paramName + "\"][" + paramIndex + "]";
      String ret = GetPrimitiveValueFromJsonOptions(ref, innerTypes.get(0));
      return ret;
    }
  }

  /**
   * 
   * @param primitiveValueReferenceInJsonOptions this means what is used in c++ to 
   * refer to the certain parameter or an element of certain parameter (element of list/bag/set)
   * or an element of an element of certain parameter (a key/val of an entry of map)
   * @param primitiveValueDataType datatype corresponds to above reference
   * @return
   */
  private String GetPrimitiveValueFromJsonOptions(
      String primitiveValueReferenceInJsonOptions, DataType primitiveValueDataType) {
    switch (primitiveValueDataType.Name) {
      case "int":
        primitiveValueReferenceInJsonOptions += ".asInt64()";
        break;
      case "uint":
        primitiveValueReferenceInJsonOptions += ".asUInt64()";
        break;
      case "float":
        primitiveValueReferenceInJsonOptions += ".asFloat()";
        break;
      case "double":
        primitiveValueReferenceInJsonOptions += ".asDouble()";
        break;
      case "datetime":
        primitiveValueReferenceInJsonOptions += ".asString()";
        // [GLE-3367] now it will throw exception if datetime is invalid
        primitiveValueReferenceInJsonOptions
            = "DATETIME(gutil::GtimeConverter::datetime_constructor("
                + primitiveValueReferenceInJsonOptions + "))";
        break;
      case "string":
        primitiveValueReferenceInJsonOptions += ".asString()";
        break;
      case "bool":
        primitiveValueReferenceInJsonOptions += ".asBool()";
        break;
    } //end switch
    return primitiveValueReferenceInJsonOptions;
  }
}
