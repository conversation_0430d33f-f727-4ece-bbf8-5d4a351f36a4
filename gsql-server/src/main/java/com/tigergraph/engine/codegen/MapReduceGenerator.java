/**
 * ****************************************************************************
 * Copyright (c) 2015-2017, TigerGraph Inc.
 * All rights reserved
 * Unauthorized copying of this file, via any medium is
 * strictly prohibited
 * Proprietary and confidential
 * ****************************************************************************
 */
package com.tigergraph.engine.codegen;

import static com.tigergraph.engine.codegen.AccumTranslator.LIMIT_TUPLE_NAME_PREFIX;
import static com.tigergraph.engine.codegen.ExprAndCondTranslator.FLAG_SUFFIX;
import static com.tigergraph.engine.codegen.ExprAndCondTranslator.FOREACH_PREFIX;
import static com.tigergraph.engine.codegen.ExprAndCondTranslator.LVAR_PREFIX;
import static com.tigergraph.engine.codegen.ExprAndCondTranslator.RANGE_OBJECT_VARNAME;

import com.tigergraph.common.MessageBundle;
import com.tigergraph.engine.parser.GSQLParser;
import com.tigergraph.engine.parser.GSQLParser.*;
import com.tigergraph.engine.typechecker.DataType;
import com.tigergraph.engine.typechecker.ForeachItem;
import com.tigergraph.engine.typechecker.VarType;
import com.tigergraph.engine.util.UDFSettings;
import com.tigergraph.engine.util.Util;
import com.tigergraph.engine.util.gsqlUtilBaseListener;
import com.tigergraph.utility.CodeGenUtil;
import com.tigergraph.utility.StringUtil;
import java.util.*;
import org.antlr.v4.runtime.ParserRuleContext;
import org.antlr.v4.runtime.RuleContext;
import org.antlr.v4.runtime.tree.ParseTree;
import org.antlr.v4.runtime.tree.ParseTreeProperty;



/**
 * ****************************************************************************************
 *
 * <p>Generates UDF EdgeMap and VertexMap functions. Assumes that the expressions and conditions
 * have already been translated to C++ syntax.
 *
 * <p>For PRINT algorithm, see Appendix[1] in this file.
 *
 * <p>*****************************************************************************************
 */
public class MapReduceGenerator extends gsqlUtilBaseListener {
  // annotates each 'block' parse tree node with a map from alias to normalized alias.
  // computed in prior pass and passed on to this listener.
  private ParseTreeProperty<Map<String, String>> _aliasNormalizationMaps;

  // As this listener enters a 'block' node, it reads the normalization map stored in the
  // ParseTreeProperty and stores it in this attribute for the benefit of the
  // traversal of the subtree. When in the subtree an attribute or vertex accumulator reference
  // is encountered, its normalized name is looked up before generating the c++ code.
  private Map<String, String> _normMap;

  // Annotates parse tree  nodes with their corresponding cpp translation.
  // expressions and conditions are already translated by preceding listener
  // ExprAndConditionTranslator. The remaining nodes are annotated by this listener.
  private ParseTreeProperty<String> _cppCode;

  // Annotates each statement with its PC value.
  // Computed by preceding StatementCounter listener and passed on to this listener.
  private ParseTreeProperty<Integer> _statementPCs;

  // Keeps pc of currently treated statement, used to name the helper c++ functions
  // used to define VertexMap and EdgeMap.
  // Read from _statementPCs annotation of 'statement' nodes.
  private int _pc;

  // We need to know whether a DML block writes to the source and/or target vertex,
  // to decide whether a message containing the update delta needs to be sent.
  // This info is kept here:
  private boolean _writing2src, _writing2tgt, _writing2other;

  // In assignmentsContext, if there is one assignment that do the update for
  // an inctive vertex, we will use the following variables to store the relevant
  // code.
  // _localVarCpp:  mapping the local variable name to its cpp code
  // _localVarDeltaCpp: generate the delta for the vertex represented by local variable
  // _localVarWriteCpp: the code that is used to send the delta
  private ParseTreeProperty<Map<String, String>> _localVarCpp;
  private ParseTreeProperty<Map<String, String>> _localVarDeltaCpp;
  private ParseTreeProperty<Map<String, String>> _localVarWriteCpp;

  /**
   * Moved to gsqlUtilBaseListener
   *
   * protected boolean _activatingSrc, _activatingTgt;
   *
   * We need to know whether the select clause activates the source or the target
   * (in an edge step). The activated vertex needs to receive this information during the reduce
   * phase, so it knows to activate itself for the next iteration. The activation status will be
   * sent in the message. Note that we cannot determine it statically, since select clauses can
   * contain case constructs, which determine whom to activate only at runtime. Also note that a
   * vertex can have its accumulator updated in the accum clause, without being activated by the
   * select clause. So we need to keep the writing and activation status info independent.
  **/


  // Stores typechecks performed in the from clause. For instance,
  //    Customer:c -(likes:e)-> Product:p
  // requires the source vertices to have type 'Customer', the targets to have type 'Product',
  // and the edge to have type 'likes'.
  // The typechecks are needed to construct a filter on the EdgeMap.
  // They are set during the traversal of the subtree of 'fromClause' parse nodes.
  String _srcTyCheck, _tgtTyCheck;
  private Set<String> _eTyCheck;

  // save the e-typechecks for BeforeIteration generator:
  ParseTreeProperty<Set<String>> _eTypeChecks;

  String _queryName;

  public ParseTreeProperty<Set<String>> getETypeChecks() {
    return _eTypeChecks;
  }

  // Records if the query job requires any EdgeMap (), resp. VertexMap () phases.
  private boolean _vMapNeeded;

  public boolean isVMapNeeded() {
    return _vMapNeeded;
  }

  // captures generated code
  private StringBuilder _udfCpp;

  // generated by ExprAndCondTranslator, add declaration before set
  // condition check or computation
  private Map<String, String> _setDeclare;

  // generated by ExprAndCondTranslator, embed code between
  // getAttDeclAssignCpp and whereCpp
  private Map<String, String> _vFunctRvalCodeMap;

  private Map<Integer, String> _pc2cpp;

  // get from ExprAndCondTranslator
  private ParseTreeProperty<Map<String, Map<String, DataType>>> _AttLocalVarDeclMapTree;
  private ParseTreeProperty<Map<String, Map<String, String>>> _AttLocalVarAssignMapTree;

  // Whether each FROM clause use runtime specified target vertex types.
  ParseTreeProperty<Boolean> _tgtRuntimeTypes;

  ParserRuleContext _currBlockCtx;

  // UDFSettings
  private ParseTreeProperty<UDFSettings> _settings;

  public MapReduceGenerator(
      CommonVariable cv,
      ParseTreeProperty<Map<String, String>> anm,
      ParseTreeProperty<Integer> pcs,
      ParseTreeProperty<Boolean> tgtRuntimeTypes,
      ParseTreeProperty<String> cpp,
      Map<String, String> setDeclare,
      Map<String, String> vFunctRvalCodeMap,
      ParseTreeProperty<Map<String, Map<String, DataType>>> attDecl,
      ParseTreeProperty<Map<String, Map<String, String>>> attAssign,
      ParseTreeProperty<Set<String>> attFlag,
      StringBuilder u,
      ParseTreeProperty<UDFSettings> setting,
      Map<Integer, Boolean> printInOrder,
      ParseTreeProperty<Map<String, String>> localVarCpp,
      VarType varType) {
    super(varType, cv);
    _aliasNormalizationMaps = anm;
    _statementPCs = pcs;
    _cppCode = cpp;
    _udfCpp = u;
    _AttLocalVarDeclMapTree = attDecl;
    _AttLocalVarAssignMapTree = attAssign;
    _exprCppFlagTree = attFlag;

    _settings = setting;

    _tgtRuntimeTypes = tgtRuntimeTypes;

    _eTypeChecks = new ParseTreeProperty<Set<String>>();

    _vMapNeeded = false;

    _setDeclare = setDeclare;
    _vFunctRvalCodeMap = vFunctRvalCodeMap;

    _queryName = "";
    _pc2cpp = new HashMap<Integer, String>();

    _localVarCpp = localVarCpp;
    _localVarDeltaCpp = new ParseTreeProperty<Map<String, String>>();
    _localVarWriteCpp = new ParseTreeProperty<Map<String, String>>();
  }

  protected ParseTreeProperty<Set<String>> exprCppFlagTree() {
    return _exprCppFlagTree;
  }

  //////////////////
  // listener rules
  /////////////////

  public void exitJob(GSQLParser.JobContext ctx) {
    for (String cpp : _pc2cpp.values()) {
      _udfCpp.append(cpp);
    }
    genFixedMapReduce(ctx);
  }

  public void enterJob(GSQLParser.JobContext ctx) {
    _queryName = commonVariable.getQueryName();
  }

  public void enterStatementBlock(GSQLParser.StatementBlockContext ctx) {
    _pc = _statementPCs.get(ctx);
    _writing2src = false;
    _writing2tgt = false;
    _writing2other = false;
    _activatingSrc = false;
    _activatingTgt = false;
  }

  public void exitStatementBlock(GSQLParser.StatementBlockContext ctx) {
    if (Util.IsVStep(ctx)) {
      _vMapNeeded = true;
    }
  }

  public void enterStatementUpdate(GSQLParser.StatementUpdateContext ctx) {
    _pc = _statementPCs.get(ctx);
    _writing2src = false;
    _writing2tgt = false;
    _activatingSrc = false;
    _activatingTgt = false;
  }

  public void enterStatementDelete(GSQLParser.StatementDeleteContext ctx) {
    _pc = _statementPCs.get(ctx);
    _writing2src = false;
    _writing2tgt = false;
    _activatingSrc = false;
    _activatingTgt = false;
  }

  public void exitStatementUpdate(GSQLParser.StatementUpdateContext ctx) {
    if (Util.IsVStep(ctx)) {
      _vMapNeeded = true;
    }
  }

  public void exitStatementDelete(GSQLParser.StatementDeleteContext ctx) {
    if (Util.IsVStep(ctx)) {
      _vMapNeeded = true;
    }
  }

  // block in StatementBlock
  public void enterBlock(GSQLParser.BlockContext ctx) {
    _normMap = _aliasNormalizationMaps.get(ctx);
    _currBlockCtx = ctx;
  }

  public void exitBlock(GSQLParser.BlockContext ctx) {
    String map = genStageMap(ctx);
    String reduce = genStageReduce(ctx);
    // put codes to pc map
    _pc2cpp.put(_pc, map + reduce);
    _eTypeChecks.put(ctx, _eTypeChecks.get(ctx.fromClause()));
  }

  // updateBlock in StatementUpdateVertex
  public void enterUpdateBlock(GSQLParser.UpdateBlockContext ctx) {
    _normMap = _aliasNormalizationMaps.get(ctx);
    _currBlockCtx = ctx;
  }

  public void enterDeleteBlock(GSQLParser.DeleteBlockContext ctx) {
    _normMap = _aliasNormalizationMaps.get(ctx);
    _currBlockCtx = ctx;
  }

  public void exitUpdateBlock(GSQLParser.UpdateBlockContext ctx) {
    String map = genStageMap(ctx);
    String reduce = genReduceForGraphUpdate(ctx);
    // put codes to pc map
    _pc2cpp.put(_pc, map + reduce);
    _eTypeChecks.put(ctx, _eTypeChecks.get(ctx.fromClause()));
  }

  public void exitDeleteBlock(GSQLParser.DeleteBlockContext ctx) {
    String map = genStageMapDelete(ctx);
    String reduce = genReduceForGraphDelete(ctx);
    // put codes to pc map
    _pc2cpp.put(_pc, map + reduce);
    _eTypeChecks.put(ctx, _eTypeChecks.get(ctx.fromClause()));
  }

  // SELECT

  public void exitSelectVar(GSQLParser.SelectVarContext ctx) {
    if (isSrc(ctx.var())) {
      _activatingSrc = true;
      _activatingTgt = false;
    } else {
      _activatingSrc = false;
      _activatingTgt = true;
    }

    String selectFn = "";
    StatementContext stmt = Util.GetStatementContext(ctx);
    if (isEdgeTraversal(stmt))
      selectFn = "   " + (isSrc(ctx.var()) ? "src" : "tgt") + "_delta."
        + CodeGenUtil.toCppName("activate") + " = true;\n";
    else selectFn = "   src_delta." + CodeGenUtil.toCppName("activate") + " = true;\n";
    saveCpp(ctx, selectFn);
  }

  public void exitUpdateClause(GSQLParser.UpdateClauseContext ctx) {
    // when src is selected or tgt is selected
    // the update operations will be moved to
    // reduce phase, therefore when _activatingSrc
    // and _activatingTgt both equals to false
    // that means we do updates in EdgeMap
    if (isSrc(ctx.var())) {
      _activatingSrc = true;
      _activatingTgt = false;
    } else if (isTgt(ctx.var())) {
      _activatingSrc = false;
      _activatingTgt = true;
    } else {
      _activatingSrc = false;
      _activatingTgt = false;
    }

    String selectFn = "";
    StatementContext stmt = Util.GetStatementContext(ctx);
    if (isEdgeTraversal(stmt) && (_activatingSrc || _activatingTgt))
      selectFn = "   " + (isSrc(ctx.var()) ? "src" : "tgt") + "_delta."
        + CodeGenUtil.toCppName("activate") + " = true;\n";
    else selectFn = "   src_delta." + CodeGenUtil.toCppName("activate") + " = true;\n";

    saveCpp(ctx, selectFn);
  }

  public void exitDeleteClause(GSQLParser.DeleteClauseContext ctx) {
    // when src is selected or tgt is selected
    // the delete operations will be moved to
    // reduce phase, therefore when _activatingSrc
    // and _activatingTgt both equals to false
    // that means we do deletion in EdgeMap
    if (isSrc(ctx.var())) {
      _activatingSrc = true;
      _activatingTgt = false;
    } else if (isTgt(ctx.var())) {
      _activatingSrc = false;
      _activatingTgt = true;
    } else {
      _activatingSrc = false;
      _activatingTgt = false;
    }

    String selectFn = "";
    StatementContext stmt = Util.GetStatementContext(ctx);
    if (isEdgeTraversal(stmt) && (_activatingSrc || _activatingTgt))
      selectFn = "   " + (isSrc(ctx.var()) ? "src" : "tgt") + "_delta."
        + CodeGenUtil.toCppName("activate") + " = true;\n";
    else selectFn = "   src_delta." + CodeGenUtil.toCppName("activate") + " = true;\n";

    saveCpp(ctx, selectFn);
  }

  // FROM
  public void enterFromClause(GSQLParser.FromClauseContext ctx) {
    // will be set by traversal of subtree
    _srcTyCheck = null;
    _tgtTyCheck = null;
    _eTyCheck = new HashSet<String>();
  }

  public void exitFromClause(GSQLParser.FromClauseContext ctx) {
    // these will be enforced in EdgeMap
    String tyCheck = "";
    if (_srcTyCheck != null) tyCheck += _srcTyCheck;
    if (_tgtTyCheck != null) {
      tyCheck += (tyCheck != "" ? " && " : "") + _tgtTyCheck;
    }
    if (tyCheck == "") tyCheck = "true";
    saveCpp(ctx, tyCheck);

    // this one is treated specially, as it can be enforced in BeforeIteration ()
    // once and for all, which is more efficient than repeatedly running it at each
    // vertex:
    if (!_eTyCheck.containsAll(_varType.allEdgeTypes())) {
      _eTypeChecks.put(ctx, _eTyCheck);
    }
  }

  @Override
  public void enterEStep(GSQLParser.EStepContext ctx) {
    // entering new pattern step (see step in GSQLParser.g4),
    // reset current vertex set type to src
    setSrc(true);
  }

  @Override
  public void enterVStep(GSQLParser.VStepContext ctx) {
    // entering new pattern step (see step in GSQLParser.g4),
    // reset current vertex set type to src
    setSrc(true);
  }

  @Override
  public void enterPathPattern(GSQLParser.PathPatternContext ctx) {
    // entering new pattern step (see step in GSQLParser.g4),
    // reset current vertex set type to src
    setSrc(true);
  }

  @Override
  public void exitVTest(GSQLParser.VTestContext ctx) {
    if (isSrc()) handleSrcVertexType(ctx);
    else handleTgtVertexType(ctx);
    // vertex set types will always be of type tgt after first
    // pass through this method for current pattern step
    setSrc(false);
  }

  @Override
  public void exitETest(GSQLParser.ETestContext ctx) {
    if (hasGenericTyCheck(ctx.tyCheckList())) {
      _eTyCheck.addAll(_varType.allEdgeTypes());
    } else {
      getTyCheckExprList(ctx.tyCheckList()).forEach(expr -> _eTyCheck.add(expr.getText()));
    }
  }

  @Override
  public void exitPatternAtom(PatternAtomContext ctx) {
    collectEdgeTypesFromAtomicPattern(ctx.atomicPattern());
  }

  @Override
  public void exitPatternDisj(PatternDisjContext ctx) {
    ctx.disjPattern().atomicPattern().forEach(atom -> collectEdgeTypesFromAtomicPattern(atom));
  }

  // WHERE

  public void exitWhereClause(GSQLParser.WhereClauseContext ctx) {
    saveCpp(ctx, getCpp(ctx.condition()));
  }

  /**
   * ACCUM In ExprAndCondTranslater, the graphupdate declaration code is piggybacked onto
   * AccumClauseContext. Here append the assignment's code after the graph update decl code
   */
  public void exitAccumClause(GSQLParser.AccumClauseContext ctx) {
    String cpp = getCpp(ctx).replace("@@@_UPDATE_ATT_CPP", getCpp(ctx.assignments()));

    saveCpp(ctx, cpp);
  }

  public void enterAssignments(GSQLParser.AssignmentsContext ctx) {
    _localVarDeltaCpp.put(ctx, new HashMap<String, String>());
    _localVarWriteCpp.put(ctx, new HashMap<String, String>());
  }

  public void exitAssignments(GSQLParser.AssignmentsContext ctx) {
    String res = "", cpp;

    StringBuilder breakOrContinueCppCode = new StringBuilder();
    for (int i = 0, j = 0; i < ctx.getChildCount(); i += 2, j++) {
      ParseTree node = ctx.getChild(i);
      cpp = getCpp(ctx.getChild(i));
      if ((node instanceof GSQLParser.AssignBreakContext)
          || (node instanceof  GSQLParser.AssignContinueContext)) {
        // if the context is a break or continue context,
        // should insert code at the end of all the assignments.
        breakOrContinueCppCode.append((cpp == null) ? "" : "\n   " + cpp);
        break;
      }
      if (cpp != null && ctx.getChild(i) instanceof GSQLParser.AssignVFunctRvalContext
          && ctx.getParent() instanceof GSQLParser.PostAccumClauseContext
          && j < ctx.assignment().size()) {
        cpp = this.applyAttFlag(cpp, ctx.assignment(j));
      }
      res += (cpp == null) ? "" : "\n   " + cpp;
    }

    Map<String, String> initDelta = _localVarDeltaCpp.get(ctx);
    Map<String, String> wrtDelta = _localVarWriteCpp.get(ctx);

    for (String value : initDelta.values()) {
      res = value + res;
    }

    for (String value : wrtDelta.values()) {
      res = res + value;
    }

    res = res + breakOrContinueCppCode.toString();

    saveCpp(ctx, res);
  }

  public void exitLogExpr(GSQLParser.LogExprContext ctx) {
    String condition = getCpp(ctx.expressions().expr(0));

    String cpp = "if (" + condition + ") {\n";
    cpp += "      // print to log\n" + "      GCOUT(0) << \"[UDF_" + _queryName + " log] \" << ";

    for (int i = 1; i <= ctx.expressions().getChildCount() / 2; ++i) {
      String exprCpp = getCpp(ctx.expressions().expr(i));
      cpp +=
          "\""
              + ctx.expressions().expr(i).getText().replace("\"", "\\\"")
              + ": \" << "
              + exprCpp
              + " << \", \" << ";
    }
    cpp += "std::endl;\n   }\n";

    saveCpp(ctx, cpp);
  }

  public void exitAllFuncAccess(GSQLParser.AllFuncAccessContext ctx) {
    if (ctx.exprAtom().getChild(0) instanceof VAccRvalContext) {
      String assignCpp = getCpp(ctx);
      saveCpp(ctx, assignCpp);

    } else if (ctx.exprAtom().getChild(0) instanceof AllTupleFieldAccessContext
        || ctx.exprAtom() instanceof ExprRhsIdentContext) {

      // if attrRval is used in post accum, we need to send message
      ForeachItem item = _varType.GetForeachItem(ctx.exprAtom().getText(), ctx);
      if (!Util.InPostAccum(ctx) || item != null) {
        return;
      }

      String origVarName = ctx.exprAtom().getChild(0).getText();
      String normalizedName = normalizeVarName(origVarName);

      // we need to send message to the source vertex
      _writing2src |= "src".equals(normalizedName);

      // we need to send message to the target vertex
      _writing2tgt |= "tgt".equals(normalizedName);
    }
  }

  public void exitAllTupleFieldAccess(GSQLParser.AllTupleFieldAccessContext ctx) {
    // if attrRval is used in post accum, we need to send message
    ForeachItem item = _varType.GetForeachItem(ctx.exprAtom().getText(), ctx);
    if (!Util.InPostAccum(ctx) || item != null) {
      return;
    }

    String origVarName = ctx.exprAtom().getText();
    String normalizedName = normalizeVarName(origVarName);

    // we need to send message to the source vertex
    _writing2src |= "src".equals(normalizedName);

    // we need to send message to the target vertex
    _writing2tgt |= "tgt".equals(normalizedName);
  }

  public void exitAssignVAcc(GSQLParser.AssignVAccContext ctx) {
    String deltaCpp = getCpp(ctx.vAccLval());
    String exprCpp = getCpp(ctx.getChild(2));
    String origVarName = ctx.vAccLval().var().getText();
    genDeltaCode(ctx, origVarName);

    if (ctx.vAccLval().exprDimension() != null && ctx.vAccLval().exprDimension().size() != 0) {
      DataType vAccTy = _varType.GetAccType(ctx.vAccLval().VACCNAME().getText(), ctx);

      String exprCode = getCpp(ctx.expr());
      /**
       * Fix for UDF local ArrayAccum reduce (+=) in GLE-3462.
       * Already in ArrayAccum, check if needed to construct datetime for rhs.
       * Global containers are already annotated, there's no need to construct
       * datetime if they're on rhs. More details please refer to comments
       * in gsqlUtilBaseListener.statementReduceGlobalAccum.
       */
      if (vAccTy.isDatetimeNut() && !_varType.getType(ctx.expr()).isContainer()) {
        exprCode = "(DATETIME) " + exprCode;
      }

      exprCpp = vAccTy.toCpp() + " ("
          + Util.getDimensionVectorStr(ctx.vAccLval().exprDimension(), _cppCode)
          + "," + exprCode + ");\n";
    }

    String assignCpp = deltaCpp + " += " + exprCpp + ";\n";

    if (!Util.InPostAccum(ctx)) {
      String hasChanged = deltaCpp.replaceAll("[.]", "." + CodeGenUtil.toCppName("hasChanged_"));
      assignCpp += "   " + hasChanged + " = true;";
    }

    // flag for assignments
    assignCpp = this.applyAttFlag(assignCpp, ctx);

    saveCpp(ctx, prepareEvalCpp(ctx) + assignCpp);
  }

  /**
   * Generate the attribute update code.
   *
   * <p>1. Get the template code generated from ExprAndCondTranslator with format:
   * int tp = context->GraphAPI()->GetVertexType(src);
   * if (tp == _schema["VTY_xx1"]) {
   *   attrUpdate_src->Set(_schema["VATT_xx1_yy"], @@@_NEW_VALUE);
   * } else if (tp == _schema["VTY_xx2"]) {
   *   attrUpdate_src->Set(_schema["VATT_xx2_yy"], @@@_NEW_VALUE);
   * } else if (...) {
   *   ...
   * }
   *
   * <p>2. Replace placeholder @@@_NEW_VALUE with the rhs expression's cpp code. Here string type
   * expression is a special case because the graphupdate->Set(int position, string& val) takes a
   * string reference. We need to first assign the expression to a local string variable and then
   * use the local variable as parameter. The code for assigning string has the format: { string
   * lval_update_str_attr = {exprCpp}; int tp = context->GraphAPI()->GetVertexType(src); if (tp
   * == VTY_xx1) { attrUpdate_src->Set(VATT_xx1_yy, lval_update_str_attr);} else if (tp == VTY_xx2)
   * {   attrUpdate_src->Set(VATT_xx2_yy,lval_update_str_attr);} else if (...) {...}. The code for
   * assigning other type data has the format: int tp = context->GraphAPI()->GetVertexType(src);
   * if (tp == VTY_xx1) { attrUpdate_src->Set(VATT_xx1_yy, {exprCpp});} else if (tp == VTY_xx2)
   * {   attrUpdate_src->Set(VATT_xx2_yy, {exprCpp});} else if (...) {...}
   */
  public void exitStatementAssignAttr(GSQLParser.StatementAssignAttrContext ctx) {
    String assignCpp = replaceAttrUpdatePlaceHolder(ctx.attrLval(), ctx.expr());
    // flag for assignments
    assignCpp = this.applyAttFlag(assignCpp, ctx);
    saveCpp(ctx, assignCpp);
  }

  public void exitAssignAttr(GSQLParser.AssignAttrContext ctx) {
    String assignCpp = replaceAttrUpdatePlaceHolder(ctx.attrLval(), ctx.expr());

    RuleContext pCtx = findPostAccumRootContext(ctx);
    if (pCtx != null) {
      // This attribute assignment is nested in Post-Accum
      // need to add the Post-Accum accum declaration to each assignment
      assignCpp = getCpp(pCtx).replace("@@@_UPDATE_ATT_CPP", assignCpp);
    }
    // flag for assignments
    assignCpp = this.applyAttFlag(assignCpp, ctx);
    saveCpp(ctx, assignCpp);
  }

  public void exitDeleteVertexEdge(GSQLParser.DeleteVertexEdgeContext ctx) {
    // Apply the type or src / tgt flags to delete vertex statement.
    saveCpp(ctx, this.applyAttFlag(getCpp(ctx), ctx));

    // Active src or tgt depending on alias's cannonical name
    String normalizedName = normalizeVarName(ctx.var().getText());

    if ("src".equals(normalizedName)) {
      // we are sending message to the source vertex
      _writing2src = true;
    } else if ("tgt".equals(normalizedName)) {
      // sending to target
      _writing2tgt = true;
    }
  }

  public void exitSetVAcc(GSQLParser.SetVAccContext ctx) {
    //tgt_delta.count
    String deltaCpp = getCpp(ctx.vAccLval());
    String exprCpp = getCpp(ctx.getChild(2));
    String setCpp = "";
    String origVarName = ctx.vAccLval().var().getText();
    genDeltaCode(ctx, origVarName);

    if (ctx.vAccLval().exprDimension() != null
        && ctx.vAccLval().exprDimension().size() != 0) {
      setCpp = deltaCpp + ".setValue("
        + Util.getDimensionVectorStr(ctx.vAccLval().exprDimension(), _cppCode)
        + ", " + exprCpp + ");\n";
    } else {
      setCpp = deltaCpp + " = " + exprCpp + ";\n";
    }

    if (!Util.InPostAccum(ctx)) {
      String setV = deltaCpp.replaceAll("[.]", "." + CodeGenUtil.toCppName("set_"));
      setCpp += "   " + setV + " = true;";
    }

    // att flag for assignments
    setCpp = this.applyAttFlag(setCpp, ctx);

    saveCpp(ctx, prepareEvalCpp(ctx) + setCpp);
  }

  public void exitAssignGAcc(GSQLParser.AssignGAccContext ctx) {
    String gAccName = ctx.gAccLval().GACCNAME().getText(); // Has leading '@@'
    String gAccTy = _varType.GetAccType(gAccName, ctx).toCpp();
    String codeGenName = _varType.getCodeGenName(gAccName.substring(2), ctx, "GACC");
    String gVarName = "GV_GACC_" + codeGenName;

    String cpp = "";
    if (ctx.gAccLval().exprDimension() != null
        && ctx.gAccLval().exprDimension().size() != 0) {
      // this ArrayAccum branch should be checked first for case "@@gv[2] += @@gv[1]"

      /**
       * Already in ArrayAccum, check if needed to construct datetime for rhs.
       * Global containers are already annotated, there's no need to construct
       * datetime if they're on rhs. More details please refer to comments
       * in gsqlUtilBaseListener.statementReduceGlobalAccum.
       */
      String exprCode = getCpp(ctx.getChild(2));
      DataType gAccType = _varType.GetAccType(ctx.gAccLval().GACCNAME().getText(), ctx);
      if (gAccType.isDatetimeNut() && !_varType.getType(ctx.expr()).isContainer()) {
        exprCode = "(DATETIME) " + exprCode;
      }

      cpp =
          "context->GlobalVariable_Reduce<"
              + gAccTy
              + "> ("
              + gVarName
              + ", "
              + gAccTy
              + " ("
              + Util.getDimensionVectorStr(ctx.gAccLval().exprDimension(), _cppCode) + ","
              + exprCode
              + "));";
    } else if (getCpp(ctx.getChild(2)).contains(gAccTy)) {
      //for tuple constructor, do not add gAccTy, as we have
      //generated the constructor, and save it with ctx.
      cpp =
          "context->GlobalVariable_Reduce<"
              + gAccTy
              + "> ("
              + gVarName
              + ", "
              + getCpp(ctx.getChild(2))
              + ");";
    } else {
      cpp =
          "context->GlobalVariable_Reduce<"
              + gAccTy
              + "> ("
              + gVarName
              + ", "
              + gAccTy
              + " ("
              + getCpp(ctx.getChild(2))
              + "));";
    }

    if (_varType.IsStaticGacc(ctx.gAccLval().GACCNAME().getText())) {
      cpp +=
          "                     context->GlobalVariable_Reduce<bool> (GV_SYS_"
              + codeGenName
              + "_CHANGED, true);\n";
    }

    // att flag for assignments
    cpp = this.applyAttFlag(cpp, (ParserRuleContext) ctx.getChild(2));

    saveCpp(ctx, prepareEvalCpp(ctx) + cpp);
  }

  public void exitAssignDeclLocalContainer(GSQLParser.AssignDeclLocalContainerContext ctx) {

    String RVar = getCpp(ctx.expr());
    String VarName = LVAR_PREFIX + ctx.localContainerVal().getText();
    DataType dt =
        DataType.createLocalContainerDataType(ctx.containerType(), _varType, commonVariable);

    // For local variable, if the type is vertex or edge, "= default value"
    // part is not needed because they have default constructor.
    String declareCpp =
        dt.toCpp()
            + " "
            + VarName
            + ((dt.isVertex() || dt.isEdge()) ? "" : (" = " + dt.DefaultValue()))
            + ";\n"
            + "  bool "
            + VarName
            + FLAG_SUFFIX
            + " = false;\n";

    String assignCpp = VarName + " = " + RVar + ";\n" + VarName + FLAG_SUFFIX + " = true;\n";

    assignCpp = this.applyAttFlag(assignCpp, ctx.expr());

    saveCpp(ctx, declareCpp + prepareEvalCpp(ctx) + assignCpp);
  }

  public void exitAssignDeclLocalTuple(GSQLParser.AssignDeclLocalTupleContext ctx) {

    String RVar = getCpp(ctx.expr());
    String VarName = LVAR_PREFIX + ctx.localTupleVal().getText();
    DataType dt = null;
    if (ctx.anonymousTupleType() != null) {
      // It is safe to get DataType from rhs expression after passed TypeChecker
      // To avoid the issue like "tuple<int, string> b = mainTuple(2, "good")"
      dt = _varType.getType(ctx.expr());
      // when right hand side is anonymousTuple as well, generate type from typeParams
      if (dt.isAnonymousTuple()) {
        dt = DataType.createAnonymousTupleDataType(ctx.anonymousTupleType(), null);
      }
    } else {
      dt = DataType.getTypeFromTupleTypeNameContext(ctx.tupleTypeName(), _varType);
    }

    // For local variable, if the type is vertex or edge, "= default value"
    // part is not needed because they have default constructor.
    String declareCpp =
        dt.toCpp()
            + " "
            + VarName
            + ((dt.isVertex() || dt.isEdge()) ? "" : (" = " + dt.DefaultValue()))
            + ";\n"
            + "  bool "
            + VarName
            + FLAG_SUFFIX
            + " = false;\n";

    String assignCpp = VarName + " = " + RVar + ";\n" + VarName + FLAG_SUFFIX + " = true;\n";

    assignCpp = this.applyAttFlag(assignCpp, ctx.expr());

    saveCpp(ctx, declareCpp + prepareEvalCpp(ctx) + assignCpp);
  }

  public void exitAssignDeclLocal(GSQLParser.AssignDeclLocalContext ctx) {

    String RVar = getCpp(ctx.expr());
    String lVarName = LVAR_PREFIX + ctx.localVarLVal().getText();

    // For local variable, if the type is vertex or edge, should change
    // type to VERTEX and EDGE correspondingly.
    DataType dt = DataType.createBaseDataType(ctx.baseType());

    // For local variable, if the type is vertex or edge, "= default value"
    // part is not needed because they have default constructor.
    String declareCpp =
        dt.toCpp()
            + " "
            + lVarName
            + ((dt.isVertex() || dt.isEdge()) ? "" : (" = " + dt.DefaultValue()))
            + ";\n"
            + "  bool "
            + lVarName
            + FLAG_SUFFIX
            + " = false;\n";

    String assignCpp = lVarName + " = " + RVar + ";\n" + lVarName + FLAG_SUFFIX + " = true;\n";

    assignCpp = this.applyAttFlag(assignCpp, ctx.expr());

    saveCpp(ctx, declareCpp + prepareEvalCpp(ctx) + assignCpp);
  }

  public void exitAssignLocal(GSQLParser.AssignLocalContext ctx) {

    String varName = ctx.localVarLVal().getText();
    String lVarName = LVAR_PREFIX + varName;
    String rCpp = getCpp(ctx.expr());

    // lVarName is local variable
    String cpp = lVarName + " = " + rCpp + ";\n";
    // assign flag to true
    cpp += "  " + lVarName + FLAG_SUFFIX + " = true;\n";

    // lVarName is global variable
    if (_varType.GetVarType(varName, ctx) == VarType.GLOBAL_VARIABLE) {
      DataType dt = _varType.GetGlobalVarType(varName, ctx);
      // add scopeIndex to support name reuse
      String codeGenName = _varType.getCodeGenName(varName, ctx, "GlobalVar");

      rCpp = "static_cast<" + dt.toCpp() + ">(" + rCpp + ")";
      cpp = "context->GlobalVariable_Reduce" + "(GV_GV_"
          + codeGenName + ", " + rCpp + ");\n";
    }

    cpp = this.applyAttFlag(cpp, ctx.expr());

    saveCpp(ctx, prepareEvalCpp(ctx) + cpp);
  }

  public void exitAssignCaseConst(GSQLParser.AssignCaseConstContext ctx) {
    List<GSQLParser.WhenConstAccContext> whens =
        ctx.getRuleContexts(ctx.whenConstAcc(0).getClass());

    String res = "";

    for (int i = 0; i < whens.size(); i++) {
      String whenCpp = getCpp(whens.get(i));
      if (i > 0) res += "\n   else ";
      res += whenCpp;
    }

    if (ctx.assignments() != null) {
      String assignments = getCpp(ctx.assignments());
      res += "\n   else { " + assignments + " }";
    }

    String postAccumFlag = this.popPostAccumFlag4CaseWhen(ctx);

    // expr flags for assignments
    res = this.applyAttFlag(res, ctx.exprAtom());

    if (postAccumFlag != null) {
      res = "  if (" + postAccumFlag + ") {\n" + res + "\n  }\n";
    }

    saveCpp(ctx, prepareEvalCpp(ctx) + res);
  }

  public void exitAssignCaseCond(GSQLParser.AssignCaseCondContext ctx) {
    List<GSQLParser.WhenCondAccContext> whens = ctx.getRuleContexts(ctx.whenCondAcc(0).getClass());

    String res = "";

    String postAccumFlag = this.popPostAccumFlag4CaseWhen(ctx);

    for (int i = 0; i < whens.size(); i++) {
      String whenCpp = getCpp(whens.get(i));
      if (i > 0) res += "   else ";
      res += whenCpp;
    }

    if (ctx.assignments() != null) {
      String assignments = getCpp(ctx.assignments());
      res += "   else { " + assignments + "\n }";
    }

    if (postAccumFlag != null) {
      res = "  if (" + postAccumFlag + ") {\n" + res + "\n  }\n";
    }

    saveCpp(ctx, prepareEvalCpp(ctx) + res);
  }

  public void exitAssignIf(GSQLParser.AssignIfContext ctx) {

    String postAccumFlag = this.popPostAccumFlag4CaseWhen(ctx);


    String res = "";

    res += "if ("
        + getCpp(ctx.condition())
        + ") {\n"
        + getCpp(ctx.assignments())
        + "\n  }";

    if (ctx.assignElseIfBlock(0) != null) {
      List<GSQLParser.AssignElseIfBlockContext> elseifs
          = ctx.getRuleContexts(ctx.assignElseIfBlock(0).getClass());
      for (int i = 0; i < elseifs.size(); i++) {
        String elseifsCpp = getCpp(elseifs.get(i));
        res += "   else "
            + elseifsCpp;
      }
    }

    if (ctx.assignElseBlock() != null) {
      String assignments = getCpp(ctx.assignElseBlock().assignments());
      res += "   else { " + assignments + "\n }";
    }

    if (postAccumFlag != null) {
      res = "  if (" + postAccumFlag + ") {\n" + res + "\n  }\n";
    }

    saveCpp(ctx, prepareEvalCpp(ctx) + res);
  }

  public void exitExprRhsIdent(GSQLParser.ExprRhsIdentContext ctx) {
    //check if it is alias
    if (!Util.InPostAccum(ctx)) {
      return;
    }

    String normalizedName = normalizeVarName(ctx.getText());

    // we need to send message to the source vertex
    _writing2src |= "src".equals(normalizedName);

    // we need to send message to the target vertex
    _writing2tgt |= "tgt".equals(normalizedName);
  }

  public void exitVAccRval(GSQLParser.VAccRvalContext ctx) {
    // if vAccRval is used in post accum, we need to send message
    if (!Util.InPostAccum(ctx)) {
      return;
    }

    String origVarName = ctx.var().ident().getText();
    String normalizedName = normalizeVarName(origVarName);

    // we need to send message to the source vertex
    _writing2src |= "src".equals(normalizedName);

    // we need to send message to the target vertex
    _writing2tgt |= "tgt".equals(normalizedName);
  }

  public void exitVAccLval(GSQLParser.VAccLvalContext ctx) {
    String vAccName = ctx.VACCNAME().getText().substring(1); // leading '@'
    String origVarName = ctx.var().ident().getText();
    String normalizedName = normalizeVarName(origVarName);
    int vtype = _varType.GetVarType(origVarName, ctx);
    ForeachItem foreachItem = _varType.GetForeachItem(origVarName, ctx);
    _writing2other = false;

    String codeGenName = _varType.getCodeGenName(vAccName, ctx, "VACC");

    if (vtype == VarType.LOCAL_LVERTEX_VAR || foreachItem != null) {
      _writing2other = Util.InAccum(ctx);
      saveCpp(ctx, origVarName + "_other_delta." + codeGenName);
    } else if (vtype == VarType.LOCAL_LEDGE_VAR || normalizedName.equals("e")) {
      error(ctx.var().ident().getStart(), MessageBundle.get("edgeAccum.notSupported"));
    } else if (normalizedName.equals("src")) {
      // we are sending message to the source vertex
      _writing2src = true;
      if (Util.InPostAccum(ctx)) {
        saveCpp(ctx, "l_val" + commonVariable.VValueAccessor() + codeGenName);
      } else {
        saveCpp(ctx, "src_delta." + codeGenName);
      }
    } else if (normalizedName.equals("tgt")) {
      // sending to target
      _writing2tgt = true;
      if (Util.InPostAccum(ctx)) {
        saveCpp(ctx, "l_val" + commonVariable.VValueAccessor() + codeGenName);
      } else {
        saveCpp(ctx, "tgt_delta." + codeGenName);
      }
    } else {
      error(ctx.var().ident().getStart(), "could not normalizeVarName " + origVarName);
    }
  }

  /** This method is used to determine if the src or tgt needs to be activated. */
  public void exitAttrLval(GSQLParser.AttrLvalContext ctx) {
    // If attrLval is in a foreach variable, no need to activate
    if (_varType.GetForeachItem(ctx.var().getText(), ctx) != null) {
      return;
    }

    // if attrLval is a parameter, return since no map or reduce
    if (_varType.IsParameter(ctx.var().getText())) {
      return;
    }
    // if attrLval is a global var, return since no map or reduce
    if (_varType.IsGlobalVar(ctx.var().getText())) {
      return;
    }

    String origVarName = ctx.var().ident().getText();
    String normalizedName = normalizeVarName(origVarName);

    if (normalizedName.equals("src")) {
      // we are sending message to the source vertex
      _writing2src = true;
    } else if (normalizedName.equals("tgt")) {
      // sending to target
      _writing2tgt = true;
    }
  }

  public void exitWhenConstAcc(GSQLParser.WhenConstAccContext ctx) {
    String expr = getCpp(ctx.getParent().getChild(1));
    String constant = getCpp(ctx.constant());
    String condition = expr + " == " + constant;

    /* optimization on e.type_name() == "xxx" ==> e.type() == _schema["ETY_xxx"] */
    if (ctx.getParent().getChild(1) instanceof GSQLParser.ExprTypeRvalContext) {
      // remove quotes
      String type = constant.substring(1, constant.length() - 1);

      // we have already checked in ParseTreeTypeDecoration
      // the constant string must be valid ET/VT in schema
      if (_varType.GetVarType(type, ctx) == VarType.ET) {
        condition = expr.replace("type_name", "type") + " == "
            + Util.GetEdgeTypeVarName(type, commonVariable);
      } else if (_varType.GetVarType(type, ctx) == VarType.VT) {
        condition = expr.replace("TypeName", "Type") + " == "
            + Util.GetVertexTypeVarName(type, commonVariable);
      }
    }
    /** *************************************************************** */
    String branch = getCpp(ctx.assignments());
    String cpp = "if (" + condition + ") { " + branch + " }";
    saveCpp(ctx, cpp);
  }

  public void exitAssignElseIfBlock(GSQLParser.AssignElseIfBlockContext ctx) {
    String condition = getCpp(ctx.condition());
    String branch = getCpp(ctx.assignments());
    String cpp = " if (" + condition + ") { " + branch + "\n }";
    saveCpp(ctx, cpp);
  }

  public void exitWhenCondAcc(GSQLParser.WhenCondAccContext ctx) {
    String condition = getCpp(ctx.condition());
    String branch = getCpp(ctx.assignments());
    String cpp = " if (" + condition + ") { " + branch + "\n }";
    saveCpp(ctx, cpp);
  }

  public void exitAssignBreak(GSQLParser.AssignBreakContext ctx) {
    saveCpp(ctx, "break;");
  }

  public void exitAssignContinue(GSQLParser.AssignContinueContext ctx) {
    saveCpp(ctx, "continue;");
  }

  public void exitAssignForeach(GSQLParser.AssignForeachContext ctx) {
    ExprAtomContext setExprCtx = ctx.foreachVars().exprAtom();
    String var = getCpp(setExprCtx);

    DataType setType = _varType.getType(ctx.foreachVars().exprAtom());
    DataType varType = setType.ElementTypeList.get(0);

    // if setExpr is a range object, update its value
    String cpp =
        setExprCtx instanceof GSQLParser.SetRangeIntervalContext
            ? genRangeInitialization(((GSQLParser.SetRangeIntervalContext) setExprCtx))
            : "";

    // For each loop, have a different __GQUER__step_n.
    long currentLoopLevel = Util.getLevelOfLoop(ctx);
    // timeout check every 4k iteration
    String step = "__GQUERY__step_" + currentLoopLevel;
    String stepVar = "__GQUERY__stepvar_" + currentLoopLevel;

    cpp += "uint64_t " + step + " = 0;\n"
        + "const auto& " + stepVar + " = " + var + ";\n";

    cpp +=
        "  for (auto it = ("
            + stepVar
            + ")"
            + ".begin(); "
            + "it != ("
            + stepVar
            + ")"
            + ".end(); it++) {\n";

    //map is special
    if ("MapAccum".equals(setType.Name)) {
      // map.key
      cpp +=
          "    auto& "
              + FOREACH_PREFIX
              + ctx.foreachVars().ident().get(0).getText()
              + " = it->first;\n";

      // map.val
      varType = setType.ElementTypeList.get(1);
      cpp +=
          "    auto& "
              + FOREACH_PREFIX
              + ctx.foreachVars().ident().get(1).getText()
              + " = it->second;\n";
    } else if ("GroupByAccum".equals(setType.Name)) {
      if (ctx.foreachVars().ident().size() > 1) {
        // here we assume the NAME().size match setType.ElementTypeNames.size
        for (int i = 0; i < ctx.foreachVars().ident().size(); ++i) {
          String iterator = "it->first";
          if (setType.ElementTypeList.get(i).Type.equals(DataType.Category.Accum)) {
            iterator = "it->second";
          }
          String accessor = "." + setType.ElementTypeNames.get(i);

          // If GroupByAccum contains only one key or value, it is implemented with the
          // DataType directly instead of using a Tuple in the MapAccum  declaration.
          // i.e. "MapAccum<int64_t, accumTuple>, MapAccum<baseTuple, SumAccum<int64_t>,
          // or MapAccum<int64_t, SumAccum<int64_t>. We can remove the
          // class member accessor, since the field can be accessed directly.
          // i.e "map.base_0" instead of "map.base_0.fieldName".
          int fieldSize = setType.checkFieldSize(setType.ElementTypeNames.get(i));
          if (fieldSize == 1) {
            accessor = "";
          }

          String value = iterator + accessor;
          // e.g. const int& g = it->first.baseTuple.t1
          // e.g. string& g = it->second.accumTuple.t2
          cpp +=
              "    auto& "
                  + FOREACH_PREFIX
                  + ctx.foreachVars().ident().get(i).getText()
                  + " = "
                  + value
                  + ";\n";
        }
      } else {
        //only has one foreach var
        //but we need to generate two for first and second
        cpp +=
            "    auto& "
                + FOREACH_PREFIX
                + ctx.foreachVars().ident().get(0).getText()
                + "_first = it->first;\n";
        cpp +=
            "    auto& "
                + FOREACH_PREFIX
                + ctx.foreachVars().ident().get(0).getText()
                + "_second = it->second;\n";
      }

    } else {
      cpp +=
          "    auto& " + FOREACH_PREFIX + ctx.foreachVars().ident().get(0).getText() + " = *it;\n";
    }

    cpp += getCpp(ctx.assignments()) + "\n";
    // timeout check
    cpp += CodeGenUtil.getCheckAbortedAndTimeoutCpp(step, true, false);
    cpp += "  }\n";

    //TODO cpp flag
    String postAccumFlag = this.popPostAccumFlag4CaseWhen(ctx);

    // expr flags for assignments
    cpp = this.applyAttFlag(cpp, ctx.foreachVars().exprAtom());

    if (postAccumFlag != null) {
      cpp = "  if (" + postAccumFlag + ") {\n" + cpp + "\n  }\n";
    }

    // create a new name scope for RangeObject/TimeOutCheck var
    cpp = "{\n" + cpp + "}\n";

    saveCpp(ctx, cpp);
  }

  public void exitAssignLoop(GSQLParser.AssignLoopContext ctx) {
    // Get the cpp code of while condition.
    ConditionContext condCtx = ctx.condition();
    String whileLoopCondtion = getCpp(condCtx);

    // For while loop, have a different __GQUER__step_n.
    long currentLoopLevel = Util.getLevelOfLoop(ctx);
    // timeout check every 4k iteration
    String step = "__GQUERY__step_" + currentLoopLevel;
    // Declare string variables for limit maxIteration
    String stepCondition = "";
    // use int64_t instead of uint64_t for the data type of step can avoid the error
    // caused by a negative value is assigned to an unsigned variable
    String stepDecl = "int64_t " + step + " = 0;\n";
    String stepIncrease = step + "++;\n";

    // If there is a limit clause,
    // use __GQUER__step to control the number of iteration
    if (ctx.maxIter() != null) {
      MaxIterContext maxIterCtx = ctx.maxIter();
      String maxIterText = "";
      if (maxIterCtx.CONST_INT() != null) {
        maxIterText = maxIterCtx.CONST_INT().getText();
      } else {
        maxIterText = _cppCode.get(maxIterCtx.rhsIdent());
      }

      stepCondition = " && (" + step + " < " + maxIterText + ")";
    }
    String cpp =
        stepDecl
            +  "{\n  while ("
            + whileLoopCondtion
            + stepCondition
            + ") {";

    cpp += getCpp(ctx.assignments()) + "\n";
    cpp += StringUtil.padLeftSpaces(4, stepIncrease);
    // timeout check
    cpp += CodeGenUtil.getCheckAbortedAndTimeoutCpp(step, false, false);
    cpp += "  }\n}\n";

    //TODO cpp flag
    String postAccumFlag = this.popPostAccumFlag4CaseWhen(ctx);

    if (postAccumFlag != null) {
      cpp = "  if (" + postAccumFlag + ") {\n" + cpp + "\n  }\n";
    } else {
      // create a new name scope for TimeOutCheck var
      cpp = "{\n" + cpp + "}\n";
    }

    saveCpp(ctx, cpp);
  }

  // POST-ACCUM
  @Override
  public void enterPostAccumClause(GSQLParser.PostAccumClauseContext ctx) {
    String bindingAlias = _varType.getPostAccumBinding(ctx);
    if (!"".equals(bindingAlias)) {
      String normalizedName = _varType.GetCanonicalName(bindingAlias, ctx);
      if ("src".equals(normalizedName)) {
        setPostAccumFlag(ctx, SRC_FLAG);
      } else if ("tgt".equals(normalizedName)) {
        setPostAccumFlag(ctx, TGT_FLAG);
      }
    }
  }

  @Override
  public void exitPostAccumClause(GSQLParser.PostAccumClauseContext ctx) {
    String cpp =
        "   // post-accum\n" + getCpp(ctx.assignments());
    saveCpp(ctx, cpp);
  }

  public void exitFuncRval(GSQLParser.FuncRvalContext ctx) {

    String funcName = ctx.funcName().getText();

    // If evaluate function is in post accum clause, we cannot judge
    // in compile time whether src or tgt is mentioned in the expression
    // because it is dynamic. Thus we write to src and tgt both.
    //
    // If in VStep, there is no tgt, so don't do tgt activation
    if (funcName.toLowerCase().equals("evaluate")
        && Util.InPostAccum(ctx)
        && isEdgeTraversal(ctx)) {
      _writing2src = true;
      _writing2tgt = true;
    }
  }

  public void exitAssignVFunctRval(GSQLParser.AssignVFunctRvalContext ctx) {
    if (ctx.exprAtom().getChild(0) instanceof VAccRvalContext) {
      String assignCpp = getCpp(ctx) + ";\n";

      // flag for assignments
      assignCpp = this.applyAttFlag(assignCpp, ctx);

      saveCpp(ctx, assignCpp);
    } else if (ctx.exprAtom().getChild(0) instanceof AllTupleFieldAccessContext
        || ctx.exprAtom() instanceof ExprRhsIdentContext) {

      // if attrRval is used in post accum, we need to send message
      ForeachItem item = _varType.GetForeachItem(ctx.exprAtom().getText(), ctx);
      if (!Util.InPostAccum(ctx) || item != null) {
        return;
      }

      String origVarName = ctx.exprAtom().getChild(0).getText();
      String normalizedName = normalizeVarName(origVarName);

      // we need to send message to the source vertex
      _writing2src |= "src".equals(normalizedName);

      // we need to send message to the target vertex
      _writing2tgt |= "tgt".equals(normalizedName);
    } else {
      RuleContext pCtx = findPostAccumRootContext(ctx);
      if (pCtx != null) {
        // This attribute assignment is nested in Post-Accum
        // need to add the Post-Accum accum declaration to each assignment
        String assignCpp = getCpp(ctx);
        assignCpp = getCpp(pCtx).replace("@@@_UPDATE_ATT_CPP", assignCpp);
        saveCpp(ctx, assignCpp);
      }
    }
  }

  /* check if a clause is nested inside a Post-Accum clause
     if it is, return the PostAccum root context
   */
  private RuleContext findPostAccumRootContext(RuleContext ctx) {
    RuleContext pCtx = ctx;
    while (pCtx != null && !(pCtx instanceof GSQLParser.PostAccumClauseContext)) {
      pCtx = pCtx.getParent();
    }
    return (pCtx != null && (pCtx instanceof GSQLParser.PostAccumClauseContext)) ? pCtx : null;
  }

  // SET clause
  public void exitSetClause(GSQLParser.SetClauseContext ctx) {

    String cpp =
        "   // set clause\n" + getCpp(ctx).replace("@@@_UPDATE_ATT_CPP", getCpp(ctx.assignments()));

    saveCpp(ctx, cpp);
  }

  //////////////
  // helper fns
  /////////////

  private String replaceAttrUpdatePlaceHolder(ParserRuleContext attrLval, ParserRuleContext expr) {
    // attrCpp has format
    //  int tp = context->GraphAPI()->GetVertexType(src);
    //  if (tp == _schema["VTY_xx1"]) {
    //    attrUpdate_src->Set(_schema["VATT_xx1_yy"], @@@_NEW_VALUE);
    //  } else if (tp == _schema["VTY_xx2"]) {
    //    attrUpdate_src->Set(_schema["VATT_xx2_yy"], @@@_NEW_VALUE);
    //  } else if (...) {
    //    ...
    //  }
    //  @@@_NEW_VALUE is placeholder that will be replaced by the new value want to update.
    String attrCpp = getCpp(attrLval);
    // expr cpp
    DataType dt = _varType.getType(attrLval);
    String exprCpp =
        dt.Name.equals("string")
            ? "lval_update_str_attr"
            : dt.toAttrTypeCpp() + "(" + getCpp(expr) + ")";
    // generate attribute update code
    String assignCpp = attrCpp.replace("@@@_NEW_VALUE", exprCpp);

    // for string type attribute, change code to
    // {
    //   string lval_update_str_attr = expr;
    //   ...
    // }
    if (dt.Name.equals("string")) {
      assignCpp = "{\n  string lval_update_str_attr = " + getCpp(expr) + ";\n" + assignCpp + "}\n";
    }

    return assignCpp;
  }

  private String prepareEvalCpp(AssignmentContext ctx) {
    Set<String> flags = _exprCppFlagTree.get(ctx);
    String code = getCpp(ctx);
    if (code == null) {
      code = "";
    }
    if (flags == null || code.equals("")) {
      return code.replace("@@@_POST_SCOPE", "SCOPE_BOTH");
    }

    if (flags.contains(SRC_FLAG)) {
      return "if (" + SRC_FLAG + ") {\n" + code.replace("@@@_POST_SCOPE", "SCOPE_SRC") + "}\n";
    }

    if (flags.contains(TGT_FLAG)) {
      return "if (" + TGT_FLAG + ") {\n" + code.replace("@@@_POST_SCOPE", "SCOPE_TGT") + "}\n";
    }

    return code.replace("@@@_POST_SCOPE", "SCOPE_BOTH");
  }

  private String genStageMapDelete(DeleteBlockContext ctx) {

    String fromCpp = "";
    String whereCpp = "";
    String deleteCpp = "";

    // delete src or tgt
    if (_activatingSrc || _activatingTgt) {
      fromCpp = "\n   // DELETE\n " + getCpp(ctx.deleteClause()) + "\n";
    }

    whereCpp += "\n   // WHERE";
    if (ctx.whereClause() != null) {
      whereCpp += "\n   if (!(" + getCpp(ctx.whereClause()) + ")) return;\n";
    }

    deleteCpp += "\n";

    // when none of src and tgt is selected then remove edge
    if (_activatingSrc == false && _activatingTgt == false) {
      deleteCpp += CodeGenUtil.udfDeltaIdWithIID("srcVid", "src")
          + CodeGenUtil.udfDeltaIdWithIID("tgtVid", "tgt");
      deleteCpp += "graphupdates->DeleteEdge " + "(srcVid, tgtVid, e_attr->type(), "
          + "e_attr->subtype(), "
          + "e_attr->discriminator_binarysize_, e_attr->GetDiscriminatorPtr());\n";
    }

    String deltaCpp = "\n\n" + "   // prepare messages\n";
    final String deltaType = CodeGenUtil.toCppName("Delta");
    // when it is removing src
    if (_activatingSrc) {
      String deltaMsg = String.format("   %s src_delta = %s ();\n", deltaType, deltaType);
      deltaCpp += deltaMsg + "   src_delta." + CodeGenUtil.toCppName("isSrc") + " = true;\n";
      // when it is removing tgt
    } else if (_activatingTgt) {
      String deltaMsg = String.format("   %s tgt_delta = %s ();\n", deltaType, deltaType);
      deltaCpp += deltaMsg + "   tgt_delta." + CodeGenUtil.toCppName("isTgt") + " = true;\n";
    }

    String writeCpp = "\n   // send messages\n";
    if (_activatingSrc)
      writeCpp += "   context->Write (src, src_delta);\n";
    if (_activatingTgt)
      writeCpp += "   context->Write (tgt, tgt_delta);\n";

    String mapCpp =
        getEdgeSignatureCpp(ctx)
            + this.getAttDeclAssignCpp("map")
            + whereCpp
            + deltaCpp
            + deleteCpp
            + "   "
            + fromCpp
            + writeCpp
            + commonVariable.LOG_EngineHigh;

    if (isEdgeTraversal(ctx)) {
      mapCpp +=
          "\"Exit function "
              + "EdgeMap_"
              + _pc
              + " src: \""
              + " << src << \" tgt \" << tgt << std::endl;\n";
    } else {
      mapCpp += "\"Exit function " + "VertexMap_" + _pc + " src: \"" + " << src << std::endl;\n";
    }

    mapCpp += "}\n";

    saveCpp(ctx, mapCpp);
    return mapCpp;
  }

  private String getEdgeSignatureCpp(ParserRuleContext ctx) {
    String mapSignature = "";
    StatementContext stmt = Util.GetStatementContext(ctx);

    if (isEdgeTraversal(stmt)) {
      mapSignature +=
          "void EdgeMap_"
              + _pc
              + " (const VERTEX& src,\n"
              + "                V_ATTR*                src_attr,\n"
              + "                const V_VALUE&         src_val,\n"
              + "                const VERTEX& tgt,\n"
              + "                V_ATTR*                tgt_attr,\n"
              + "                const V_VALUE&         tgt_val,\n"
              + "                E_ATTR*                e_attr,\n"
              + "                gpelib4::SingleValueMapContext<MESSAGE>* context) {\n"
              + "   "
              + commonVariable.LOG_EngineHigh
              + "\"Enter function EdgeMap_"
              + _pc
              + " src: \" << src << "
              + "\" tgt: \" << tgt << std::endl;\n";

    } else {
      mapSignature =
          "void VertexMap_"
              + _pc
              + " (const VERTEX& src,\n"
              + "                  V_ATTR*                src_attr,\n"
              + "                  const V_VALUE&         src_val,\n"
              + "                  gpelib4::SingleValueMapContext<MESSAGE>* context) {\n"
              + "   "
              + commonVariable.LOG_EngineHigh
              + "\"Enter function VertexMap_"
              + _pc
              + " src: \" << src << std::endl;\n";
    }

    // [CORE-1369] check memory in critical when enter edge map or vertex map
    mapSignature +=
        StringUtil.padLeftSpacesBatch(3, CodeGenUtil.getCheckAbortedAndTimeoutCpp());

    return mapSignature;
  }

  private String genStageMap(ParserRuleContext ctx) {
    String selectCpp = "";
    String fromCpp = "";
    String whereCpp = "";
    String accumCpp = "";

    if (ctx instanceof UpdateBlockContext) {
      UpdateBlockContext blockContext = (UpdateBlockContext) ctx;
      // if it is updating src or tgt
      if (_activatingSrc || _activatingTgt) {
        selectCpp = "\n   // SELECT\n" + getCpp(blockContext.updateClause()) + "\n";
      }
      fromCpp = "\n   // FROM\n   if (!(" + getCpp(blockContext.fromClause()) + ")) return;\n";
      if (blockContext.whereClause() != null) {
        whereCpp += "\n   // WHERE";
        whereCpp += "\n   if (!(" + getCpp(blockContext.whereClause()) + ")) return;\n";
      }

      // when updating the edge
      if (_activatingSrc == false && _activatingTgt == false) {
        if (blockContext.setClause() != null) {
          accumCpp = "\n   // SET\n   ";
          accumCpp += getCpp(blockContext.setClause()) + "\n";
        }
      }
    } else if (ctx instanceof BlockContext) {
      BlockContext blockContext = (BlockContext) ctx;
      selectCpp = "\n   // SELECT\n" + getCpp(blockContext.selectClause()) + "\n";
      fromCpp = "\n   // FROM\n   if (!(" + getCpp(blockContext.fromClause()) + ")) return;\n";
      if (blockContext.whereClause() != null) {
        whereCpp += "\n   // WHERE";
        whereCpp += "\n   if (!(" + getCpp(blockContext.whereClause()) + ")) return;\n";
      }

      if (blockContext.accumClause() != null) {
        accumCpp = "\n   // ACCUM\n   ";
        accumCpp += getCpp(blockContext.accumClause()) + "\n";
      }
    }

    String deltaCpp = "\n\n" + "   // prepare messages\n";
    final String deltaType = CodeGenUtil.toCppName("Delta");
    if (_writing2src || _activatingSrc) {
      String deltaMsg = String.format("   %s src_delta = %s ();\n", deltaType, deltaType);
      deltaCpp += deltaMsg + "   src_delta." + CodeGenUtil.toCppName("isSrc") + " = true;\n";
    }

    if (_writing2tgt || _activatingTgt) {
      String deltaMsg = String.format("   %s tgt_delta = %s ();\n", deltaType, deltaType);
      deltaCpp += deltaMsg + "   tgt_delta." + CodeGenUtil.toCppName("isTgt") + " = true;\n";
    }

    String writeCpp = "\n   // send messages\n";
    if (_writing2src || _activatingSrc)
      writeCpp += "   context->Write (src, src_delta);\n";
    if (_writing2tgt || _activatingTgt)
      writeCpp += "   context->Write (tgt, tgt_delta);\n";

    String setDeclareCpp = getDeclareCpp("Map_" + _pc);
    String vFunctRvalCode = getVFunctRvalCpp("Map_" + _pc);

    String mapCpp =
        getEdgeSignatureCpp(ctx)
            + fromCpp
            + this.getAttDeclAssignCpp("map")
            + setDeclareCpp
            + vFunctRvalCode
            + whereCpp
            + deltaCpp
            + accumCpp
            + selectCpp
            + writeCpp
            + "   "
            + commonVariable.LOG_EngineHigh;

    StatementContext stmt = Util.GetStatementContext(ctx);

    if (isEdgeTraversal(stmt)) {
      mapCpp +=
          "\"Exit function "
              + "EdgeMap_"
              + _pc
              + " src: \""
              + " << src << \" tgt \" << tgt << std::endl;\n";
    } else {
      mapCpp += "\"Exit function " + "VertexMap_" + _pc + " src: \"" + " << src << std::endl;\n";
    }

    mapCpp += "}\n";

    saveCpp(ctx, mapCpp);
    return mapCpp;
  }

  /**
   * This function generates the attrbutes' local variables declaration and assignments c++ code.
   *
   * @param stage indicates map or reduce stage
   */
  private String getAttDeclAssignCpp(String stage) {

    // get the maps from property trees
    Map<String, DataType> _AttLvarDeclMap = _AttLocalVarDeclMapTree.get(_currBlockCtx).get(stage);
    Map<String, String> _AttLvarAssignMap = _AttLocalVarAssignMapTree.get(_currBlockCtx).get(stage);

    // validate check, return empty string if we
    // cannot find from the tree
    if (_AttLvarDeclMap == null || _AttLvarAssignMap == null) {
      return "";
    }

    /** ** 1. declaration code generation ***** */
    String attDeclCpp = "\n   //attributes' local var declaration\n";
    // loop Map(tgt_att1_int --> DataType)
    for (Map.Entry<String, DataType> entry : _AttLvarDeclMap.entrySet()) {
      // type tgt_att1_type = default;
      // bool tgt_att1_type_flag = false;
      attDeclCpp += "   " + entry.getValue().toCpp() + " " + entry.getKey();

      String initVal = entry.getValue().getInitVal();
      if (initVal != null) {
        attDeclCpp += " = " + initVal;
      }
      attDeclCpp += ";\n";

      // This is because for evaluate function, I put evalFlag directly
      // as a local variable. It is declared by above sentence.
      if (!entry.getKey().endsWith(FLAG_SUFFIX)) {
        attDeclCpp += "   bool " + entry.getKey() + FLAG_SUFFIX + " = false" + ";\n";
      }
    }

    /** ** 2. assignments code generation ***** */
    String attAssignCpp = "\n";
    // define a Map(src --> Map(V1 --> c++ code) )
    Map<String, Map<String, String>> attMap = new HashMap<String, Map<String, String>>();

    // loop Map (src|V1 --> c++ code)
    for (Map.Entry<String, String> entry : _AttLvarAssignMap.entrySet()) {
      String[] tmp = entry.getKey().split("\\|", -1);
      String normName = tmp[0]; // src/e/tgt
      String vName = tmp[1]; // V1/V2

      Map<String, String> tmpMap = new HashMap<String, String>();
      // if src/e/tgt has a map in attMap already
      if (attMap.get(normName) != null) tmpMap = attMap.get(normName);

      // append code that already in the map
      String oldCpp = tmpMap.get(vName);
      if (oldCpp == null) oldCpp = "";

      tmpMap.put(vName, oldCpp + entry.getValue());
      attMap.put(normName, tmpMap);
    }

    // loop the map we just built, and generate the if/else statements for src/e/tgt
    for (Map.Entry<String, Map<String, String>> entry : attMap.entrySet()) {
      boolean isEdge = entry.getKey().equals("e");
      attAssignCpp += "   //get " + entry.getKey() + "'s attribute\n";
      String typeAPI =
          isEdge
              ? "e_attr->type()"
              : "context->GraphAPI()->GetVertexType(" + entry.getKey() + ")";
      //typeIDVarName is a temporal name for typeAPI used in the folliwng if/else if statement
      String typeIDVarName = Util.getAttrTypeIDVar(entry.getKey());
      attAssignCpp += "   int " + typeIDVarName  + " = "  + typeAPI + ";\n";
      int isFirst = 0;
      for (Map.Entry<String, String> codeEntry : entry.getValue().entrySet()) {
        // for getAttr() we inserted empty entry
        if (codeEntry.getKey().isEmpty()) continue;
        String typeIdx = isEdge
            ? Util.GetEdgeTypeVarName(codeEntry.getKey(), commonVariable)
            : Util.GetVertexTypeVarName(codeEntry.getKey(), commonVariable);
        attAssignCpp += ( isFirst++ == 0 ? "     if (" : " else if (" )
                     + typeIDVarName
                     + " == "
                     + typeIdx + ") {\n       ";
        // get the assign code and format with space
        attAssignCpp += (codeEntry.getValue()).replace("\n", "\n     ") + "}";
      }
    }

    return attDeclCpp + attAssignCpp + "\n";
  }

  private void genFixedMapReduce(GSQLParser.JobContext ctx) {
    String eMapCpp =
        "\n"
            + "\n"
            + "void (UDF_"
            + _queryName
            + "::*edgemap) (const VERTEX& src,\n"
            + "                 V_ATTR*                src_attr,\n"
            + "                 const V_VALUE&         src_val,\n"
            + "                 const VERTEX& tgt,\n"
            + "                 V_ATTR*                tgt_attr,\n"
            + "                 const V_VALUE&         tgt_val,\n"
            + "                 E_ATTR*                e_attr,\n"
            + "                 gpelib4::SingleValueMapContext<MESSAGE>* context);\n"
            + "\n"
            + "ALWAYS_INLINE "
            + "void EdgeMap (const VertexLocalId_t& src,\n"
            + "              V_ATTR*                src_attr,\n"
            + "              const V_VALUE&         src_val,\n"
            + "              const VertexLocalId_t& tgt,\n"
            + "              V_ATTR*                tgt_attr,\n"
            + "              const V_VALUE&         tgt_val,\n"
            + "              E_ATTR*                e_attr,\n"
            + "              gpelib4::SingleValueMapContext<MESSAGE>* ctx) {\n"
            + "\n"
            + "   (this->*(edgemap))(VERTEX(src), src_attr, src_val,"
            + " VERTEX(tgt), tgt_attr, tgt_val, e_attr, ctx);\n"
            + "}\n\n";

    String vMapCpp = "";
    if (_vMapNeeded)
      vMapCpp +=
          "\n"
              + "\n"
              + "void (UDF_"
              + _queryName
              + "::*vertexmap) (const VERTEX& src,\n"
              + "                   V_ATTR*                src_attr,\n"
              + "                   const V_VALUE&         src_val,\n"
              + "                   gpelib4::SingleValueMapContext<MESSAGE>* context);\n"
              + "\n"
              + "ALWAYS_INLINE "
              + "void VertexMap (const VertexLocalId_t& src,\n"
              + "                V_ATTR*                src_attr,\n"
              + "                const V_VALUE&         src_val,\n"
              + "                gpelib4::SingleValueMapContext<MESSAGE>* ctx) {\n"
              + "\n"
              + "   (this->*(vertexmap)) (VERTEX(src), src_attr, src_val, ctx);\n"
              + "}\n\n";

    String deltaMsg = "delta,\n";
    String deltaMsg2 =
        "    (this->*(reduce))(VERTEX(v), v_attr, v_val, delta, context, _request);\n";

    String reduce =
        "\n"
            + "\n"
            + "void (UDF_"
            + _queryName
            + "::*reduce) (const VERTEX&                v,\n"
            + "                V_ATTR*                               v_attr,\n"
            + "                const V_VALUE&                        v_val,\n"
            + StringUtil.padLeftSpaces(16, "MESSAGE&" + StringUtil.padLeftSpaces(30, deltaMsg))
            + "                gpelib4::SingleValueContext<V_VALUE>* context,\n"
            + "                gpelib4::EngineServiceRequest&                 request);\n"
            + "\n"
            + "ALWAYS_INLINE "
            + "void Reduce (const VertexLocalId_t&       v,\n"
            + "                           V_ATTR*                      v_attr,\n"
            + "                           const V_VALUE&               v_val,\n"
            + StringUtil.padLeftSpaces(27, "MESSAGE&" + StringUtil.padLeftSpaces(21, deltaMsg))
            + "                           gpelib4::SingleValueContext<V_VALUE>* context) {\n"
            + "\n"
            + deltaMsg2
            + "}\n\n";

    saveCpp(ctx, eMapCpp + vMapCpp + reduce);
    _udfCpp.append(eMapCpp + vMapCpp + reduce);
  }

  private String genStageReduce(GSQLParser.BlockContext ctx) {
    String deltaMsg = "                MESSAGE&                              delta,\n";

    String reduceCpp =
        "\n"
            + "void Reduce_"
            + _pc
            + " (const VERTEX&                 v,\n"
            + "                V_ATTR*                               v_attr,\n"
            + "                const V_VALUE&                        v_val,\n"
            + deltaMsg
            + "                gpelib4::SingleValueContext<V_VALUE>* context,\n"
            + "                gpelib4::EngineServiceRequest&                 request) {\n"
            + "   "
            + commonVariable.LOG_EngineHigh
            + "\"Enter function Reduce_"
            + _pc
            + " v: \" << v << std::endl;\n"
            // [CORE-1369] check memory in critical when enter reduce
            + StringUtil.padLeftSpacesBatch(3, CodeGenUtil.getCheckAbortedAndTimeoutCpp())
            + "\n"
            + "   // Note that v_val is a const reference so it cannot be modified.\n"
            + "   // Make local non-const copy of v_val (using copy constructor) first\n"
            + "   // then apply delta to it (using += operator):\n";

    // update vertex value if the reduce need v_val, we have already set the value
    // in UDFSettingsCollector if the v_val is changed
    if ((_settings.get(ctx.getParent()).ReduceSetting & UDFSettings.VValueRequired)
        == UDFSettings.VValueRequired) {
      reduceCpp +=
          commonVariable.VValueUsePointer
              ? ("   V_VALUE l_val = V_VALUE(new " + CodeGenUtil.toCppName("VertexVal")
                  + "(*v_val));\n"
                  + "   (*l_val) += delta;\n")
              : "   V_VALUE l_val = (V_VALUE (v_val) += delta);\n";
    }

    reduceCpp += this.getAttDeclAssignCpp("reduce");
    reduceCpp += "\n" + getDeclareCpp("Reduce_" + _pc);
    reduceCpp += getVFunctRvalCpp("Reduce_" + _pc);

    // post-accum clause is optional, check existence
    if (!ctx.postAccumClause().isEmpty()) reduceCpp += "\nif (delta."
      + CodeGenUtil.toCppName("isSrc")
      + "|| delta." + CodeGenUtil.toCppName("isTgt") + ") {\n"
      + getCpp(ctx.postAccumClause()) + "}\n";

    reduceCpp +=
        "\n"
            + "   // Will this vertex be active in next iteration?\n"
            + "   // This depends on both whether select clause said so (communicated in delta)\n"
            + "   // and whether the new vertex value passes the having clause filter (if any):\n";

    // prepend having clause's evaluate code.
    if (ctx.havingClause() != null) {
      String code = getCpp(ctx.havingClause());
      if (code != null) {
        reduceCpp +=
            (_activatingSrc ? "   if (delta." + CodeGenUtil.toCppName("isSrc") + ") {\n" : "   "
                + "if (delta." + CodeGenUtil.toCppName("isTgt") + ") {\n") + code + "}\n";
      }
    }

    reduceCpp += "   bool " + CodeGenUtil.toCppName("activate") + " = delta."
        + CodeGenUtil.toCppName("activate");

    // having clause is optional, check its existence:
    if (ctx.havingClause() != null) {
      reduceCpp += "\n       && " + getCpp(ctx.havingClause().condition());
    }

    String limitCpp = "";
    String ifCond =
        _activatingSrc
            ? "   if (" + CodeGenUtil.toCppName("activate") + " && delta."
            + CodeGenUtil.toCppName("isSrc") + ") {\n"
            : "   if (" + CodeGenUtil.toCppName("activate") + " && delta."
            + CodeGenUtil.toCppName("isTgt") + ") {\n";
    String tupleName = LIMIT_TUPLE_NAME_PREFIX + _pc;

    if (ctx.orderClause() != null) {
      String constructorParams = "v";
      String orderKeyTmp = "";
      // Append evaluate code to If condition
      String code = getCpp(ctx.orderClause());
      if (code != null) {
        ifCond += code;
      }

      int idx = 1;
      for (GSQLParser.OrderKeyContext key : ctx.orderClause().orderKey()) {
        String name = "__tmp_orderkey_" + idx;
        String flag = getAttFlags(key.exprAtom());
        String value = _cppCode.get(key.exprAtom());
        if (!flag.isEmpty()) {
          value = flag + "? " + value + " : ";
          // ASC  --> max val (smaller values are chosen)
          // DESC --> min val (larger values are chosen)
          if (key.direction() != null && key.direction().DESC() != null) {
            value += _varType.getType(key.exprAtom()).MinValue();
          } else {
            value += _varType.getType(key.exprAtom()).MaxValue();
          }
        }
        orderKeyTmp += "     auto " + name + " = " + value + ";\n";
        constructorParams += ", " + name;
        ++idx;
      }
      limitCpp = "\n   //push limit k to global heap\n" + ifCond;

      String heapType =
          "HeapAccum<" + tupleName + ", " + tupleName + "Compare<" + tupleName + "> > ";

      limitCpp += orderKeyTmp;

      limitCpp +=
          "     context->GlobalVariable_Reduce<"
              + heapType
              + ">(GV_SYS_"
              + tupleName
              + ", "
              + heapType
              + "("
              + tupleName
              + "("
              + constructorParams
              + ")));\n   }\n";
    } else if (ctx.limitClause() != null) {
      // limit without orderby
      String setType = "SetAccum<VertexLocalId_t>";
      limitCpp = "\n   //push limit k to global set\n"
          + ifCond
          + "     context->GlobalVariable_Reduce<"
          + setType
          + ">(GV_SYS_"
          + tupleName
          + ", "
          + setType
          + "(v));\n   }\n";
    }

    reduceCpp +=
        ";\n"
            + limitCpp
            + "\n"
            +
            // if v_val is needed, activate v and update v_val
            // o/w activate v only
            (((_settings.get(ctx.getParent()).ReduceSetting & UDFSettings.VValueRequired) == 2)
                ? "   // Overwrite vertex value and activate if appropriate\n"
                    + "   context->Write (v, l_val, " + CodeGenUtil.toCppName("activate") + ");\n"
                : "   if (" + CodeGenUtil.toCppName("activate") + "&&!__GQUERY__all_vetex_mode)"
                + " context->SetActiveFlag(v);\n")
            + "   "
            + commonVariable.LOG_EngineHigh
            + "\"Exit function Reduce_"
            + _pc
            + " v: \" << v << std::endl;\n"
            + "}\n";

    return reduceCpp;
  }

  private String genReduceForGraphUpdate(UpdateBlockContext ctx) {
    String deltaMsg = "MESSAGE&                              delta,\n";

    String updateCpp = "";

    if (_activatingSrc || _activatingTgt) {
      updateCpp += getDeclareCpp("Reduce_" + _pc);
      updateCpp += getVFunctRvalCpp("Reduce_" + _pc);
      updateCpp += this.getAttDeclAssignCpp("reduce");
      updateCpp += getCpp(ctx.setClause());
    }

    String reduceCpp =
        "\n"
            + "void Reduce_"
            + _pc
            + " (const VERTEX&                 v,\n"
            + "                V_ATTR*                               v_attr,\n"
            + "                const V_VALUE&                        v_val,\n"
            + StringUtil.padLeftSpaces(16, deltaMsg)
            + "                gpelib4::SingleValueContext<V_VALUE>* context,\n"
            + "                gpelib4::EngineServiceRequest&                 request) {\n"
        ;

    // update vertex value if the reduce need v_val, we have already set the value
    // in UDFSettingsCollector if the v_val is changed
    // since update clause won't have delta, so just make l_val an alias of v_val
    if ((_settings.get(ctx.getParent()).ReduceSetting & UDFSettings.VValueRequired)
        == UDFSettings.VValueRequired) {
      reduceCpp +=
          "   const V_VALUE& l_val = v_val;\n";
    }

    reduceCpp += updateCpp + "}\n";

    return reduceCpp;
  }

  private String genReduceForGraphDelete(DeleteBlockContext ctx) {
    String deltaMsg = "MESSAGE&                              delta,\n";

    String deleteCpp = "";

    if (_activatingSrc || _activatingTgt) {
      deleteCpp += CodeGenUtil.udfDeltaIdWithIID("vVid", "v");
      deleteCpp += "graphupdates->DeleteVertex " + "(false, vVid);\n";
    }

    String reduceCpp =
        "\n"
            + "void Reduce_"
            + _pc
            + " (const VERTEX&                 v,\n"
            + "                V_ATTR*                               v_attr,\n"
            + "                const V_VALUE&                        v_val,\n"
            + StringUtil.padLeftSpaces(16, deltaMsg)
            + "                gpelib4::SingleValueContext<V_VALUE>* context,\n"
            + "                gpelib4::EngineServiceRequest&                 request) {\n"
            + deleteCpp
            + "}\n";
    return reduceCpp;
  }

  private String genRangeInitialization(GSQLParser.SetRangeIntervalContext ctx) {
    String startExpr = getCpp(ctx.rangeInterval().exprAtom(0));
    String finishExpr = getCpp(ctx.rangeInterval().exprAtom(1));
    String stepExpr = "1"; // STEP in RangeObject is 1 by default
    if (ctx.rangeInterval().ident() != null) {
      stepExpr = getCpp(ctx.rangeInterval().exprAtom(2));
    }
    return "  RangeObject "
        + RANGE_OBJECT_VARNAME
        + " = RangeObject("
        + startExpr
        + ", "
        + finishExpr
        + ", "
        + stepExpr
        + ", context, _request);\n"
        + "  if (!"
        + RANGE_OBJECT_VARNAME
        + ".is_valid()) {\n"
        + "    return;\n"
        + "  }\n";
  }

  private String normalizeVarName(String alias) {
    return _normMap.get(alias);
  }

  // does this alias refer to source?
  private boolean isSrc(GSQLParser.VarContext ctx) {
    return (normalizeVarName(ctx.ident().getText())).equals("src");
  }

  private boolean isTgt(GSQLParser.VarContext ctx) {
    return (normalizeVarName(ctx.ident().getText())).equals("tgt");
  }

  private boolean isE(GSQLParser.VarContext ctx) {
    return (normalizeVarName(ctx.ident().getText())).equals("e");
  }


  private void saveCpp(ParserRuleContext ctx, String code) {
    _cppCode.put(ctx, new String(code));
  }

  private String getCpp(ParseTree ctx) {
    return _cppCode.get(ctx);
  }

  private String getCpp(List<PostAccumClauseContext> ctxList) {
    StringJoiner joiner = new StringJoiner("\n");
    for (PostAccumClauseContext ctx : ctxList) {
      joiner.add(_cppCode.get(ctx));
    }
    return joiner.toString();
  }

  private String getDeclareCpp(String key) {
    String res = _setDeclare.get(key);

    if (res == null) {
      return "";
    } else {
      return res;
    }
  }

  private String getVFunctRvalCpp(String key) {
    String res = _vFunctRvalCodeMap.get(key);

    if (res == null) {
      return "";
    } else {
      return "\n   // vFunctRval\n" + res;
    }
  }

  private String getAttFlags(RuleContext ctx) {
    String flags = "";
    if (ctx.getParent().getParent().getParent() instanceof PostAccumClauseContext) {
      // generate flags from both POST-ACCUM and itself
      AssignmentsContext as = (AssignmentsContext) ctx.getParent().getParent();
      Set<String> flagSet = new HashSet<>();
      if (_exprCppFlagTree.get(ctx) != null)
        flagSet.addAll(_exprCppFlagTree.get(ctx));
      if (_exprCppFlagTree.get(as) != null)
        flagSet.addAll(_exprCppFlagTree.get(as));
      for (String flag : flagSet) {
        if (!flags.isEmpty()) flags += " && ";
        flags += flag;
      }
      return flags;
    }

    if (_exprCppFlagTree.get(ctx) == null) {
      return "";
    }
    // att flag for assignments
    for (String flag : _exprCppFlagTree.get(ctx)) {
      if (!flags.isEmpty()) flags += " && ";
      flags += flag;
    }
    return flags;
  }

  /** Apply the attributes' flag as if condition to the cpp code. */
  private String applyAttFlag(String cpp, RuleContext ctx) {
    if (!(ctx.getParent().getParent().getParent() instanceof PostAccumClauseContext)
        && (_exprCppFlagTree.get(ctx) == null)) {
      return cpp;
    }

    // att flag for assignments
    String flags = getAttFlags(ctx);

    if (!flags.isEmpty()) {
      cpp = "if (" + flags + ") {" + ("\n" + cpp).replace("\n", "\n  ") + "\n   }\n";
    }

    return cpp;
  }

  /**
   * Populate the post-accum flag (isSec or isTgt) from _exprCppFlagTree of a ctx, and remove the
   * flag from this ctx and all descendants.
   */
  private String popPostAccumFlag4CaseWhen(ParseTree tree) {
    Set<String> set = _exprCppFlagTree.get(tree);
    String flag = null;

    // since all flag inherit from descendants
    // so if it is null, no need to go further
    if (set == null) return null;

    if (set.contains(SRC_FLAG)) {
      flag = SRC_FLAG;
    } else if (set.contains(TGT_FLAG)) {
      flag = TGT_FLAG;
    } else {
      return null;
    }

    set.remove(flag);

    // this will avoid some redundant if condition
    // but not all, since some code is generated already.
    // to do this, we need extra step in ExprAndCondTranslator
    for (int i = 0; i < tree.getChildCount(); ++i) {
      popPostAccumFlag4CaseWhen(tree.getChild(i));
    }

    return flag;
  }

  /**
   * For current inactive vertex, generate the 'delta' code and 'write message' code
   */
  private void genDeltaCode(ParserRuleContext ctx, String origVarName) {
    if (_writing2other) {
      String varName = _localVarCpp.get(Util.GetAssignmentsContext(ctx)).get(origVarName);
      String deltaName = origVarName + "_other_delta";

      final String deltaType = CodeGenUtil.toCppName("Delta");
      String deltaCpp = String.format("   %s %s = %s ();\n", deltaType, deltaName, deltaType);
      deltaCpp += "   " + deltaName + "." + CodeGenUtil.toCppName("isOther") + " = true;\n";
      String writeCpp = "   context->Write (" + varName + ", " + deltaName + ");\n";

      _localVarDeltaCpp.get(Util.GetAssignmentsContext(ctx)).put(origVarName, deltaCpp);
      _localVarWriteCpp.get(Util.GetAssignmentsContext(ctx)).put(origVarName, writeCpp);
    }
  }

  private void collectEdgeTypesFromAtomicPattern(AtomicPatternContext atom) {
    TyCheckExprContext expr = getTyCheckExprFromAtomicPattern(atom);
    if (hasGenericTyCheck(List.of(expr))) {
      if (atom instanceof AtomicPatRightArrowContext) {
        _eTyCheck.addAll(_varType.allDirectedEdgeTypes());
      } else if (atom instanceof AtomicPatNoArrowContext) {
        _eTyCheck.addAll(_varType.allUndirectedEdgeTypes());
      }
    } else {
      _eTyCheck.add(expr.getText());
    }
  }

  private void setPostAccumFlag(PostAccumClauseContext ctx, String flag) {
    this.addFlag2Tree(ctx.assignments(), flag);
    ctx.assignments().assignment().forEach(a -> this.addFlag2Tree(a, flag));
  }

  private void handleSrcVertexType(GSQLParser.VTestContext ctx) {
    if (ctx.tyCheckList() == null) return;

    // remove parenthesis
    TyCheckListContext tyCheckList = ctx.tyCheckList();
    while (tyCheckList.tyCheckList() != null) {
      tyCheckList = tyCheckList.tyCheckList();
    }
    String tyCheck = tyCheckList.getText();
    
    if (!_varType.IsVSetVar(tyCheck) && !tyCheck.equals("_") && !tyCheck.equals("any")) {
      _srcTyCheck = "";
      for (int i = 0; i < getTyCheckExprList(ctx.tyCheckList()).size(); i++) {
        if (i > 0) _srcTyCheck += " || ";
        tyCheck = getTyCheckExprList(ctx.tyCheckList()).get(i).getText();
        _srcTyCheck += "context->GraphAPI()->GetVertexType(src) == "
                       + Util.GetVertexTypeVarName(tyCheck, commonVariable);
      }
    }
  }

  private void handleTgtVertexType(GSQLParser.VTestContext ctx) {
    // exclusive "any" and "_"
    if (hasGenericTyCheck(ctx.tyCheckList())) return;

    boolean runtimeTgtTypes = _tgtRuntimeTypes.get(ctx);
    String getTypeName = "context->GraphAPI()->GetVertexTypeName(tgt)";
    String getType = "context->GraphAPI()->GetVertexType(tgt)";
    List<GSQLParser.TyCheckExprContext> types = getTyCheckExprList(ctx.tyCheckList());

    // Runtime tgt vertex types
    if (runtimeTgtTypes) {
      _tgtTyCheck = new String();
      for (int i = 0; i < types.size(); ++i) {
        if (i > 0) {
          _tgtTyCheck += " || ";
        }

        DataType dt = _varType.getType(types.get(i));
        // tgt.type equals string
        if (dt.isString()) {
          _tgtTyCheck += getTypeName + " == " + getCpp(types.get(i));
        } else {
          // set accum contains
          _tgtTyCheck += "(" + getCpp(types.get(i)) + ").contains (" + getTypeName + ")";
        }
      }
    } else {
      // Compile time tgt vertex types
      StringJoiner joiner = new StringJoiner(" || ");
      for (TyCheckExprContext expr : types) {
        String cond = String.format("%s == %s", getType,
            Util.GetVertexTypeVarName(expr.getText(), commonVariable));
        joiner.add(cond);
      }
      _tgtTyCheck = joiner.toString();
    }
  }
}

/**
 * APPENDIX[1] Print Algorithm.
 *
 * <p>We support Print Exp (, Exp)* after a DML block Statement. The expression list can contain
 * global variable. Thus, we can only print afer it is available, which is in the before iteration.
 *
 * <p>So, the best place is to print in the next iteration's VertexMap.
 *
 * <p>Three scenarios.
 *
 * <p>Scenario 1 (Zixuan) ==================== Print is the last statement of the query. We need to
 * add an extra iteration to print in VertexMap.
 *
 * <p>Scenario 2 (Zixuan) =================== Print is "Sandwiched" by two DML blocks. - If the
 * bottom DML block is WHILE statement, we need to add an extra iteration with VertexMap to
 * print. - If the bottom DML block is EStep, we need to add an extra VertexMap print code. - If
 * the bottom DML block is VStep, we just prefix print code to the generated VertexMap code.
 *
 * <p>Scenario 3 (future work) ======================== For non-linear plan, we need to add an extra
 * iteration with VertexMap to print before we persist current active vertex set.
 *
 * <p>Scenario 4 (Zixuan) ==================== If a print statement contains just global variables,
 * we deal it in Before Iteration, so that it won't be print multiple times in VertexMap.
 *
 * <p>Note:
 *
 * <p>- If we have multiple print statements, each statement should be redirected to a distinct file
 * name. If no redirection is specified, we will create n strings to hold the n print statemnts
 * respectively. And Then, concatenate them according to the print statement PC counter order.
 *
 * <p>- print after DML block with Limit K clause must be dealt with in BeforeIteraion.(Mingxi)
 */
