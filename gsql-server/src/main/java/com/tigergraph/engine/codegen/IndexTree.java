package com.tigergraph.engine.codegen;

import com.tigergraph.engine.typechecker.ForeachItem;
import java.util.LinkedList;
import java.util.List;

class IndexTree {
  private int _index;
  private boolean _isWhile; // is while loop or not, otherwise foreach
  private ForeachItem _foreachVar;
  private List<IndexTree> _children;

  public IndexTree(int i) {
    _children = new LinkedList<IndexTree>();
    _index = i;
    _isWhile = true;
    _foreachVar = null;
  }

  public IndexTree(int i, boolean isWhile, ForeachItem foreachVar) {
    _children = new LinkedList<IndexTree>();
    _index = i;
    _isWhile = isWhile;
    _foreachVar = foreachVar;
  }

  public ForeachItem getForeachVar() {
    return _foreachVar;
  }

  public boolean isWhile() {
    return _isWhile;
  }

  public int getIndex() {
    return _index;
  }

  public void addChild(IndexTree c) {
    _children.add(c);
  }

  public IndexTree lookup(Integer idx) {
    if (idx.intValue() == _index) return this;
    for (int i = 0; i < _children.size(); i++) {
      IndexTree t = _children.get(i).lookup(idx);
      if (t != null) return t;
    }
    return null;
  }

  // preorder traversal
  public List<IndexTree> getDescendants() {
    List<IndexTree> desc = new LinkedList<IndexTree>();

    for (int i = 0; i < _children.size(); i++) {
      IndexTree child = _children.get(i);
      desc.add(child);
      desc.addAll(child.getDescendants());
    }

    return desc;
  }

  @Override
  public String toString() {
    String result = _index + "(";
    for (int i = 0; i < _children.size(); i++) {
      result += _children.get(i).toString() + ",";
    }

    //get rid off last ","
    if (_children.size() > 0) {
      result = result.substring(0, result.length() - 1);
    }
    result += ")";
    return result;
  }
}
