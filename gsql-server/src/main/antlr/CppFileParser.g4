/******************************************************************************
 * Copyright (c) 2023, TigerGraph Inc. All rights reserved Unauthorized copying of this file, via
 * any medium is strictly prohibited Proprietary and confidential
 * ****************************************************************************
 */
parser grammar CppFileParser;

options {
	tokenVocab = CppFileLexer;
}
/**
 * This start rule parse the cpp files with functions, and get function signature to do the type
 * check. This start rule applies to: gquery_libs.hpp (basic built-in functions) ExprFunctions.hpp
 * (UDF functions) tg_ExprFunctions.hpp (ML/Algo maintained UDF functions) Parse the macros and
 * statements, but only extract all identifiers from them.
 */
udf_impl:
  (nameSpaceDef | blockStmt)*;

/**
 * This is another start rule, to parse more general cpp files, that may contain more types in cpp,
 * eg: struct, class. This start rule applies to: ExprUtil.hpp tg_ExprUtil.hpp TokenBank.cpp
 */
general_cpp: (cppdirective | typedefdeclaration| id | bracket | nonid)*;

nameSpaceDef:
    NAMESPACE Identifier LBRACE blockStmt* RBRACE;

blockStmt:
    cppdirective
    | typedefdeclaration
    | function
		| otherStmt; // GLE-4738, allow other statements besides function and typedef.

cppdirective:
	DIR_INCLUDE	# DirectiveInclude
	| DIR_MACRO	# DirectiveMacro
	| DIR_OTHER	# DirectiveOther;

typedefdeclaration:
    TYPEDEF;

function:
	template? INLINE? type Identifier LPAREN params? RPAREN suffix;

suffix:
	SEMICOLON				# FunctionDeclSuffix
	| LBRACE functionStmt RBRACE	# FucntionImplSuffix;

// both float and double
constFpNum
  : DOT CONST_INT
  | CONST_INT DOT CONST_INT*
  ;

template: TEMPLATE LT ( typename (COMMA typename)*)? GT;

typename: ( TYPENAME | CLASS) Identifier;

type:
	namespace? Identifier (
		LT (type | CONST_INT) (COMMA (type | CONST_INT))* GT
	)?
	| INT
	| STRING
	| FLOAT
	| DOUBLE
	| BOOL
	| Identifier;

namespace: Identifier DCOLON (Identifier DCOLON)*;

params: param (COMMA param)*;

param: CONST? type (AMPERSAND | STAR)?? Identifier? (EQ constant)?;

otherStmt:
  (id | nonid | bracket)+? SEMICOLON;

id: Identifier;

constant
  : HYPHEN? CONST_INT
  | HYPHEN? constFpNum
  | TRUE
  | FALSE
  | STRING_LITERAL
  ;

nonid:
	~(
		Identifier
		| LPAREN
		| RPAREN
		| LBRACK
		| RBRACK
		| LBRACE
		| RBRACE
	);

bracket: LPAREN | RPAREN | LBRACK | RBRACK | LBRACE | RBRACE;

functionStmt: balancedtoken*?;

balancedtoken:
	cppdirective
	| typedefdeclaration
	| LPAREN functionStmt RPAREN
	| LBRACK functionStmt RBRACK
	| LBRACE functionStmt RBRACE
	| (id | nonid)+?;
