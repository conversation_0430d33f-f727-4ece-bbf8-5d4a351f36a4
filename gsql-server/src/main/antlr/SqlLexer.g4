lexer grammar Sql<PERSON><PERSON>er;

//////////////////////////////////////////////////////
// SQL reserved keyword is case insensitive
// User defined object identifiers are case sensitive
//////////////////////////////////////////////////////

// reserved keywords in lowercase
AND           : A N D { setText("and"); } ;
ANY           : A N Y { setText("any"); } ;
API           : A P I { setText("api"); } ;
AS            : A S { setText("as"); } ;
ASC           : A S C { setText("asc"); } ;
AVG           : A V G { setText("avg"); } ;
BAG           : B A G { setText("bag"); } ;
BETWEEN       : B E T W E E N { setText("between"); } ;
BY            : B Y { setText("by"); } ;
CASE          : C A S E { setText("case"); } ;
COALESCE      : C O A L E S C E { setText("coalesce"); } ;
CONTAINS      : C O N T A I N S { setText("contains"); } ;
COUNT         : C O U N T { setText("count"); } ;
DATETIME      : D A T E T I M E { setText("datetime"); } ;
DATETIME_ADD  : D A T E T I M E UNDERSCORE A D D { setText("datetime_add"); } ;
DATETIME_SUB  : D A T E T I M E UNDERSCORE S U B { setText("datetime_sub"); } ;
DESC          : D E S C { setText("desc"); } ;
DISTINCT      : D I S T I N C T { setText("distinct"); } ;
ELSE          : E L S E { setText("else"); } ;
END           : E N D { setText("end"); } ;
FALSE         : F A L S E { setText("false"); } ;
FILE          : F I L E { setText("file"); } ;
FLOAT         : F L O A T { setText("float"); } ;
FOR           : F O R { setText("for"); } ;
FROM          : F R O M { setText("from"); } ;
FULL          : F U L L { setText("full"); } ;
GRAPH         : G R A P H { setText("graph"); };
GROUP         : G R O U P { setText("group"); } ;
HAVING        : H A V I N G { setText("having"); } ;
IF            : I F { setText("if"); } ;
IN            : I N { setText("in"); } ;
INNER         : I N N E R { setText("inner"); } ;
INTO          : I N T O { setText("into"); } ;
IS            : I S { setText("is"); } ;
JOIN          : J O I N { setText("join"); } ;
LEFT          : L E F T { setText("left"); } ;
LIKE          : L I K E { setText("like"); } ;
LIMIT         : L I M I T { setText("limit"); } ;
MAX           : M A X { setText("max"); } ;
MIN           : M I N { setText("min"); } ;
NOT           : N O T { setText("not"); } ;
NULL          : N U L L { setText("null"); } ;
NULLIF        : N U L L I F { setText("nullif"); } ;
OFFSET        : O F F S E T { setText("offset"); } ;
ON            : O N { setText("on"); } ;
OR            : O R { setText("or"); } ;
ORDER         : O R D E R { setText("order"); } ;
OUTER         : O U T E R { setText("outer"); } ;
RIGHT         : R I G H T { setText("right"); } ;
SELECT        : S E L E C T { setText("select"); } ;
SUM           : S U M { setText("sum"); } ;
THEN          : T H E N { setText("then"); } ;
TO            : T O { setText("to"); } ;
TO_DATETIME   : T O UNDERSCORE D A T E T I M E { setText("to_datetime"); } ;
TRAILING      : T R A I L I N G { setText("trailing"); } ;
TRANSLATESQL  : T R A N S L A T E S Q L { setText("translatesql"); } ;
TRIM          : T R I M { setText("trim"); } ;
TRUE          : T R U E { setText("true"); } ;
UNION         : U N I O N { setText("union"); } ;
WHEN          : W H E N { setText("when"); } ;
WHERE         : W H E R E { setText("where"); } ;


// other literals
TYPE : 'type' ;

// special characters
QUOTE      : '"' ;
SQUOTE     : '\'' ;
BQUOTE     : '`' ;
DOLLAR     : '$' ;
PERCENTAGE : '%' ;
AMPERSAND  : '&' ;
LPAREN     : '(' ;
RPAREN     : ')' ;
LBRACK     : '[' ;
RBRACK     : ']' ;
LBRACE     : '{' ;
RBRACE     : '}' ;
PLUS       : '+' ;
MINUS      : '-' ;
DIV        : '/' ;
STAR       : '*' ;
BSLASH     : '\\' ;
DOT        : '.' ;
DOT_DOT    : '..' ;
DOT_STAR   : '.*' ;
COMMA      : ',' ;
COLON      : ':' ;
SEMICOLON  : ';' ;
EQ         : '=' ;
EQ_EQ      : '==' ;
NEQ        : '!=' ;
LT         : '<' ;
LTE        : '<=' ;
GT         : '>' ;
GTE        : '>=' ;
UNDERSCORE : '_' ;
PIPE       : '|' ;
LARROW     : '<-' ;
RARROW     : '->' ;
LSHIFT     : '<<' ;
AT         : '@' ;
QUESTION_MARK : '?' ;

// constants
CONST_INT : [0-9]+ ;
CONST_STR : (QUOTE (~["] | BSLASH (QUOTE | BSLASH))* QUOTE)
          | (SQUOTE (~['] | BSLASH (SQUOTE | BSLASH))* SQUOTE) ;

// identifiers
NAME      : ID ;
//BQUOTE_ID : BQUOTE (~[` | BSLASH BQUOTE])+ BQUOTE ;
GACCNAME  : '@@'ID ;
VACCNAME  : '@'ID ;
fragment ID : [a-zA-Z_][a-zA-Z0-9_]* ;

// whitespace:
WS : [ \t\r\n]+ -> channel (HIDDEN) ;

// comments:
SL_COMMENT : ('//' | '#') .*? '\n' -> channel (HIDDEN) ;
ML_COMMENT : '/*' .*? '*/' -> channel (HIDDEN) ;

// user defined escape character
// Example: 'David_' LIKE 'David|_' ESCAPE '|';
// In above condition, '|' is user defined escape character
ESCAPE_CHAR : SQUOTE . SQUOTE ;

// this matches error token to suppress unplesant error messages
ERROR_TOKEN : . ;

// Support case-insensitive keywords and allowing case-sensitive identifiers
fragment A : 'a' | 'A' ;
fragment B : 'b' | 'B' ;
fragment C : 'c' | 'C' ;
fragment D : 'd' | 'D' ;
fragment E : 'e' | 'E' ;
fragment F : 'f' | 'F' ;
fragment G : 'g' | 'G' ;
fragment H : 'h' | 'H' ;
fragment I : 'i' | 'I' ;
fragment J : 'j' | 'J' ;
fragment K : 'k' | 'K' ;
fragment L : 'l' | 'L' ;
fragment M : 'm' | 'M' ;
fragment N : 'n' | 'N' ;
fragment O : 'o' | 'O' ;
fragment P : 'p' | 'P' ;
fragment Q : 'q' | 'Q' ;
fragment R : 'r' | 'R' ;
fragment S : 's' | 'S' ;
fragment T : 't' | 'T' ;
fragment U : 'u' | 'U' ;
fragment V : 'v' | 'V' ;
fragment W : 'w' | 'W' ;
fragment X : 'x' | 'X' ;
fragment Y : 'y' | 'Y' ;
fragment Z : 'z' | 'Z' ;
