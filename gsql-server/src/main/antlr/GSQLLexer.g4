lexer grammar GSQLLexer;

//////////////////////////////////////////////////////
// GSQL reserved keyword is case insensitive
// User defined object identifiers are case sensitive
//////////////////////////////////////////////////////

// reserved keywords in lowercase
ABORT         : A B O R T ;
ACCUM         : A C C U M { setText("accum"); } ;
ALL           : A L L ;
AND           : A N D { setText("and"); } ;
ANY           : A N Y { setText("any"); } ;
API           : A P I { setText("api"); } ;
AS            : A S { setText("as"); } ;
ASC           : A S C { setText("asc"); } ;
AVG           : A V G { setText("avg"); } ;
BAG           : B A G { setText("bag"); } ;
BATCH         : B A T C H { setText("batch"); } ;
BETWEEN       : B E T W E E N { setText("between"); } ;
BITWISEANDACCUM: B I T W I S E A N D A C C U M { setText("BitwiseAndAccum"); };
BITWISEORACCUM: B I T W I S E O R A C C U M { setText("BitwiseOrAccum"); };
BOOL          : B O O L { setText("bool"); } ;
BOTH          : B O T H { setText("both"); } ;
BREAK         : B R E A K { setText("break"); } ;
BY            : B Y ;
CANDIDATE_SET : C A N D I D A T E UNDERSCORE S E T;
CASE          : C A S E { setText("case"); } ;
CATCH         : C A T C H { setText("catch"); } ;
COALESCE      : C O A L E S C E { setText("coalesce"); } ;
COLLECT       : C O L L E C T { setText("collect"); } ;
COLUMN        : C O L U M N ;
COMMIT        : C O M M I T ;
COMPRESS      : C O M P R E S S { setText("compress"); } ;
CONTINUE      : C O N T I N U E { setText("continue"); } ;
COUNT         : C O U N T { setText("count"); } ;
CREATE        : C R E A T E { setText("create"); } ;
DATETIME      : D A T E T I M E { setText("datetime"); } ;
DATETIME_ADD  : D A T E T I M E UNDERSCORE A D D { setText("datetime_add"); } ;
DATETIME_SUB  : D A T E T I M E UNDERSCORE S U B { setText("datetime_sub"); } ;
DEFAULT       : D E F A U L T ;
DELETE        : D E L E T E { setText("delete"); } ;
DESC          : D E S C { setText("desc"); } ;
DIRECTED      : D I R E C T E D ;
DISCRIMINATOR : D I S C R I M I N A T O R { setText("discriminator"); } ;
DISTANCE_MAP      : D I S T A N C E UNDERSCORE M A P;
DISTINCT      : D I S T I N C T ;
DISTRIBUTED   : D I S T R I B U T E D { setText("batch"); } ;
DO            : D O { setText("do"); } ;
DOUBLE        : D O U B L E { setText("double"); } ;
EDGE          : E D G E { setText("edge"); } ;
EF            : E F { setText("ef"); } ;
ELSE          : E L S E { setText("else"); } ;
END           : E N D { setText("end"); } ;
ESCAPE        : E S C A P E { setText("escape"); } ;
EXCEPTION     : E X C E P T I O N { setText("exception"); } ;
FALSE         : F A L S E { setText("false"); } ;
FILE          : F I L E ;
FILTER        : F I L T E R { setText("filter"); } ;
FILTERTYPE    : F I L T E R T Y P E;
FLOAT         : F L O A T { setText("float"); } ;
FOR           : F O R { setText("for"); } ;
FOREACH       : F O R E A C H { setText("foreach"); } ;
FROM          : F R O M { setText("from"); } ;
FUNCTION      : F U N C T I O N ;
GRAPH         : G R A P H { setText("graph"); } ;
GROUP         : G R O U P ;
HAVING        : H A V I N G { setText("having"); } ;
IF            : I F { setText("if"); } ;
IN            : I N { setText("in"); } ;
INIT          : I N I T { setText("init"); } ;
INSERT        : I N S E R T ;
INSTRUCTION   : I N S T R U C T I O N;
INT           : I N T { setText("int"); } ;
INTERPRET     : I N T E R P R E T { setText("interpret"); } ;
INTERSECT     : I N T E R S E C T { setText("intersect"); } ;
INTERVAL      : I N T E R V A L { setText("interval"); } ;
INTO          : I N T O { setText("into"); } ;
IS            : I S { setText("is"); } ;
ISEMPTY       : I S E M P T Y { setText("isempty"); } ;
JSONARRAY     : J S O N A R R A Y { setText("jsonarray"); } ;
JSONOBJECT    : J S O N O B J E C T { setText("jsonobject"); } ;
JOIN          : J O I N ;
LASTHOP       : L A S T H O P ;
LEADING       : L E A D I N G { setText("leading"); } ;
LEFT          : L E F T ;
LEGACY        : L E G A C Y;
LET           : L E T ;
LIKE          : L I K E { setText("like"); } ;
LIMIT         : L I M I T { setText("limit"); } ;
LIST          : L I S T ;
LOAD_ACCUM    : L O A D A C C U M { setText("loadAccum"); } ;
LOG           : L O G ;
MAP           : M A P ;
MATCH         : M A T C H ;
MAX           : M A X { setText("max"); } ;
MIN           : M I N { setText("min"); } ;
MINUS         : M I N U S { setText("minus"); } ;
NOT           : N O T { setText("not"); } ;
NOW           : N O W ;
NULL          : N U L L { setText("null"); } ;
OFFSET        : O F F S E T { setText("offset"); } ;
ON            : O N ;
OR            : O R { setText("or"); } ;
ORDER         : O R D E R { setText("order"); } ;
PATH          : P A T H ;
PER           : P E R ;
PINNED        : P I N N E D { setText("pinned"); } ;
POST_ACCUM    : P O S T HYPHEN A C C U M { setText("post-accum"); }
              | P O S T UNDERSCORE A C C U M { setText("post_accum"); } ;
PRIMARY_ID    : P R I M A R Y UNDERSCORE I D { setText("primary_id"); } ;
PROJECT       : P R O J E C T ;
PRINT         : P R I N T { setText("print"); } ;
QUERY         : Q U E R Y { setText("query"); } ;
RAISE         : R A I S E { setText("raise"); } ;
RANGE         : R A N G E { setText("range"); } ;
REPLACE       : R E P L A C E ;
RETURN        : R E T U R N { setText("return"); } ;
RETURNS       : R E T U R N S { setText("returns"); } ;
RUN           : R U N { setText("run"); } ;
SAMPLE        : S A M P L E { setText("sample"); } ;
SELECT        : S E L E C T { setText("select"); } ;
STDEV         : S T D E V { setText("stdev"); } ;
STDEVP        : S T D E V P { setText("stdevp"); } ;
SELECT_VERTEX : S E L E C T V E R T E X { setText("selectvertex"); } ;
SEMIJOIN      : S E M I J O I N ;
SET           : S E T { setText("set"); } ;
SRC           : S R C ;
STATIC        : S T A T I C { setText("static"); } ;
STRING        : S T R I N G { setText("string"); } ;
SUM           : S U M { setText("sum"); } ;
SYNTAX        : S Y N T A X { setText("syntax"); } ;
TARGET        : T A R G E T { setText("target"); } ;
TAGS          : T A G S { setText("tags"); } ;
TEMPLATE      : T E M P L A T E { setText("template"); } ;
TGT           : T G T ;
THEN          : T H E N { setText("then"); } ;
TO            : T O { setText("to"); } ;
TO_CSV        : T O UNDERSCORE C S V { setText("to_csv"); } ;
TO_DATETIME   : T O UNDERSCORE D A T E T I M E ;
TRAILING      : T R A I L I N G { setText("trailing"); } ;
TRIM          : T R I M { setText("trim"); } ;
TRUE          : T R U E { setText("true"); } ;
TRY           : T R Y { setText("try"); } ;
TUPLE         : T U P L E { setText("tuple"); } ;
TYPEDEF       : T Y P E D E F { setText("typedef"); } ;
UINT          : U I N T { setText("uint"); } ;
UNDIRECTED    : U N D I R E C T E D ;
UNION         : U N I O N { setText("union"); } ;
UNWIND        : U N W I N D { setText("unwind"); } ;
UPDATE        : U P D A T E ;
VALUES        : V A L U E S { setText("values"); } ;
VECTOR        : V E C T O R ;
VECTOR_SEARCH   : V E C T O R S E A R C H { setText("vectorSearch"); } ;
VERTEX        : V E R T E X { setText("vertex"); } ;
VIRTUAL       : V I R T U A L ;
WHEN          : W H E N { setText("when"); } ;
WHERE         : W H E R E { setText("where"); } ;
WHILE         : W H I L E { setText("while"); } ;
WITH          : W I T H { setText("with"); } ;

// reserved keywords in uppercase
GSQL_INT_MAX  : G S Q L UNDERSCORE I N T UNDERSCORE M A X { setText("GSQL_INT_MAX"); } ;
GSQL_INT_MIN  : G S Q L UNDERSCORE I N T UNDERSCORE M I N { setText("GSQL_INT_MIN"); } ;
GSQL_UINT_MAX : G S Q L UNDERSCORE U I N T UNDERSCORE M A X { setText("GSQL_UINT_MAX"); } ;
GSQL_DEBUG    : G S Q L UNDERSCORE D E B U G ;

// reserved keywords in lowercase
RESET_COLLECTION_ACCUM : R E S E T UNDERSCORE C O L L E C T I O N UNDERSCORE A C C U M
  { setText("reset_collection_accum"); } ;

// engineTemplate
TEMPLATE_DELTA         : '__TEMPLATE__DELTA' ;
TEMPLATE_DELTA_MESSAGE : '__TEMPLATE__DELTA_MESSAGE' ;
TEMPLATE_VERTEX_VAL    : '__TEMPLATE__VERTEX_VAL' ;
TEMPLATE_HEAP          : '__TEMPLATE__' NAME '_COMPARATOR' ;

// engineParam
ENGINE_E_ATTR     : '__ENGINE__E_ATTR' ;
ENGINE_SRC_ATTR   : '__ENGINE__SRC_ATTR' ;
ENGINE_TGT_ATTR   : '__ENGINE__TGT_ATTR' ;
ENGINE_V_ATTR     : '__ENGINE__V_ATTR' ;
ENGINE_SRC_VAL    : '__ENGINE__SRC_VAL' ;
ENGINE_TGT_VAL    : '__ENGINE__TGT_VAL' ;
ENGINE_V_VAL      : '__ENGINE__V_VAL' ;
ENGINE_MESSAGE    : '__ENGINE__MESSAGE' ;
ENGINE_CONTEXT    : '__ENGINE__CONTEXT' ;
ENGINE_REQUEST    : '__ENGINE__REQUEST' ;
ENGINE_SERVICEAPI : '__ENGINE__SERVICEAPI' ;

// special keywords for testing and debugging
DEBUG_FAIL_COMPILE : '__DEBUG__FAIL_COMPILE' ; // generate code that cannot compile

// other literals
TYPE : 'type' ;

// special characters
QUOTE      : '"' ;
SQUOTE     : '\'' ;
BQUOTE     : '`' ;
DOLLAR     : '$' ;
PERCENTAGE : '%' ;
AMPERSAND  : '&' ;
LPAREN     : '(' ;
RPAREN     : ')' ;
LBRACK     : '[' ;
RBRACK     : ']' ;
LBRACE     : '{' ;
RBRACE     : '}' ;
PLUS       : '+' ;
HYPHEN     : '-' ;
STAR       : '*' ;
SLASH      : '/' ;
BSLASH     : '\\' ;
DOT        : '.' ;
DOT_DOT    : '..' ;
DOT_STAR   : '.*' ;
COMMA      : ',' ;
COLON      : ':' ;
SEMICOLON  : ';' ;
EQ         : '=' ;
EQ_EQ      : '==' ;
NEQ        : '!=' ;
PLUS_EQ    : '+=' ;
LT         : '<' ;
LTE        : '<=' ;
LTGT       : '<>' ;
GT         : '>' ;
GTE        : '>=' ;
UNDERSCORE : '_' ;
PIPE       : '|' ;
LARROW     : '<-' ;
RARROW     : '->' ;
LSHIFT     : '<<' ;
TLARROW    : '<~' ;
TRARROW    : '~>' ;
TILDE      : '~' ;

// constants
CONST_INT : [0-9]+ ;
CONST_STR : QUOTE (~["] | BSLASH (QUOTE | BSLASH))* QUOTE ;

// identifiers
NAME      : ID ;
//BQUOTE_ID : BQUOTE (~[` | BSLASH BQUOTE])+ BQUOTE ;
GACCNAME  : '@@'ID ;
VACCNAME  : '@'ID ;
fragment ID : [a-zA-Z_][a-zA-Z0-9_]* ;

// whitespace:
WS : [ \t\r\n]+ -> channel (HIDDEN) ;

// comments:
SL_COMMENT : ('//' | '#') .*? '\n' -> channel (HIDDEN) ;
ML_COMMENT : '/*' .*? '*/' -> channel (HIDDEN) ;

// user defined escape character
// Example: 'David_' LIKE 'David|_' ESCAPE '|';
// In above condition, '|' is user defined escape character
ESCAPE_CHAR : SQUOTE . SQUOTE ;

// this matches error token to suppress unplesant error messages
ERROR_TOKEN : . ;

// Support case-insensitive keywords and allowing case-sensitive identifiers
fragment A : 'a' | 'A' ;
fragment B : 'b' | 'B' ;
fragment C : 'c' | 'C' ;
fragment D : 'd' | 'D' ;
fragment E : 'e' | 'E' ;
fragment F : 'f' | 'F' ;
fragment G : 'g' | 'G' ;
fragment H : 'h' | 'H' ;
fragment I : 'i' | 'I' ;
fragment J : 'j' | 'J' ;
fragment K : 'k' | 'K' ;
fragment L : 'l' | 'L' ;
fragment M : 'm' | 'M' ;
fragment N : 'n' | 'N' ;
fragment O : 'o' | 'O' ;
fragment P : 'p' | 'P' ;
fragment Q : 'q' | 'Q' ;
fragment R : 'r' | 'R' ;
fragment S : 's' | 'S' ;
fragment T : 't' | 'T' ;
fragment U : 'u' | 'U' ;
fragment V : 'v' | 'V' ;
fragment W : 'w' | 'W' ;
fragment X : 'x' | 'X' ;
fragment Y : 'y' | 'Y' ;
fragment Z : 'z' | 'Z' ;
