/******************************************************************************
 * Copyright (c) 2023, TigerGraph Inc. All rights reserved Unauthorized copying of this file, via
 * any medium is strictly prohibited Proprietary and confidential
 * ****************************************************************************
 */
lexer grammar CppFileLexer;

//////////////////
// lexical rules ////////////////

// literals
CLASS: 'class';
CONST: 'const';
INLINE: 'inline';
NAMESPACE: 'namespace';
TEMPLATE: 'template';
TYPENAME: 'typename';
CONST_INT: [0-9]+;
STRING_LITERAL:
	DOUBLE_QUOTE (~["] | ESCAPE_DOUBLE_QUOTE)*? DOUBLE_QUOTE;
TRUE: 'true';
FALSE: 'false';

// typename literals
INT: 'int';
STRING: 'string' | 'std::string';
FLOAT: 'float';
DOUBLE: 'double';
BOOL: 'bool';

// special characters
fragment SINGLE_QUOTE: '\'';
fragment DOUBLE_QUOTE: '"';
fragment ESCAPE_SINGLE_QUOTE: '\\\'';
fragment ESCAPE_DOUBLE_QUOTE: '\\"';
LPAREN: '(';
RPAREN: ')';
LBRACK: '[';
RBRACK: ']';
LBRACE: '{';
RBRACE: '}';
AMPERSAND: '&';
STAR: '*';
COMMA: ',';
DCOLON: '::';
SEMICOLON: ';';
EQ: '=';
LT: '<';
GT: '>';
HYPHEN: '-';
DOT: '.';

// identifier
Identifier: IdentifierNonDigit (IdentifierNonDigit | DIGIT)*;

// Notice some universal character names are also allowed in an identifier, see:
// https://learn.microsoft.com/en-us/cpp/cpp/identifiers-cpp
fragment IdentifierNonDigit: NONDIGIT | UniversalCharacterName;

fragment DIGIT: [0-9];

fragment NONDIGIT: [a-zA-Z_];

fragment UniversalCharacterName:
	'\\u' HexQuad
	| '\\U' HexQuad HexQuad;

fragment HexQuad:
	HEXADECIMALDIGIT HEXADECIMALDIGIT HEXADECIMALDIGIT HEXADECIMALDIGIT;

fragment HEXADECIMALDIGIT: [0-9a-fA-F];

// whitespace:
WS: [ \t\r\n]+ -> skip;

// typedef
TYPEDEF: 'typedef' ~[;\r\n]* SEMICOLON;

// comments:
SL_COMMENT: '//' .*? '\n' -> skip;
ML_COMMENT: '/*' .*? '*/' -> skip;

// cpp directives https://cplusplus.com/doc/tutorial/preprocessor/
DIR_INCLUDE: '#include' WS ( LT (~[>])+? GT | STRING_LITERAL);

DIR_MACRO: '#define' (~[\n]*? '\\' '\r'? '\n')* ~[\n]+;

DIR_OTHER: '#' DIR_NAME (~[\n]*? '\\' '\r'? '\n')* ~[\n]*;

DIR_NAME:
	'undef'
	| 'ifdef'
	| 'ifndef'
	| 'if'
	| 'else'
	| 'elif'
	| 'endif'
	| 'line'
	| 'error'
	| 'pragma';

// keep this at last, at lower precendence need this to cover all other special characters that not
// declared above eg: escaped characters, symbols like '^', ...
NonIdentifier: .;
