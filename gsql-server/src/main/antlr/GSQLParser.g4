parser grammar GSQLParser;

options { tokenVocab = GSQLLexer; }

job
  : (CREATE | INTERPRET) (OR REPLACE)? (TEMPLATE | (BATCH | DISTRIBUTED))? QUERY 
    (packageName (DOT packageName)* DOT)? queryName?
    LPAREN params? RPAREN (FOR GRAPH graphName)? returnClause? apiClause?
    syntaxClause? modeClause?
    // GLE-2092: merge varDeclaration and exceptionDeclaration to statement
    LBRACE typeDefines markerForVSetGenAccs statements RBRACE
  | CREATE (OR REPLACE)? FUNCTION gsqlFunc LPAREN params? RPAREN
    returnClause LBRACE statements RBRACE 
  | // for stand-alone, no-signature query block. Will be normalized away and turned into
    // explicit INTERPRET query command
    (LET typeDefines varDeclaration* IN)? implicitOutputBlock EOF
  ;

gsqlFunc : pkgs DOT gsqlFuncName ;

pkgs : pkg (DOT pkg)* ;

pkg : ident ;

gsqlFuncName : ident ;


queryName : ident ;
packageName : ident ;
graphName : ident ;

///////////////////////////////////////////////////////////
// dummy non-terminal uses as marker for where to place
// declarations of accums for inVSet tests. Used by Printer.
////////////////////////////////////////////////////////////
markerForVSetGenAccs : ;

returnClause : RETURNS LPAREN returnType (COMMA returnType)* RPAREN ;

apiClause : API LPAREN CONST_STR RPAREN ;

syntaxClause : SYNTAX ident ;

modeClause : IN modeKeyword (COMMA modeKeyword)* ;

modeKeyword 
  : GSQL_DEBUG 
  | INSTRUCTION
  | LEGACY 
  ;

returnType
  // allow ad-hoc schema tuple
  : anonymousTupleType #ReturnAnonymousTupleType
  | accType #ReturnAccType
  | parType #ReturnParType
  ;

anonymousTupleType : TUPLE LT baseType (COMMA baseType)* GT;

params : param (COMMA param)* ;

param : parType ident (EQ constant)? ;

parType
  : baseType
  | SET LT baseType GT
  | BAG LT baseType GT
  | LIST LT baseType GT
  | MAP LT baseType COMMA baseType GT
  | FILE
  | FILTERTYPE // only used in template filter
  ;

////////////////
// type define
///////////////
typeDefines : (typeDef SEMICOLON | typeDefAccum SEMICOLON)* ;

typeDef : TYPEDEF TUPLE LT tupleType GT ident ;

typeDefAccum : TYPEDEF accType ident ;

///////////////
// exception define
///////////////
// error code must be in application error range (40001-),
// gpe build-in error range (1-20000, with name)
// gquery build-in error range (20001-40000 with name)
// GLE-2092: singleton exceptionDeclaration
exceptionVarDecl : EXCEPTION exceptionVarName LPAREN errorCode RPAREN SEMICOLON ;

errorCode : CONST_INT ;

////////////////
// accumulators
///////////////
// GLE-2092: singleton varDelcaration
varDeclaration : accDecl | globalVarDecl | fileVarDecl ;

globalVarDecl
  : baseType globalVarName (COMMA globalVarName)* SEMICOLON #PrimitiveTypeDecl
  | tupleTypeName tupleVarName (COMMA tupleVarName)* SEMICOLON #TupleTypeDecl
  ;

fileVarDecl : FILE fileVar (COMMA fileVar)* SEMICOLON ;

fileVar : ident LPAREN funcParams RPAREN ;

accDecl : STATIC? accType EDGE? accnames SEMICOLON ;

dimension : LBRACK CONST_INT? RBRACK ;

accType
  : bitwiseAccumType (LT accumSizeParam GT)? #BitwiseAccType
  | ident typeParams? #GeneralAccType
  | ident LT tupleTypeName GT LPAREN (heapSize COMMA)? sortKey (COMMA sortKey)* RPAREN #TopkHeapType
  ;

// BitwiseAccum type and size
bitwiseAccumType : BITWISEORACCUM | BITWISEANDACCUM;

accumSizeParam
  : CONST_INT
  | rhsIdent
  ;

heapSize
  : CONST_INT
  | rhsIdent
  ;

sortKey : fieldName direction? ;

direction
  : ASC
  | DESC
  ;

engineTemplate
  : TEMPLATE_DELTA #engineTemplateDelta
  | TEMPLATE_DELTA_MESSAGE #engineTemplateDeltaMsg
  | TEMPLATE_VERTEX_VAL #engineTemplateVerVal
  | TEMPLATE_HEAP #engineTemplateComp
  ;

tupleTypeName : ident ;

typeParams : LT typeParam (COMMA typeParam)* GT ;

// stringCompress is GroupByAccum field aliases (only GroupByAccum for now, maybe extended later)
typeParam : (baseType | engineTemplate | tupleTypeName | accType | stringCompress | anonymousTupleType) (ident)? ;

stringCompress : STRING COMPRESS ;

tupleType : fieldType (COMMA fieldType)* ;

fieldType
  : baseType fieldName
  | fieldName baseType
  ;

baseType
  : primitiveType
  | VERTEX (LT vParType GT)?
  | EDGE (LT vParType GT)?
  | JSONOBJECT
  | JSONARRAY
  ;

// restricted by GPE, map type attribute, key can be int/string ONLY
mapKeyType
  : INT
  | DATETIME
  | STRING
  ;

// restricted by GPE, map/list/set type attribute, value can be INT/DOUBLE/STRING/DATETIME/UDT only
// skip UDT for virtual edge for now
mapListSetValueType
  : INT
  | DOUBLE
  | STRING
  | DATETIME
  ;

primitiveType
  : INT
  | UINT
  | FLOAT
  | DOUBLE
  | STRING
  | BOOL
  | DATETIME
  ;

fieldName : ident ;

vParType : ident ;

//////////////
// statements
/////////////

statements : statement+ ;

statement
  : lhsIdent vSetTyDef? assignOp expr SEMICOLON #StatementLVarAssignExpr
  | lVSetVar vSetTyDef? EQ seedSet SEMICOLON #StatementSeed
  | PRINT printExpr (COMMA printExpr)* (WHERE condition)? (TO_CSV rhsIdent)? (WITH VECTOR)? SEMICOLON #StatementPrint
  | (lVSetVar vSetTyDef? EQ)? block SEMICOLON #StatementBlock
  | updateBlock SEMICOLON #StatementUpdate
  | deleteBlock SEMICOLON #StatementDelete
  | insertOp SEMICOLON #StatementInsert
  | CREATE (DIRECTED | UNDIRECTED) VIRTUAL EDGE ident LPAREN virtualEdgeParams RPAREN SEMICOLON #StatementCreateVirtualEdge
  | RESET_COLLECTION_ACCUM LPAREN (gAccRval | VACCNAME) RPAREN SEMICOLON #StatementResetAccum
  | DELETE rhsIdent SEMICOLON #StatementDeleteVar
  | WHILE condition (LIMIT maxIter)? DO statements END SEMICOLON #StatementLoop
  | CASE exprAtom WHEN constant THEN statements caseConstBlock* elseBlock? END SEMICOLON #StatementCaseConst
  | CASE WHEN condition THEN statements caseCondBlock* elseBlock? END SEMICOLON #StatementCaseCond
  | IF condition THEN statements elseIfBlock* elseBlock? END SEMICOLON #StatementIf
  | attrLval EQ expr SEMICOLON #StatementAssignAttr
  | gAccLval EQ expr SEMICOLON #StatementAssignGlobalAccum
  | gAccLval PLUS_EQ expr SEMICOLON #StatementReduceGlobalAccum
  | CONTINUE SEMICOLON #StatementContinue
  | BREAK SEMICOLON #StatementBreak
  | COMMIT SEMICOLON #StatementCommit
  | ABORT SEMICOLON #StatementAbort
  | LOG LPAREN expressions RPAREN SEMICOLON #StatementLog
  | funcRval SEMICOLON #StatementFuncRval
  | gAccLval assignOp LBRACE loadAccmFromFile (COMMA loadAccmFromFile)* RBRACE SEMICOLON #StatementLoadAccum
  // StatementAssignVAcc and StatementSetVAcc are syntactically right but semantically illegal.
  | vAccLval PLUS_EQ expr SEMICOLON #StatementAssignVAcc
  | vAccLval EQ expr SEMICOLON #StatementSetVAcc
  | FOREACH foreachVars DO statements END SEMICOLON #StatementForeach
  | RETURN (expr)? SEMICOLON #StatementReturn
  // raise statement by default abort the query, and return the error info to user.
  // It uses the following fields in the response json object:
  // error_code (int), error (bool), and message (string)
  // need to log the raise statement in gpe log with HIGH_LEVEL
  // GLE-2092: update Except to Exception
  | RAISE exceptionVarName errorMsg? SEMICOLON #StatementRaise
  // try-exception statement is handling user-and-system defined exception, the query will continue.
  // need to log the exception in gpe log with HIGH_LEVEL
  // GLE-2092: update Except to Exception
  | TRY statements EXCEPTION caseExceptionBlock+ elseExceptionBlock? END SEMICOLON #StatementException
  | expr SEMICOLON #StatementExprFunc
  | DEBUG_FAIL_COMPILE SEMICOLON #StatementDebugFailCompile
  // GLE-2092: merge varDeclaration and exceptionDeclaration to statement
  | varDeclaration #variableDeclaration
  | exceptionVarDecl #exceptionDeclaration
  | lVSetVar EQ topKVecSearchFuncRval SEMICOLON #StatementTopKVecSearchRval
  | initTable SEMICOLON #StatementInitTable
  | joinBlock SEMICOLON #StatementJoin
  | projectVSet SEMICOLON #StatementProjectVSet
  | projectTable SEMICOLON #StatementProjectTable
  | filterTable SEMICOLON #StatementFilterTable
  | orderTable SEMICOLON #StatementOrderTable
  | unwindTable SEMICOLON #StatementUnwindTable
  | unwindInit SEMICOLON #StatementUnwindInit
  | unionTable SEMICOLON #StatementUnionTable
  ;

initTable : INIT tableName expr AS columnName (COMMA expr AS columnName)*;

joinBlock
  : joinType tableName joinTableAlias WITH tableName
    (joinTableAlias | LPAREN joinTableAlias preWhereClause? RPAREN)
    (ON joinCondition)?
    whereClause?
    PROJECT DISTINCT? expr AS columnName (COMMA expr AS columnName)*
    INTO tableName
  ;

joinCondition
  : joinTableAlias DOT columnName EQ_EQ joinTableAlias DOT columnName #JoinCondColumnEq
  | LPAREN joinCondition RPAREN #JoinCondParen
  | joinCondition AND joinCondition #JoinCondAnd
  | joinCondition OR joinCondition #JoinCondOr
  ;

joinType
  : JOIN #InnerJoin
  | SEMIJOIN #SemiJoin
  | LEFT JOIN #LeftJoin
  ;

preWhereClause : WHERE condition ;

projectVSet
  : PROJECT tableName ON VERTEX
    COLUMN columnName INTO lVSetVar
    (COMMA columnName INTO lVSetVar)*
  ;

projectTable
  : PROJECT DISTINCT? tableName
    ON expr AS columnName (COMMA expr AS columnName)*
    INTO tableName
  ;

filterTable : FILTER tableName ON condition ;

orderTable : ORDER tableName BY orderKey (COMMA orderKey)* limitClause? ;

unwindTable : UNWIND tableName ON expr AS columnName INTO tableName ;

unwindInit : UNWIND expr AS columnName INTO tableName ;

unionTable : UNION ALL? tableName WITH tableName INTO tableName ;

printExpr : expr (LBRACK vSetProj (COMMA vSetProj)* RBRACK)? (WITH TAGS)? (AS ident)? ;

vSetProj : expr (AS ident)? ;

// insertOp will be used both used in statement and block level
// optional EDGE? is used only when ident is a variable/parameter name.
// if ident is a valid vertex/edge type name, it will be ignored
insertOp : INSERT INTO EDGE? ident (insertKeys)? VALUES insertValues ;

assignOp
  : EQ
  | PLUS_EQ
  ;

insertKeys
  : LPAREN (PRIMARY_ID | ident) (COMMA ident)* RPAREN #InsertVertexKeys
  | LPAREN FROM COMMA TO (COMMA ident)* RPAREN #InsertEdgeKeys
  | LPAREN FROM COMMA TO COMMA discriminatorAttribute (COMMA ident)* RPAREN #InsertMultiEdgeKeys
  ;  

discriminatorAttribute : DISCRIMINATOR LPAREN ident (COMMA ident)* RPAREN ;

insertValues
  : LPAREN (
      // This handles insert vertex with only PRIMARY_ID.
      insertValue
      // This handles insert vertex with at least one attribute other than PRIMARY_ID,
      // and all cases insert edge. There will be semantic check that if it is inserting vertex,
      // shouldn't provide fromVTHint or toVTHint.
      | insertValue fromVTHint? COMMA insertValue toVTHint? (COMMA insertValue)*
    ) RPAREN ;

insertValue
  : UNDERSCORE
  | expr
  ;

fromVTHint : ident ;

toVTHint : ident ;

virtualEdgeParams : FROM (fromVertexList | STAR) COMMA TO (toVertexList | STAR) (COMMA attribute)* ;

fromVertexList : ident (PIPE ident)* ;

toVertexList : ident (PIPE ident)* ;

attribute : ident attType (DEFAULT constant)? ;

attType
  : primitiveType
  | LIST LT mapListSetValueType GT
  | SET LT mapListSetValueType GT
  | MAP LT mapKeyType COMMA mapListSetValueType GT
  ;

foreachVars : (ident | LPAREN ident (COMMA ident)+ RPAREN) (IN | COLON) exprAtom ;

caseConstBlock : WHEN constant THEN statements ;

caseCondBlock : WHEN condition THEN statements ;

// GLE-2092: update Except to Exception
caseExceptionBlock : WHEN exceptionVarName THEN statements ;

elseIfBlock : ELSE IF condition THEN statements ;

elseBlock : ELSE statements ;

// GLE-2092: update Except to Exception
elseExceptionBlock : ELSE statements ;

maxIter
  : CONST_INT
  | rhsIdent
  ;

lVSetVar : ident ;

lhsIdent : ident ;

rhsIdent : ident ;

// GLE-2092: update Except to Exception
exceptionVarName : ident ;

errorMsg : LPAREN expr RPAREN ;

// seed statements
// when seed is a set parameter, semantic check
seedSet : LBRACE seedList? RBRACE ;

seedList : seed (COMMA seed)* ;

seed
  : rSeedAny #SeedAny
  | rhsIdent #SeedParam
  | rSeedVType #SeedVType
  | selectVertexFromFile #SeedFromFile
  | gAccRval #SeedGaccRval
  ;

loadAccmFromFile
  : LOAD_ACCUM LPAREN
    fileName COMMA vertexIdColumn (COMMA vertexIdColumn)* COMMA separator COMMA usingHeader
    RPAREN filterCond? ;

// select vertex by parsing a file.
// fileName is the path of the file. It can be either a string literal or from string type parameter.
// vertexIdColumn specify which column is the veretx external id.
// vertexTypeColumn specify which column is the vertex type (it can also be fixed type)
// separator specify the delimiter of spliting the file line
// header specify whether use header or not
selectVertexFromFile
  : SELECT_VERTEX LPAREN
    fileName COMMA vertexIdColumn COMMA vertexTypeColumn COMMA separator COMMA usingHeader
    RPAREN filterCond? ;

// VertexIdColumnPosition has format like $0, $1. Specify vertex id column by position.
// VertexIdColumnName has format like $"user", $"product". Specify vertex id column by name.
vertexIdColumn
  : DOLLAR CONST_INT #VertexIdColumnPosition
  | DOLLAR CONST_STR #VertexIdColumnName
  ;

// VertexTypeColumnPosition has format like $0, $1. Specify vertex type column by position.
// VertexTypeColumnName has format like $"types". Specify vertex type column by name.
// VertexTypeColumnIdentifier has format like members, skill. Specify vertex type by type name.
vertexTypeColumn
  : DOLLAR CONST_INT #VertexTypeColumnPosition
  | DOLLAR CONST_STR #VertexTypeColumnName
  | ident #VertexTypeColumnIdentifier
  ;

// Separator can be single character. Escape character like \t, \32 is also supported.
separator
  : CONST_STR
  | exprAtom
  ;

usingHeader
  : TRUE
  | FALSE
  | exprAtom
  ;

fileName : exprAtom ;

// DML block
block
  : (selectClause fromClause | templateFilterClause) sampleClause? whereClause? setClause? perClause? accumClause? postAccumClause* groupByClause? havingClause?
    orderClause? limitClause? ;

// implicit-output block - will be interpreted, with output vset/table name generated
implicitOutputBlock
  : implicitOutputSelectClause fromClause sampleClause? whereClause? setClause? perClause? accumClause? postAccumClause* groupByClause?  havingClause?
    orderClause? limitClause? SEMICOLON? ;

// update vertex/edge block
updateBlock : updateClause fromClause setClause whereClause? ;

deleteBlock : deleteClause fromClause whereClause? ;

// SET clause for vertex / edge update.
// Assignment can only be assignAttr or case when clause
// or assignDeclLocal or assignLocal or logExpr
setClause : SET assignments ;

// SELECT
selectClause
  : SELECT var #SelectVar
  | SELECT DISTINCT? columnExpr (COMMA columnExpr)* INTO tableName #SelectTable
  | SELECT DISTINCT? STAR INTO tableName #SelectStar
  ;

// will be interpreted, with output vset/table name generated
implicitOutputSelectClause
  : SELECT var
  | SELECT DISTINCT? columnExpr (COMMA columnExpr)*
  | SELECT DISTINCT? STAR
  ;

var : ident ;

columnExpr : expr (AS columnName)? ;

columnName : ident ;

exprCaseConst : CASE expr whenConstExpr+ ELSE expr END ;

whenConstExpr : WHEN constant THEN expr ;

exprCaseCond : CASE whenCondExpr+ ELSE expr END ;

whenCondExpr  : WHEN condition THEN expr ;

tableName : ident ;

joinTableAlias : ident ;

// FROM
fromClause
  : FROM step
  | FROM conjunctivePattern
  ;

updateClause : UPDATE var ;

deleteClause : DELETE var ;

step
  : vTest HYPHEN LPAREN eTest RPAREN RARROW vTest #EStep
  | vTest #VStep
  | vTest stepPattern+ #PathPattern
  | tableName AS? LPAREN tableAlias (COMMA tableAlias)* RPAREN #TablePattern
  ;

eTest : tyCheckList? alias? ;

vTest : tyCheckList? alias?
      // next production is for cypher/gql syntax support (normalized away in preprocessing)
      | LPAREN var? ((COLON | IS) labelExpression)? propertiesCond? (WHERE condition)? RPAREN
      ;

// for tyCheckList, the expression cannot refer to a vertex set variable
// directly. It can only refer to parameter, foreach alias or global variable,
// and the data type must be string or set<string>.
//
// Either all typecheck expressions specify an edge or vertex type that is determined
// at compile time, or all typecheck expressions are expressions that specify types
// as strings cannot mix the two kinds of type specifications together in one typecheck.
 tyCheckList
   : tyCheckExpr (PIPE tyCheckExpr)*
   | LPAREN tyCheckList RPAREN
   ;

stepPattern
  : HYPHEN LPAREN pattern? alias? RPAREN HYPHEN vTest
  // The following productions are for cypher/GQL syntax support.
  // (normalized away in preprocessing, so it does not
  //  affect our downstream listeners)
  | (LARROW |  HYPHEN | TILDE | TLARROW) LBRACK var? LPAREN? ((COLON | IS) labelExpression)? RPAREN?
        (STAR starBounds)? propertiesCond? (WHERE condition)? RBRACK (HYPHEN | RARROW | TILDE | TRARROW) gqlGraphPatternQuantifier? vTest
  | (LARROW |  HYPHEN | TILDE | TLARROW) (HYPHEN | RARROW | TILDE | TRARROW) gqlGraphPatternQuantifier? vTest
  ;

gqlGraphPatternQuantifier
  : STAR
  | PLUS
  | LBRACE CONST_INT RBRACE
  | LBRACE lBound? COMMA uBound? RBRACE
  ;

lBound : CONST_INT ;

uBound : CONST_INT ;

atomicPattern
  : tyCheckExpr #AtomicPatNoArrow
  | tyCheckExpr (RARROW | GT) #AtomicPatRightArrow
  | (LARROW | LT) tyCheckExpr #AtomicPatLeftArrow
  ;

disjPattern : atomicPattern (PIPE atomicPattern)* ;

pattern
  : atomicPattern #PatternAtom
  | LPAREN pattern RPAREN #PatternParen
  | starPattern #PatternStar
  | pattern DOT pattern #PatternConcat
  | disjPattern #PatternDisj
  ;

starPattern : (atomicPattern? | LPAREN disjPattern? RPAREN) STAR starBounds? ;

starBounds
  : CONST_INT DOT_DOT CONST_INT #StarBoundsLU
  | CONST_INT DOT_DOT #StarBoundsL
  | DOT_DOT CONST_INT #StarBoundsU
  | CONST_INT #StarBoundsExact
  ;

alias : COLON var ;

// for cypher/gql-style pattern syntax support:
propertiesCond : LBRACE propCond (COMMA propCond)* RBRACE ;
propCond : ident COLON expr ;

// could be extended to support negation, conjunction, GQL style wild card (%), etc.
labelExpression
  : labelTerm #LabelTermExpr
  | labelDisjunction #LabelDisjExpr
  ;

labelDisjunction
  : labelTerm ((PIPE COLON? | COLON) labelTerm)+
  ;

labelTerm
  : tyCheckExpr #LabelTyCheck
  | LPAREN labelExpression RPAREN #LabelParen
  ;

// the steps are meant to be only VStep and PathPattern steps
conjunctivePattern : step (COMMA step)+ ;

tableAlias : var ;

vSetTyDef : vSetType ;

vSetType : LPAREN vSetBaseType RPAREN ;

vSetBaseType
  : UNDERSCORE
  | ANY
  | ident (PIPE ident)*
  ;

percentage : PERCENTAGE ;

// SAMPLE : it's filter only supports src.outdegree () > integer
sampleClause : SAMPLE (exprAtom | exprAtom percentage) (EDGE | TARGET PINNED?) WHEN condition ;

// WHERE
whereClause : WHERE condition ;

expr
  : exprAtom #NonLogicalExpr
  | condition #LogicalExpr
  ;

condition
  : exprAtom comparisonOp exprAtom #CondCompare
  | exprAtom NOT? LIKE exprAtom (ESCAPE ESCAPE_CHAR)? #CondLike
  | LPAREN condition RPAREN #CondParen
  | NOT condition #CondNot
  | condition AND condition #CondAnd
  | condition OR condition #CondOr
  | exprAtom #CondExpr
  | exprAtom (IN | NOT IN) exprAtom #CondInOrNot
  | exprAtom IS NOT? NULL #CondIsNull
  | exprAtom BETWEEN exprAtom AND exprAtom #CondBetween
  ;

comparisonOp
  : LT
  | LTE
  | GT
  | GTE
  | EQ_EQ
  | EQ     // will be normalized away into ==
  | NEQ
  | LTGT   // will be normalized away into !=
  ;

// arithmetic expressions
exprAtom
  : exprAtom DOT accFuncRval filterCond? #AllFuncAccess
  | exprAtom DOT ident #AllTupleFieldAccess
  | typeRval #ExprTypeRval
  | vAccRval #ExprVAccRval
  | vAccPrevRval #ExprVAccPrevRval
  | funcRval #ExprFuncRval
  | dtOpFuncRval #ExprDatetimeOpFuncRval
  | trimFuncRval #ExprTrimFuncRval
  | gAccRval #ExprGAccRval
  | rhsIdent #ExprRhsIdent // this could be parameter/alias/local_var
  | constant #ExprConstant
  | kvRval #ExprKVRval // this could be kv pair/groupby kv pair/inlist
  | LPAREN exprAtom RPAREN #ExprParen
  | HYPHEN exprAtom #ExprSign
  | exprAtom (STAR | SLASH | PERCENTAGE) exprAtom #ExprMultDivMod
  | exprAtom (PLUS | HYPHEN) exprAtom #ExprAddDiff
  | exprAtom (LSHIFT | GT GT) exprAtom #ExprShift
  | exprAtom AMPERSAND exprAtom #ExprBitAnd
  | exprAtom PIPE exprAtom #ExprBitOr
  | TILDE exprAtom #ExprBitFlip
  | setScalarFunc LPAREN DISTINCT? exprAtom RPAREN #SetScalarFunction
  | COUNT LPAREN STAR RPAREN #ExprCountStar
  | COALESCE LPAREN funcParams RPAREN #ExprCoalesce
  | listEnum #ExprListEnum
  | engineParam #ExprEngineParam
  | exprAtom setOperator exprAtom #ExprSetOp
  | LPAREN exprAtom (COMMA exprAtom)+ RPAREN #SetInlist
  | rangeInterval #SetRangeInterval
  | rSeedAny #ExprSeedAny
  | rSeedVType #ExprSeedVType
  | selectVertexFromFile #ExprSeedFromFile
  | exprCaseCond #CaseCondExpr
  | exprCaseConst #CaseConstExpr
  | NULL #ExprNull
  ;

rSeedAny
  : UNDERSCORE
  | ANY
  ;

rSeedVType : seedIdent DOT_STAR ;

seedIdent : ident ;

exprDimension : LBRACK exprAtom RBRACK ;

setOperator
  : INTERSECT
  | UNION
  | MINUS
  ;

// from clause typecheck expression
tyCheckExpr
  : ident #tyCheckIdent
  | gAccRval #tyCheckAcc
  | UNDERSCORE #tyCheckUnderscore
  | ANY #tyCheckAny
  ;

listEnum : LBRACK exprAtom (COMMA exprAtom)* RBRACK ;

// this is key val pair for MapAccum or exprAtom list for GroupByAccum
// keys and values are called alternative labels. They are used to distinguish "expr" as a key or a value. 
kvRval : LPAREN keys+=expr (COMMA keys+=expr)* RARROW values+=expr (COMMA values+=expr)* RPAREN ;

setScalarFunc
// TODO: GLE-6975: some SetScalarFunctions like stdev will be added to non-reserved keywords
  : COUNT #SetScalarCount
  | COLLECT #SetScalarCollect
  | ISEMPTY #SetScalarIsEmpty
  | MAX #SetScalarMax
  | MIN #SetScalarMin
  | AVG #SetScalarAvg
  | STDEV #SetScalarStdev
  | STDEVP #SetScalarStdevP
  | SUM #SetScalarSum
  ;

typeRval : var DOT TYPE ;

vAccRval : var DOT VACCNAME exprDimension* ;

vAccPrevRval : var DOT VACCNAME SQUOTE ;

// vFunctRval    : var DOT ident LPAREN funcParams RPAREN filterCond? ;

filterCond : DOT FILTER LPAREN condition RPAREN ;

accFuncRval : funcName LPAREN funcParams RPAREN ;

// used for tuple constructor, or math funct
funcRval : funcName typeParams? LPAREN funcParams RPAREN ;

trimFuncRval : TRIM LPAREN ( BOTH | LEADING | TRAILING )? ( exprAtom FROM )? exprAtom RPAREN ;

dtOpFuncRval : dtOpFuncName LPAREN exprAtom COMMA INTERVAL exprAtom unit RPAREN ;

dtOpFuncName
  : DATETIME_ADD
  | DATETIME_SUB
  ;

topkFilterSetVar : ident;
embAttrIdent : ident;
vtypeIdent : ident;
embAttrItem : vtypeIdent DOT embAttrIdent;
topkEmbeddingAttrMetaList : embAttrItem (COMMA embAttrItem)*;
embeddingParam : exprAtom;
kParam : exprAtom;
efParam : exprAtom;
disParam : exprAtom;

topkParam
  : CANDIDATE_SET COLON topkFilterSetVar
  // when ef is not provided, on gsql side we pass (uint32_t)-1 as value, and internally engine will use K*2 if given -1
  | EF COLON efParam
  | DISTANCE_MAP COLON disParam;
topkParams : topkParam (COMMA topkParam)* ;

topKVecSearchFuncRval : VECTOR_SEARCH LPAREN LBRACE topkEmbeddingAttrMetaList RBRACE
                              COMMA embeddingParam COMMA kParam (COMMA LBRACE topkParams RBRACE)? RPAREN ;

unit : ident ;

funcParams : ( expr (COMMA expr)* )? ;

engineParam
  : ENGINE_E_ATTR
  | ENGINE_SRC_ATTR
  | ENGINE_TGT_ATTR
  | ENGINE_V_ATTR
  | ENGINE_SRC_VAL
  | ENGINE_TGT_VAL
  | ENGINE_V_VAL
  | ENGINE_MESSAGE
  | ENGINE_CONTEXT
  | ENGINE_REQUEST
  | ENGINE_SERVICEAPI
  ;

// bitwiseaccum can have and() or() set() function
funcName
  : AND
  | OR
  | SET
  | TYPE
  | ident;

gAccRval : GACCNAME exprDimension* ;

localVarLVal : ident ;

constant
  : HYPHEN? CONST_INT
  | HYPHEN? constFpNum
  | GSQL_INT_MAX
  | GSQL_INT_MIN
  | GSQL_UINT_MAX
  | TRUE
  | FALSE
  | CONST_STR
  | TO_DATETIME LPAREN CONST_STR RPAREN
  ;

// floating point number is handled in parser due to lexer ambiguity
// e.g. 1..2 : 1. and .2 instead of 1, .., and 2
// both float and double
constFpNum
  : DOT CONST_INT
  | CONST_INT DOT CONST_INT*
  ;

rangeInterval : RANGE LBRACK exprAtom COMMA exprAtom RBRACK (DOT ident LPAREN exprAtom RPAREN)? ;

// PER
perClause : PER multiplicitySpec
          | PER LPAREN perAlias (COMMA perAlias)* RPAREN
          ;
multiplicitySpec
  : PATH | MATCH | SRC HYPHEN TGT | SRC | TGT | SRC HYPHEN LASTHOP
  | LASTHOP HYPHEN EDGE | LASTHOP HYPHEN SRC | LASTHOP HYPHEN SRC TGT
  ;
perAlias : ident ;


// ACCUM
accumClause : ACCUM assignments ;

assignments : assignment (COMMA assignment)* ;

assignment
  : vAccLval PLUS_EQ expr #assignVAcc
  | vAccLval EQ expr #setVAcc
  | attrLval (EQ | PLUS_EQ) expr #assignAttr
  | DELETE LPAREN var RPAREN #deleteVertexEdge
  | gAccLval PLUS_EQ expr #assignGAcc
  | gAccLval EQ  expr #setGAcc
  | baseType localVarLVal EQ expr #assignDeclLocal
  | localVarLVal EQ expr #assignLocal
  | containerType localContainerVal EQ expr  #assignDeclLocalContainer
  | (tupleTypeName | anonymousTupleType) localTupleVal EQ expr # assignDeclLocalTuple
  | CASE exprAtom whenConstAcc+ (ELSE assignments)? END #assignCaseConst
  | CASE whenCondAcc+ (ELSE assignments)? END #assignCaseCond
  | LOG LPAREN expressions RPAREN #logExpr
  | FOREACH foreachVars DO assignments END #assignForeach
  | IF condition THEN assignments assignElseIfBlock* assignElseBlock? END #assignIf
  | WHILE condition (LIMIT maxIter)? DO assignments END #assignLoop
  | CONTINUE #assignContinue
  | BREAK #assignBreak
  | funcRval #assignFuncRval
  | insertOp #assignInsertBlock
  | exprAtom #assignVFunctRval
  ;

containerType
  : SET LT typeParam GT
  | BAG LT typeParam GT
  | LIST LT typeParam GT
  | MAP LT typeParam COMMA typeParam GT
  ;

localContainerVal : ident ;

localTupleVal : ident;

expressions : expr (COMMA expr)* ;

vAccLval : var DOT VACCNAME exprDimension* ;

attrLval : var DOT ident ;

gAccLval : GACCNAME exprDimension* ;

assignElseIfBlock : ELSE IF condition THEN assignments ;

assignElseBlock : ELSE assignments ;

whenConstAcc : WHEN constant THEN assignments ;

whenCondAcc : WHEN condition THEN assignments ;

// POSTACCUM
postAccumClause : POST_ACCUM (LPAREN ident RPAREN)? assignments ;

// GROUP BY
groupByClause : GROUP BY expr (COMMA expr)* ;

// HAVING
havingClause : HAVING condition ;

// ORDER BY
orderClause : ORDER BY orderKey (COMMA orderKey)* ;

orderKey : exprAtom direction? ;

// LIMIT
offset : exprAtom ;

rowCount : exprAtom ;

limitClause : LIMIT ((offset COMMA)? rowCount | rowCount OFFSET offset) ;

templateFilterClause: LBRACE ident RBRACE;

ident
  : NAME
  | nonReservedKeywords
  ;

// non-reserved keywords
// Tokens that are not reserved words and can be used as identifiers
nonReservedKeywords
  : ABORT
  | ALL
  | BY
  | CANDIDATE_SET
  | COLUMN
  | COMMIT
  | DEFAULT
  | DIRECTED
  | DISTINCT
  | FILE
  | FILTERTYPE
  | FUNCTION
  | GROUP
  | INIT
  | INSERT
  | INSTRUCTION
  | JOIN
  | LASTHOP
  | LEFT
  | LEGACY
  | LIST
  | LOG
  | MAP
  | MATCH
  | NOW
  | ON
  | PATH
  | PER
  | PROJECT
  | RANGE
  | REPLACE
  | SEMIJOIN
  | SRC
  | TGT
  | TO_DATETIME
  | UNDIRECTED
  | UNWIND
  | UPDATE
  | VECTOR
  | VIRTUAL
  ;

globalVarName : ident (EQ expr)? ;

tupleVarName : ident ;

accnames : accnameconst (COMMA accnameconst)* ;

accnameconst : accname dimension* (EQ expr)? ;

accname
  : GACCNAME
  | VACCNAME
  ;
