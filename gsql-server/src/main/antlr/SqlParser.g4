parser grammar SqlParser;

options { tokenVocab = SqlLexer; }

translationJob
  : TRANSLATESQL FOR GRAPH NAME LBRACE block RBRACE
  ;

block
  : selectClause 
    fromClause 
    whereClause? 
    (groupByClause havingClause?)? 
    orderClause?
    limitClause?
    SEMICOLON
  ;

selectClause
  : SELECT DISTINCT? STAR
  | SELECT DISTINCT? selExpr (COMMA selExpr)*
  ;

selExpr 
  : expr (AS? columnName)?
  | tableReference DOT STAR  (AS? columnName)?
  ;

columnName : NAME ;

tableReference : NAME ;   // NAME is either table name or alias

fromClause : FROM atom (COMMA atom)* ;

atom : tableAtom | joinAtom ;

tableAtom : tableName (AS? alias)? ;

tableName : NAME ;

alias : NAME ;

joinAtom : tableAtom (joinOp tableAtom ON condition)+ ;

joinOp  
  : INNER? JOIN
  | (LEFT | RIGHT | FULL) OUTER? JOIN
  ;

whereClause : WHERE condition ;

groupByClause : GROUP BY columnReference  (COMMA columnReference)* ;

columnReference 
  : attribName
  | tableReference DOT attribName
  ;

attribName : NAME ;

havingClause : HAVING condition ;

orderClause : ORDER BY sortKey (COMMA sortKey)* ;

sortKey : (columnReference|expr) (ASC | DESC)? ; 

limitClause : LIMIT expression (OFFSET expression)? ;

expr
  : expression
  | condition
  ;

expression
  : expressionTerm (arithOp expression)?
  ;

expressionTerm
  : nonBooleanConstant                                                                       
  | columnReference                                                               
  | parameter                                                                  
  | QUESTION_MARK                                                                  
  | func LPAREN args? RPAREN                                                      
  | LPAREN expression (COMMA expression)* RPAREN                                                      
  | COUNT LPAREN STAR RPAREN                                                      
  | COUNT LPAREN DISTINCT? expression RPAREN                                      
  | ( AVG | MAX | MIN | SUM ) LPAREN expression RPAREN                             
  | NULLIF LPAREN expression COMMA expression RPAREN                              
  | COALESCE LPAREN expression (COMMA expression)* RPAREN                         
  | CASE expression (WHEN <expression> THEN expression)+ (ELSE expression)? END   
  | CASE (WHEN condition THEN expression)+ (ELSE expression)? END                
  ;
 
nonBooleanConstant
  : CONST_STR
  | MINUS? CONST_INT
  | MINUS? constFpNum
  | TO_DATETIME LPAREN CONST_STR RPAREN
  ;

constFpNum
  : DOT CONST_INT
  | CONST_INT DOT CONST_INT?
  ;

parameter : AT NAME ;

func : NAME ;

args : expr (COMMA expr)* ;

arithOp : STAR | DIV | PLUS | MINUS ;

condition                                                   
  : TRUE                                              #CondTrue
  | FALSE                                             #CondFalse
  | columnReference                                   #CondColRef 
  | parameter                                         #CondParam
  | QUESTION_MARK                                     #CondQM
  | func LPAREN args? RPAREN                          #CondFunc
  | expression IS NOT? NULL                           #CondNullCheck
  | expression cmpOp expression                       #CondCompare
  | expression BETWEEN expression AND expression      #CondBetween
  | LPAREN condition RPAREN                           #CondParen
  | NOT condition                                     #CondNot
  | condition AND condition                           #CondAnd
  | condition OR condition                            #CondOr
  ;

cmpOp : NEQ | GTE | LTE | GT | LT | EQ | LT GT | LIKE | NOT LIKE | IN | NOT IN | CONTAINS ;

