/**
 * Description: extract javacc TigerGraph.jj loader and ddl grammar to EBNF 
 *              so that we can unify javacc legacy code to antlr
 * Author: Mingxi 
 * Date: Feb 11, 2017 
 */

createQb ::= 'create' (createUserQb | createSecreteQb | createTokenQb |
                       createVertexQb | createEdgeQb | createGraphQb |
                       createLoadingJobQb | createSchemaChangeJobQb |
                       createGraphQueryQb)

createLoadingJobQb ::= ('loading' | 'online_post') 'job' ident 'for' 'graph' ident '{'  
               ('load' (offline_link | online_link) 'to' target (',' 'to' target)* 'using' keyValSuffix? ';')+ '}'

//offline_link can be a string (a file path), or a temp table name
offline_link ::=  'temp_table' ident | String

//online_link can be empty, or a temp table name
online_link ::= ('temp_table' ident)?

target ::= targetName mappings whereClause? options?

targetName ::= 'vertex' ident | 'edge' ident | 'temp_table' ident '(' ident (',' ident)* ')' 

mappings ::= 'values' '(' srcColIndex (',' srcColIndex)* ')' 

srcColIndex ::= indexPrimitiveElement 

indexPrimitiveElement ::= refByPosition | refByLit | refByNull | refByTokenFunc |
         refByReduceFunc | refByFlatten | refByFlattenJson | refByKv

refByPosition ::= '$' (Integer | ident (':' ident)* | 'sys.file_name') ident? 
refByLit ::= (Integer | String | Real)  ident?   
refByNull ::= 'null' | '_' 
refByTokenFunc ::= ident '(' indexPrimitiveElement (',' indexPrimitiveElement)* ')' ident? 
refByReduceFunc ::=  'reduce' '(' aggFunc '(' indexPrimitiveElement ')' ')' 
refByFlatten ::= 'flatten' '(' indexPrimitiveElement ',' String ','  (String ',')? Int ')' 
refByFlattenJson ::= 'flatten_json_array' '(' indexPrimitiveElement (',' indexPrimitiveElement)* ')' 
refByKv ::= '(' indexPrimitiveElement '->' indexPrimitiveElement ')' 

aggFunc ::= ident | 'min' | 'max' | 'add' | 'and' | 'or' | 'overwrite'

whereClause ::= 'where'  condition

expr ::= refByPosition | refByLit | refByTokenFunc

keyValSuffix ::= keyValConfig (',' keyValConfig)*

keyValConfig ::= ident '=' String



