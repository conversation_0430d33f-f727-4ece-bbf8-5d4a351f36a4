## Default owners
* @dadongwang-tg

## default to call all owners
gsql-server/src/main/java/com/tigergraph/schema/operation @dadongwang-tg @adil-ainihaer @ArthurXie-tg @minzhangtg @xun-gao-tigergraph @zhihuapengtg @DavidFan2000 @jue-yuan
gsql-server/src/main/java/com/tigergraph/utility/IOUtil.java @dadongwang-tg @adil-ainihaer @ArthurXie-tg @minzhangtg @xun-gao-tigergraph @zhihuapengtg @DavidFan2000 @jue-yuan
gsql-server/src/main/java/com/tigergraph/utility/NameUtil.java @dadongwang-tg @adil-ainihaer @ArthurXie-tg @minzhangtg @xun-gao-tigergraph @zhihuapengtg @DavidFan2000 @jue-yuan
gsql-server/src/main/java/com/tigergraph/utility/StringUtil.java @dadongwang-tg @adil-ainihaer @ArthurXie-tg @minzhangtg @xun-gao-tigergraph @zhihuapengtg @DavidFan2000 @jue-yuan

## Audit Log
gsql-server/src/main/java/com/tigergraph/common/logger @ArthurXie-tg

## Authentication (LDAP/SSO/Kerberos/etc)
gsql-server/src/main/java/com/tigergraph/schema/security/auth @xun-gao-tigergraph @zhihuapengtg
gsql-server/src/main/java/com/tigergraph/schema/utility/JWTAuthUtil.java @xun-gao-tigergraph @zhihuapengtg
gsql-server/src/main/java/com/tigergraph/schema/utility/LdapAuthUtil.java @xun-gao-tigergraph @zhihuapengtg
gsql-server/src/main/java/com/tigergraph/utility/AuthenticateUtil.java @xun-gao-tigergraph @zhihuapengtg

## Catalog & Schema Change
gsql-server/src/main/java/com/tigergraph/common/ToStringMasked.java @minzhangtg @jue-yuan
gsql-server/src/main/java/com/tigergraph/schema/catalog @minzhangtg @jue-yuan
gsql-server/src/main/java/com/tigergraph/schema/config @minzhangtg @jue-yuan
gsql-server/src/main/java/com/tigergraph/schema/lock @minzhangtg @jue-yuan
gsql-server/src/main/java/com/tigergraph/schema/plan/configs @minzhangtg @jue-yuan
gsql-server/src/main/java/com/tigergraph/schema/topology @minzhangtg @jue-yuan
gsql-server/src/main/java/com/tigergraph/schema/utility @minzhangtg @jue-yuan
gsql-server/src/main/java/com/tigergraph/schema/* @minzhangtg @jue-yuan
gsql-server/src/main/java/com/tigergraph/utility/CollectionUtil.java @minzhangtg @jue-yuan
gsql-server/src/main/java/com/tigergraph/utility/DateTimeUtil.java @minzhangtg @jue-yuan

## Compile Query Runtime

## Cross Region Replication (Metadata)
gsql-server/src/main/java/com/tigergraph/schema/crr @ArthurXie-tg @DavidFan2000

## Database Import / Export
gsql-server/src/main/java/com/tigergraph/schema/controller/ExportImportController.java @xun-gao-tigergraph
gsql-server/src/main/java/com/tigergraph/schema/operation/helper @xun-gao-tigergraph

## Gradle Build & Dev Environment
.github @jue-yuan
.vscode @jue-yuan
tools @jue-yuan
config @jue-yuan
build.gradle @jue-yuan
gradle.properties @jue-yuan
gradlew @jue-yuan
gradlew.bat @jue-yuan
settings.gradle @jue-yuan
gradle/wrapper @jue-yuan
gsql-client/build.gradle @jue-yuan
gsql-client/gradle.properties @jue-yuan
gsql-server/build.gradle @jue-yuan
gsql-server/gradle.properties @jue-yuan

## GSQL Client
gsql-client @xun-gao-tigergraph

## GSQL Interpreted Query
gsql-server/src/main/java/com/tigergraph/engine/normalizer @minzhangtg @DavidFan2000
gsql-server/src/main/java/com/tigergraph/engine/semchecker/InterpretFeatureChecker.java @minzhangtg @DavidFan2000
gsql-server/src/main/java/com/tigergraph/engine/sql2gsql @minzhangtg @DavidFan2000

## High Availability (HA)
gsql-server/src/main/java/com/tigergraph/schema/GsqlHAHandler.java @zhihuapengtg @jue-yuan

## HTTP Server & REST API
gsql-server/src/main/java/com/tigergraph/common/util @xun-gao-tigergraph @minzhangtg
gsql-server/src/main/java/com/tigergraph/common/MessageBundle.java @xun-gao-tigergraph @minzhangtg
gsql-server/src/main/java/com/tigergraph/schema/annotation @xun-gao-tigergraph @minzhangtg
gsql-server/src/main/java/com/tigergraph/schema/apidoc @xun-gao-tigergraph @minzhangtg
gsql-server/src/main/java/com/tigergraph/schema/controller @xun-gao-tigergraph @minzhangtg
gsql-server/src/main/java/com/tigergraph/schema/interceptor @xun-gao-tigergraph @minzhangtg
gsql-server/src/main/java/com/tigergraph/schema/jna @xun-gao-tigergraph @minzhangtg
gsql-server/src/main/java/com/tigergraph/schema/request @xun-gao-tigergraph @minzhangtg
gsql-server/src/main/java/com/tigergraph/schema/security/util/CertificateKeyUtil.java @xun-gao-tigergraph @minzhangtg
gsql-server/src/main/java/com/tigergraph/schema/utility/HttpAsyncCall.java @xun-gao-tigergraph @minzhangtg
gsql-server/src/main/java/com/tigergraph/schema/utility/HttpConnectionOperator.java @xun-gao-tigergraph @minzhangtg
gsql-server/src/main/java/com/tigergraph/schema/utility/HttpException.java @xun-gao-tigergraph @minzhangtg
gsql-server/src/main/java/com/tigergraph/schema/utility/HttpHeader.java @xun-gao-tigergraph @minzhangtg
gsql-server/src/main/java/com/tigergraph/schema/utility/HttpHeadersBuilder.java @xun-gao-tigergraph @minzhangtg
gsql-server/src/main/java/com/tigergraph/schema/utility/HttpMethod.java @xun-gao-tigergraph @minzhangtg
gsql-server/src/main/java/com/tigergraph/schema/utility/HttpRetryHandler.java @xun-gao-tigergraph @minzhangtg
gsql-server/src/main/java/com/tigergraph/schema/utility/QueryParamParser.java @xun-gao-tigergraph @minzhangtg
gsql-server/src/main/java/com/tigergraph/schema/version @xun-gao-tigergraph @minzhangtg
gsql-server/src/main/java/com/tigergraph/utility/GSQLResponse.java @xun-gao-tigergraph @minzhangtg
gsql-server/src/main/resources/application.properties @xun-gao-tigergraph @minzhangtg
gsql-server/src/main/resources/MessageBundle.properties @xun-gao-tigergraph @minzhangtg

## Loading Job & Datasource
gsql-server/src/main/java/com/tigergraph/schema/connector @zhihuapengtg
gsql-server/src/main/java/com/tigergraph/schema/kafka @zhihuapengtg
gsql-server/src/main/java/com/tigergraph/schema/LoadOptions.java @zhihuapengtg
gsql-server/src/main/java/com/tigergraph/schema/metadata @zhihuapengtg
gsql-server/src/main/java/com/tigergraph/schema/plan/job @zhihuapengtg
gsql-server/src/main/resources/compiletokenbank @zhihuapengtg

## Non-Query Syntax & Semantics
gsql-server/src/main/java/com/tigergraph/schema/ast @xun-gao-tigergraph @adil-ainihaer
gsql-server/src/main/java/com/tigergraph/common/GsqlReflectionToStringBuilder.java @xun-gao-tigergraph @adil-ainihaer
gsql-server/src/main/java/com/tigergraph/common/JsonAPIVersion.java @xun-gao-tigergraph @adil-ainihaer
gsql-server/src/main/javacc @xun-gao-tigergraph @adil-ainihaer

## OpenCypher Query
gsql-server/src/main/java/com/tigergraph/engine/cypher2gsql @adil-ainihaer @DavidFan2000 @jue-yuan
gsql-server/src/main/antlr/Cypher.g4 @adil-ainihaer @DavidFan2000 @jue-yuan

## Query Code Generation
gsql-server/src/main/java/com/tigergraph/engine/codegen @adil-ainihaer @ArthurXie-tg @minzhangtg @zhihuapengtg
gsql-server/src/main/java/com/tigergraph/utility/CodeGenUtil.java @adil-ainihaer @ArthurXie-tg @minzhangtg @zhihuapengtg

## Query Installation & Compilation
gsql-server/src/main/java/com/tigergraph/schema/plan/query/install @minzhangtg @ArthurXie-tg

## Query Optimizer
gsql-server/src/main/java/com/tigergraph/engine/optim @DavidFan2000
gsql-server/src/main/java/com/tigergraph/schema/db @DavidFan2000
gsql-server/src/main/java/com/tigergraph/schema/statistics @DavidFan2000
gsql-server/src/main/proto @DavidFan2000

## Query Planner
gsql-server/src/main/java/com/tigergraph/engine/queryplanner @adil-ainihaer @jue-yuan @zhihuapengtg
gsql-server/src/main/java/com/tigergraph/utility/RuntimeUtil.java @adil-ainihaer @jue-yuan @zhihuapengtg

## Query Syntax & Semantics
gsql-server/src/main/antlr @jue-yuan @adil-ainihaer
gsql-server/src/main/java/com/tigergraph/engine/semchecker @jue-yuan @adil-ainihaer
gsql-server/src/main/java/com/tigergraph/engine/typechecker @jue-yuan @adil-ainihaer
gsql-server/src/main/java/com/tigergraph/engine/util @jue-yuan @adil-ainihaer
gsql-server/src/main/java/com/tigergraph/common/SyntaxVersion.java @jue-yuan @adil-ainihaer
gsql-server/src/main/java/com/tigergraph/schema/plan/query @jue-yuan @adil-ainihaer
gsql-server/src/main/java/com/tigergraph/schema/plan/templatequery @jue-yuan @adil-ainihaer
gsql-server/src/main/java/com/tigergraph/utility/TokenParserErrorListener.java @jue-yuan @adil-ainihaer

## Query Transformation
gsql-server/src/main/java/com/tigergraph/engine/preproc @DavidFan2000 @jue-yuan @adil-ainihaer

## RBAC
gsql-server/src/main/java/com/tigergraph/schema/plan/function @jue-yuan @DavidFan2000
gsql-server/src/main/java/com/tigergraph/schema/plan/packages @jue-yuan @DavidFan2000
gsql-server/src/main/java/com/tigergraph/schema/plan/policy @jue-yuan @DavidFan2000
gsql-server/src/main/java/com/tigergraph/schema/security/acl @jue-yuan @DavidFan2000
gsql-server/src/main/java/com/tigergraph/schema/security/rbac @jue-yuan @DavidFan2000
gsql-server/src/main/java/com/tigergraph/schema/security/util/ @jue-yuan @DavidFan2000
gsql-server/src/main/java/com/tigergraph/schema/ClearTagBitQueryChecker.java @jue-yuan @DavidFan2000

## Security Vulnerability
.snyk @jue-yuan @adil-ainihaer
gsql-server/src/main/java/com/tigergraph/schema/security/fileinputoutput @jue-yuan @adil-ainihaer

## Upgrade & Backup/Restore & Workload Management
tools/upgrade @zhihuapengtg
gsql-server/src/main/java/com/tigergraph/schema/catalog/SpecialOperationRunner.java @zhihuapengtg
gsql-server/src/main/java/com/tigergraph/schema/controller/BackupRestoreController.java @zhihuapengtg
gsql-server/src/main/java/com/tigergraph/schema/grpc @zhihuapengtg
gsql-server/src/main/java/com/tigergraph/schema/wlm @zhihuapengtg
gsql-server/src/main/java/com/tigergraph/schema/utility/BackCompatibilityUtil.java @zhihuapengtg
gsql-server/src/main/java/com/tigergraph/schema/utility/BackCompatQueryFileInOutPathCollector.java @zhihuapengtg
gsql-server/src/main/java/com/tigergraph/schema/utility/ExprFunctionsMergeUtil.java @zhihuapengtg
gsql-server/src/main/java/com/tigergraph/schema/utility/PushDictOperation.java @zhihuapengtg
gsql-server/src/main/java/com/tigergraph/utility/EtcdUdfCacheUtil.java @zhihuapengtg

## User Defined Function
schema/security/udf @DavidFan2000 @xun-gao-tigergraph
regress/resources/common @DavidFan2000 @xun-gao-tigergraph
gsql-server/src/main/java/com/tigergraph/utility/GithubUdfCacheUtil.java @DavidFan2000 @xun-gao-tigergraph
