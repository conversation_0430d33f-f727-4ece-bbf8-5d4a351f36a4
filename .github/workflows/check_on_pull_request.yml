# Run checks on every push

name: Check

on:
  pull_request:

concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

jobs:
  cloc:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout feature branch
        uses: actions/checkout@v2
      - name: CLOC featrue branch (main)
        id: scc-feat-main
        uses: Adapt-API/scc-docker-action@master
        with:
          args: ${{ env.workspace }} --exclude-dir */src/test --exclude-dir regress --exclude-dir refcard --format json
      - name: CLOC feature branch (test)
        id: scc-feat-test
        uses: Adapt-API/scc-docker-action@master
        with:
          args: ${{ env.workspace }} */src/test regress --exclude-dir refcard --format json
      - name: Checkout base branch
        uses: actions/checkout@v2
        with:
          ref: ${{ github.base_ref }}
      - name: CLOC base branch (main)
        id: scc-base-main
        uses: Adapt-API/scc-docker-action@master
        with:
          args: ${{ env.workspace }} --exclude-dir */src/test --exclude-dir regress --exclude-dir refcard --format json
      - name: CLOC base branch (test)
        id: scc-base-test
        uses: Adapt-API/scc-docker-action@master
        with:
          args: ${{ env.workspace }} */src/test regress --exclude-dir refcard --format json
      - name: Tabularize CLOC (main)
        id: scc-table-main
        env:
          SCC_BASE_JSON: ${{ steps.scc-base-main.outputs.scc }}
          SCC_FEAT_JSON: ${{ steps.scc-feat-main.outputs.scc }}
        run: |
          echo -e "SCC_BASE_JSON:\n$SCC_BASE_JSON"
          echo -e "SCC_FEAT_JSON:\n$SCC_FEAT_JSON"
          DIFF_JSON=$(jq -n --argjson base "$SCC_BASE_JSON" --argjson feat "$SCC_FEAT_JSON" '
            ($base + $feat | group_by(.Name) | map(select(length == 2) | {
              Name: .[1].Name,
              Count: .[1].Count,
              CountDiff: (.[1].Count-.[0].Count),
              Lines: .[1].Lines,
              LinesDiff: (.[1].Lines-.[0].Lines),
              Code: .[1].Code,
              CodeDiff: (.[1].Code-.[0].Code),
              Comment: .[1].Comment,
              CommentDiff: (.[1].Comment-.[0].Comment),
              Blank: .[1].Blank,
              BlankDiff: (.[1].Blank-.[0].Blank),
            }))
            | map(select(.CountDiff != 0 or .LinesDiff != 0 or .CodeDiff != 0 or .CommentDiff != 0 or .BlankDiff != 0))
            | map(
              if .CountDiff > 0 then
                .CountDiff = " (+" + (.CountDiff | tostring) + ")"
              elif .CountDiff == 0 then
                .CountDiff = ("" | tostring)
              else
                .CountDiff = " (" + (.CountDiff | tostring) + ")"
              end)
            | map(
              if .LinesDiff > 0 then
                .LinesDiff = " (+" + (.LinesDiff | tostring) + ")"
              elif .LinesDiff == 0 then
                .LinesDiff = ("" | tostring)
              else
                .LinesDiff = " (" + (.LinesDiff | tostring) + ")"
              end)
            | map(
              if .CodeDiff > 0 then
                .CodeDiff = " (+" + (.CodeDiff | tostring) + ")"
              elif .CodeDiff == 0 then
                .CodeDiff = ("" | tostring)
              else
                .CodeDiff = " (" + (.CodeDiff | tostring) + ")"
              end)
            | map(
              if .CommentDiff > 0 then
                .CommentDiff = " (+" + (.CommentDiff | tostring) + ")"
              elif .CommentDiff == 0 then
                .CommentDiff = ("" | tostring)
              else
                .CommentDiff = " (" + (.CommentDiff | tostring) + ")"
              end)
            | map(
              if .BlankDiff > 0 then
                .BlankDiff = " (+" + (.BlankDiff | tostring) + ")"
              elif .BlankDiff == 0 then
                .BlankDiff = ("" | tostring)
              else
                .BlankDiff = " (" + (.BlankDiff | tostring) + ")"
              end)
            | map({
              Name: .Name,
              Count: "\(.Count)\(.CountDiff)",
              Lines: "\(.Lines)\(.LinesDiff)",
              Code: "\(.Code)\(.CodeDiff)",
              Comment: "\(.Comment)\(.CommentDiff)",
              Blank: "\(.Blank)\(.BlankDiff)"})')
          echo -e "DIFF_JSON:\n$DIFF_JSON"
          OLD_JSON=$(jq -n --argjson base "$SCC_BASE_JSON" --argjson feat "$SCC_FEAT_JSON" '
              $base | map(select(.Name as $n | $feat | map(select(.Name == $n)) | length == 0))
              | map ({
              Name: .Name,
              Count: (if .Count == 0 then "0" else "0 (-\(.Count))" end),
              Lines: (if .Lines == 0 then "0" else "0 (-\(.Lines))" end),
              Code: (if .Code == 0 then "0" else "0 (-\(.Code))" end),
              Comment: (if .Comment == 0 then "0" else "0 (-\(.Comment))" end),
              Blank: (if .Blank == 0 then "0" else "0 (-\(.Blank))" end)})')
          echo -e "OLD_JSON:\n$OLD_JSON"
          NEW_JSON=$(jq -n --argjson base "$SCC_BASE_JSON" --argjson feat "$SCC_FEAT_JSON" '
            $feat | map(select(.Name as $n | $base | map(select(.Name == $n)) | length == 0))
            | map ({
              Name: .Name,
              Count: (if .Count == 0 then "0" else "\(.Count) (+\(.Count))" end),
              Lines: (if .Lines == 0 then "0" else "\(.Lines) (+\(.Lines))" end),
              Code: (if .Code == 0 then "0" else "\(.Lines) (+\(.Code))" end),
              Comment: (if .Comment == 0 then "0" else "\(.Lines) (+\(.Comment))" end),
              Blank: (if .Blank == 0 then "0" else "\(.Lines) (+\(.Blank))" end)})')
          echo -e "NEW_JSON:\n$NEW_JSON"
          COMB_JSON=$(jq --argjson diff "$DIFF_JSON" --argjson old "$OLD_JSON" --argjson new "$NEW_JSON" -n '$diff + $old + $new')
          echo -e "COMB_JSON:\n$COMB_JSON"
          if [ $(echo "$COMB_JSON" | jq -r '. | length') -gt 0 ]; then
            SCC_MAIN_TABLE=$(echo "$COMB_JSON" | jq -r '(.[0] | keys_unsorted | (.,map(length*"-"))), .[] | map(.) | @tsv' | sed 's/\t/|/g')
          else
            SCC_MAIN_TABLE='N/A'
          fi
          echo -e "SCC_MAIN_TABLE:\n$SCC_MAIN_TABLE"
          echo "scc<<EOF" >> $GITHUB_OUTPUT
          echo "$SCC_MAIN_TABLE" >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT
      - name: Tabularize CLOC (test)
        id: scc-table-test
        env:
          SCC_BASE_JSON: ${{ steps.scc-base-test.outputs.scc }}
          SCC_FEAT_JSON: ${{ steps.scc-feat-test.outputs.scc }}
        run: |
          echo -e "SCC_BASE_JSON:\n$SCC_BASE_JSON"
          echo -e "SCC_FEAT_JSON:\n$SCC_FEAT_JSON"
          DIFF_JSON=$(jq -n --argjson base "$SCC_BASE_JSON" --argjson feat "$SCC_FEAT_JSON" '
            ($base + $feat | group_by(.Name) | map(select(length == 2) | {
              Name: .[1].Name,
              Count: .[1].Count,
              CountDiff: (.[1].Count-.[0].Count),
              Lines: .[1].Lines,
              LinesDiff: (.[1].Lines-.[0].Lines),
              Code: .[1].Code,
              CodeDiff: (.[1].Code-.[0].Code),
              Comment: .[1].Comment,
              CommentDiff: (.[1].Comment-.[0].Comment),
              Blank: .[1].Blank,
              BlankDiff: (.[1].Blank-.[0].Blank),
            }))
            | map(select(.CountDiff != 0 or .LinesDiff != 0 or .CodeDiff != 0 or .CommentDiff != 0 or .BlankDiff != 0))
            | map(
              if .CountDiff > 0 then
                .CountDiff = " (+" + (.CountDiff | tostring) + ")"
              elif .CountDiff == 0 then
                .CountDiff = ("" | tostring)
              else
                .CountDiff = " (" + (.CountDiff | tostring) + ")"
              end)
            | map(
              if .LinesDiff > 0 then
                .LinesDiff = " (+" + (.LinesDiff | tostring) + ")"
              elif .LinesDiff == 0 then
                .LinesDiff = ("" | tostring)
              else
                .LinesDiff = " (" + (.LinesDiff | tostring) + ")"
              end)
            | map(
              if .CodeDiff > 0 then
                .CodeDiff = " (+" + (.CodeDiff | tostring) + ")"
              elif .CodeDiff == 0 then
                .CodeDiff = ("" | tostring)
              else
                .CodeDiff = " (" + (.CodeDiff | tostring) + ")"
              end)
            | map(
              if .CommentDiff > 0 then
                .CommentDiff = " (+" + (.CommentDiff | tostring) + ")"
              elif .CommentDiff == 0 then
                .CommentDiff = ("" | tostring)
              else
                .CommentDiff = " (" + (.CommentDiff | tostring) + ")"
              end)
            | map(
              if .BlankDiff > 0 then
                .BlankDiff = " (+" + (.BlankDiff | tostring) + ")"
              elif .BlankDiff == 0 then
                .BlankDiff = ("" | tostring)
              else
                .BlankDiff = " (" + (.BlankDiff | tostring) + ")"
              end)
            | map({
              Name: .Name,
              Count: "\(.Count)\(.CountDiff)",
              Lines: "\(.Lines)\(.LinesDiff)",
              Code: "\(.Code)\(.CodeDiff)",
              Comment: "\(.Comment)\(.CommentDiff)",
              Blank: "\(.Blank)\(.BlankDiff)"})')
          echo -e "DIFF_JSON:\n$DIFF_JSON"
          OLD_JSON=$(jq -n --argjson base "$SCC_BASE_JSON" --argjson feat "$SCC_FEAT_JSON" '
              $base | map(select(.Name as $n | $feat | map(select(.Name == $n)) | length == 0))
              | map ({
              Name: .Name,
              Count: (if .Count == 0 then "0" else "0 (-\(.Count))" end),
              Lines: (if .Lines == 0 then "0" else "0 (-\(.Lines))" end),
              Code: (if .Code == 0 then "0" else "0 (-\(.Code))" end),
              Comment: (if .Comment == 0 then "0" else "0 (-\(.Comment))" end),
              Blank: (if .Blank == 0 then "0" else "0 (-\(.Blank))" end)})')
          echo -e "OLD_JSON:\n$OLD_JSON"
          NEW_JSON=$(jq -n --argjson base "$SCC_BASE_JSON" --argjson feat "$SCC_FEAT_JSON" '
            $feat | map(select(.Name as $n | $base | map(select(.Name == $n)) | length == 0))
            | map ({
              Name: .Name,
              Count: (if .Count == 0 then "0" else "\(.Count) (+\(.Count))" end),
              Lines: (if .Lines == 0 then "0" else "\(.Lines) (+\(.Lines))" end),
              Code: (if .Code == 0 then "0" else "\(.Lines) (+\(.Code))" end),
              Comment: (if .Comment == 0 then "0" else "\(.Lines) (+\(.Comment))" end),
              Blank: (if .Blank == 0 then "0" else "\(.Lines) (+\(.Blank))" end)})')
          echo -e "NEW_JSON:\n$NEW_JSON"
          COMB_JSON=$(jq --argjson diff "$DIFF_JSON" --argjson old "$OLD_JSON" --argjson new "$NEW_JSON" -n '$diff + $old + $new')
          echo -e "COMB_JSON:\n$COMB_JSON"
          if [ $(echo "$COMB_JSON" | jq -r '. | length') -gt 0 ]; then
            SCC_TEST_TABLE=$(echo "$COMB_JSON" | jq -r '(.[0] | keys_unsorted | (.,map(length*"-"))), .[] | map(.) | @tsv' | sed 's/\t/|/g')
          else
            SCC_TEST_TABLE='N/A'
          fi
          echo -e "SCC_TEST_TABLE:\n$SCC_TEST_TABLE"
          echo "scc<<EOF" >> $GITHUB_OUTPUT
          echo "$SCC_TEST_TABLE" >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT
      - name: Add a sticky comment about LOC
        continue-on-error: true
        uses: marocchino/sticky-pull-request-comment@v2
        with:
          header: LOC
          message: |
            # Changed Lines of Code
            ## Main
            ${{ steps.scc-table-main.outputs.scc }}
            ## Test
            ${{ steps.scc-table-test.outputs.scc }}
