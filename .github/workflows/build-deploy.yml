# Run Gradle build task.
# See https://docs.gradle.org/current/userguide/java_plugin.html#lifecycle_tasks for more info.

name: OpenCypher Build & Deploy

on:
  push:
    branches:
      # accept all branches
      - 'GLE-5326-cypher-translation-integration'
    tags:
      # ignore all tags (wip, mit, ...)
      - '!**'

concurrency:
  group: ${{ github.workflow }}-deploy-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

jobs:
  build:
    runs-on: ubuntu-latest
    outputs:
      sha: ${{ env.SHA }}
      tg_version: ${{ env.TG_VERSION }}
    steps:
      - uses: actions/checkout@v2
        with:
          # fetch all history to find the base tg_x.y.z_dev branch
          fetch-depth: 0
          token: ${{ secrets.QA_TOKEN }}
      - uses: actions/cache@v4
        with:
          path: |
            ~/.gradle/caches
            ~/.gradle/wrapper
          key: build-${{ runner.os }}-${{ hashFiles('**/build.gradle', 'gradle.properties') }}

      - uses: actions/setup-java@v1
        with:
          java-version: '17.0.15'

      - name: Get commit SHA
        id: get-commit-step
        run: |
          sha=${{ github.sha }}
          if [ "${{ github.event_name }}" == "pull_request" ]; then sha=${{ github.event.pull_request.head.sha }}; fi
          echo "SHA=$sha" >> $GITHUB_ENV
          TG_VERSION=`curl 'http://${{ secrets.OPENCYPHER_SERVER_HOST }}:14240/api/version'  --compressed --insecure | jq -r '.results.tigergraph_version'`
          echo "TG_VERSION=$TG_VERSION" >> $GITHUB_ENV

      - run: ./gradlew build -Psubmodule=true -x test
        env:
          QA_TOKEN: ${{ secrets.QA_TOKEN }}

      - name: SSH Copy File
        id: copy
        uses: appleboy/scp-action@v0.1.4
        with:
          host: ${{ secrets.OPENCYPHER_SERVER_HOST }}
          username: ${{ secrets.OPENCYPHER_SERVER_USER }}
          key: ${{ secrets.OPENCYPHER_SERVER_SSH_KEY }}
          port: 22
          source: "bin/gsql.jar,bin/gsql_client.jar"
          target: "/home/<USER>/tigergraph/app/${{ env.TG_VERSION }}/dev/gdk/gsql/lib"
          strip_components: 1

      - name: SSH and deploy app
        id: deploy
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.OPENCYPHER_SERVER_HOST }}
          username: ${{ secrets.OPENCYPHER_SERVER_USER }}
          key: ${{ secrets.OPENCYPHER_SERVER_SSH_KEY }}
          port: 22
          script: |
            cd /home/<USER>/tigergraph/app/${{ env.TG_VERSION }}/dev/gdk/gsql/lib
            mv gsql.jar .tg_dbs_gsqld.jar
            chmod +x .tg_dbs_gsqld.jar
            chmod +x gsql_client.jar
            gadmin restart gsql -y

  slack-message:
    name: Send slack message
    runs-on: ubuntu-latest
    needs: [build]
    if: |
      always()
    steps:
      - if: needs.build.result == 'success'
        run: |
          echo "deploy_status=:large_green_circle:" >> $GITHUB_ENV
      
      - if: needs.build.result == 'failure'
        run: |
          echo "deploy_status=:red_circle:" >> $GITHUB_ENV
      - name: Slack Message
        shell: bash
        run: |
          slackMessage='{
            "blocks": [
              {
                "type": "header",
                "text": {
                  "type": "plain_text",
                  "text": "OpenCypher GSQL Deployment Report ${{ env.deploy_status }}",
                  "emoji": true
                }
              },
              {
                "type": "section",
                "fields": [
                  {
                    "type": "mrkdwn",
                    "text": "*Branch:*\n${{ github.ref }}"
                  },
                  {
                    "type": "mrkdwn",
                    "text": "*Actor:*\n${{ github.actor }}"
                  },
                  {
                    "type": "mrkdwn",
                    "text": "*Sha:*\n${{ needs.build.outputs.sha }}"
                  }
                ]
              },
              {
                "type": "section",
                "text": {
                  "type": "mrkdwn",
                  "text": "<https://github.com/tigergraph/gle/actions/runs/${{ github.run_id }}|View Github Action>"
                }
              }
            ]
          }'
          curl -X POST ${{ secrets.OPENCYPHER_SLACK_WEBHOOK_URL }} -H 'Content-type:application/json' \
          --data @<(cat <<EOF
          $slackMessage
          EOF
          )


     
