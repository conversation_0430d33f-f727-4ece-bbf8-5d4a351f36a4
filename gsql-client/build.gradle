plugins {
  id 'maven-publish'
}

dependencies {
  implementation (group: 'commons-cli', name: 'commons-cli', version: '1.4')
  implementation (group: 'jline', name: 'jline', version: '2.11')
  implementation (group: 'org.apache.logging.log4j', name: 'log4j-core', version: log4jVersion)
  implementation (group: 'org.apache.httpcomponents', name: 'httpclient', version: '4.5.13')
  implementation (group: 'org.json', name: 'json', version: jsonVersion)

  constraints {
    // in org.apache.httpcomponents:httpclient
    implementation (group: 'commons-codec', name: 'commons-codec', version: '1.15')
  }
}

ext {
  // debug config file name
  debugCfgFile = '.debug_client'
}

sourceSets {
  main {
    java {
      srcDirs = [srcMainJava]
    }
    resources {
      srcDirs = [srcMainRsrc, srcGenMainRsrc]
    }
  }
}

compileJava {
  doFirst { println "Compiling ${project.name}"}
}

// handle resource files
processResources {
  dependsOn writeVersionProp
}

group = 'com.tigergraph'

jar {
  doFirst { println "Assembling ${project.name}.jar" }

  // archiveBaseName = project.name
  // archiveExtension = 'jar'
  archiveFileName = 'gsql_client.jar'
  duplicatesStrategy = DuplicatesStrategy.INCLUDE
  from (sourceSets.main.output)
  from (configurations.runtimeClasspath.collect { it.isDirectory() ? it : zipTree(it) })
  manifest.attributes(
      'Main-Class': "${project.group}.client.Driver",
      'Multi-Release': 'true')
  // make client compatible with Java 8
  sourceCompatibility = 1.8

  doLast {
    copy {
      from "${buildDir}/libs/gsql_client.jar"
      into "${project.rootDir}/bin/"
    }
  }
}

task enableDebug {
  dependsOn rootProject.hasTigerGraph
  doLast {
    def retVal = exec {
      commandLine "${buildToolsDir}/enable_debug.sh", debugCfgFile, debugPort, debugSuspend, remoteHostname
    }
    if (retVal.getExitValue() == 0) {
      println ("[   Info] JVM debug port (${debugPort}) is enabled")
    }
  }
}

task disableDebug {
  dependsOn rootProject.hasTigerGraph
  doLast {
    def retVal = exec {
      commandLine "${buildToolsDir}/disable_debug.sh", debugCfgFile, remoteHostname
    }
    if (retVal.getExitValue() != 0) {
      throw new GradleException("Failed to disable debug.")
    }
  }
}

// Maven publish (WIP)
publishing {
  publications {
    testMaven(MavenPublication) {
      groupId = 'com.tigergraph'
      artifactId = 'gsql-client'
      version = '3.1.0'

      from components.java
    }
  }

  repositories {
    maven {
      url "${buildDir}/maven"
    }
  }
}

clean {
  doFirst { println 'Cleaning auto-generated files' }

  delete srcGenRoot // delete auto-generated source files
}
