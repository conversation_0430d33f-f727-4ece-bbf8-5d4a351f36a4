/**
 * ***************************************************************************
 * Copyright (c) 2017, TigerGraph Inc.
 * All rights reserved
 * Unauthorized copying of this file, via any medium is
 * strictly prohibited
 * Proprietary and confidential
 * ****************************************************************************
 */
package com.tigergraph.client;

import com.tigergraph.client.logging.GsqlClientLogger;
import com.tigergraph.client.util.*;
import com.tigergraph.client.util.SystemUtils.ExitStatus;
import jline.console.ConsoleReader;
import jline.console.completer.ArgumentCompleter;
import jline.console.completer.FileNameCompleter;
import jline.console.history.FileHistory;
import jline.console.history.History;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.*;
import java.net.ConnectException;
import java.net.HttpURLConnection;
import java.net.URLEncoder;
import java.nio.file.FileAlreadyExistsException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.attribute.PosixFilePermissions;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.tigergraph.client.Driver.IS_LOCAL;
import static com.tigergraph.client.util.SystemUtils.logger;
import static java.nio.charset.StandardCharsets.UTF_8;

public class Client {
  private static final String DEFAULT_USER = "tigergraph";
  private static final String DEFAULT_PASSWORD = "tigergraph";
  private static final String SECRET_USER = "__GSQL__secret";
  private static final String TOKEN_USER = "__GSQL__token";

  private static final String ENDPOINT_STATEMENTS = "v1/statements";
  private static final String ENDPOINT_VERSION = "v1/version";
  private static final String ENDPOINT_HELP = "v1/help";
  private static final String ENDPOINT_LOGIN = "v1/auth/login";
  private static final String ENDPOINT_RESET = "v1/reset";
  private static final String ENDPOINT_PRECOMPILE = "v1/internal/precompile";
  private static final String ENDPOINT_DIALOG = "v1/internal/dialog";
  private static final String ENDPOINT_GET_INFO = "v1/internal/info";
  private static final String ENDPOINT_LOCK = "v1/internal/locks";
  private static final String ENDPOINT_LEADER = "v1/internal/leader";
  private static final String ENDPOINT_UDF = "v1/udt/files/%s";
  private static final String ENDPOINT_CHECK_SCHEMA = "v1/schema/check";
  private static final String ENDPOINT_RECOVER_SCHEMA = "v1/schema/recover";
  private static final String ENDPOINT_CHANGE_PASSWORD = "v1/users";
  private static final String ENDPOINT_WLM_CONFIGS = "v1/workload-manager/configs";
  // register endpoints which don't need login
  private static final Set<String> NO_LOGIN_ENDPOINTS = new HashSet<>(
      Arrays.asList(ENDPOINT_VERSION, ENDPOINT_HELP, ENDPOINT_LOGIN));

  /** Maximum number of retry to connect to GSQL server. */
  private static final int RETRY_NUM_MAX = 5;

  // the headers for gsql cookie must be same in BaseHandler.java
  private static final String GSQL_COOKIE = "GSQL-Cookie";
  private static final String SET_GSQL_COOKIE = "Set-GSQL-Cookie";

  /**
   * GSQL client shell interactive with GSQL server mostly by /statements endpoint,
   * which is "text/plain" content type
   */
  private static final String TEXT_CONTENT_TYPE = "text/plain";
  private static final String FORM_CONTENT_TYPE = "application/x-www-form-urlencoded";
  // Default error message for invalid username or password
  private static final String INVALID_USERNAME_OR_PASSWORD = "Invalid username or password!";
  // Password already expired
  private static final String USER_PASSWORD_EXPIRED = "password expired";
  /******** Security Recommendations ********/
  /** No recommendations **/
  private static final int NO_RECOMMENDATIONS = 0x0;
  /** Header for recommendations feedback **/
  private static final String RECOMMENDATIONS_HEADER
      = "\nPlease take the following steps to improve your TG system security:\n";
  /** Default tigergraph user password is still being used **/
  private static final int CHANGE_DEFAULT_TG_PASSWORD = 0x1;
  /** Recommendation to change default tigergraph user password **/
  private static final String REC_CHANGE_DEFAULT_TG_PASSWORD
      = "    * Change default tigergraph user's password\n";
  /** Password expiration approaching**/
  private static final int PASSWORD_EXPIRATION_APPROACHING = 0x2;
  /** Change password recommendation because of password expiration approaching **/
  private static final String REC_PASSWORD_EXPIRATION_APPROACHING
          = "    * Your password will expire in %d days. Please change it promptly.\n";
  /****** End Security Recommendations ******/

  /**
   * {@code true} if using interactive shell; {@code false} otherwise.
   */
  private boolean isShell = false;

  /**
   * TODO remove it, all request from GUI should go through GSQL rest API
   * {@code true} if from GraphStudio; {@code false} otherwise.
   */
  private boolean isFromGraphStudio = false;

  // the Content-Type can be different for different request
  private String contentType = TEXT_CONTENT_TYPE;
  private String username = DEFAULT_USER;
  // = URLEncoder.encode(username)
  private String encodedUsername = null;
  private String password = DEFAULT_PASSWORD;
  private String graphName = null; // login graphname, optional at login
  private String userCookie = null;
  private String welcomeMessage = null;
  private String shellPrompt = null;
  private int securityRecommendations = NO_RECOMMENDATIONS;
  private RetryableHttpConnection retryableHttpConn;
  private String serverIP;
  private String clientIP;
  // client session idle timeout second
  private int sessionTimeoutSec = -1;

  /**
   * GSQL {@code Client} constructor
   *
   * @param cli the command line arguments
   * @param serverIP Server ip address
   */
  public Client(GsqlCli cli, String serverIP) {
    if (cli.hasUser()) {
      username = cli.getUser();
    }
    if (cli.hasPassword()) {
      password = cli.getPassword();
    }
    if (cli.hasSecret()) {
      username = SECRET_USER;
      password = cli.getSecret();
    }
    if (cli.hasToken()) {
      username = TOKEN_USER;
      password = cli.getToken();
    }
    if (cli.hasGraph()) {
      graphName = cli.getGraph();
    }
    this.serverIP = serverIP;
    this.clientIP = SystemUtils.getPublicIP();

    System.setProperty("LOG_FILE_NAME", String.format("log.%d", getUniqueIdentifier()));
  }

  /**
   * @param retryableHttpConn a retryable http connection instance
   */
  public void setRetryableHttpConn(RetryableHttpConnection retryableHttpConn) {
    this.retryableHttpConn = retryableHttpConn;
    retryableHttpConn.setMaxRetry(RETRY_NUM_MAX);
  }

  /**
   * Create GSQL CLI instance to parse the CLI arguments.
   *
   * @param cli {@code GsqlCli} with parsed client args
   */
  public void start(GsqlCli cli) throws Exception {
    if (retryableHttpConn == null) {
      throw new NullPointerException("retryableHttpConn is null");
    }

    // version and help actions
    if (cli.hasVersion()) {
      executeGet(ENDPOINT_VERSION, null);
      SystemUtils.exit(ExitStatus.OK);
    } else if (cli.hasDetailedVersion()) {
      executeGet(ENDPOINT_VERSION, Collections.singletonMap("verbose", Arrays.asList("true")));
      SystemUtils.exit(ExitStatus.OK);
    } else if (cli.hasHelp()) {
      executeGet(ENDPOINT_HELP, null);
      SystemUtils.exit(ExitStatus.OK);
    } else if (cli.hasLock()) {
      // check the current lock status, internal use only
      executeGet(ENDPOINT_LOCK, null);
      SystemUtils.exit(ExitStatus.OK);
    } else if (cli.hasPrecompile()) {
      // precompile header file
      executeGet(ENDPOINT_PRECOMPILE, null);
      SystemUtils.exit(ExitStatus.OK);
    } else if (cli.hasLeader()) {
      executeGet(ENDPOINT_LEADER, null);
      SystemUtils.exit(ExitStatus.OK);
    } else if (cli.hasResign()) {
      executeGet(ENDPOINT_LEADER, Collections.singletonMap("resign", Arrays.asList("true")));
      SystemUtils.exit(ExitStatus.OK);
    } else if (cli.hasCheckSchema()) {
      executePost(ENDPOINT_CHECK_SCHEMA, "checkschema");
      SystemUtils.exit(ExitStatus.OK);
    } else if (cli.hasRecoverSchema()) {
      executePost(ENDPOINT_RECOVER_SCHEMA, "recoverschema");
      SystemUtils.exit(ExitStatus.OK);
    } else if (cli.hasChangePassword()) {
      // If the target user is specified, set the new password for that user,
      // otherwise, use the login user
      contentType = "application/json";
      String name = cli.hasTargetuser() ? cli.getTargetUser() : username;
      JSONObject json = new JSONObject().put("password", cli.getNewPassword()).put("name", name);
      executePut(ENDPOINT_CHANGE_PASSWORD, json.toString());
      SystemUtils.exit(ExitStatus.OK);
    } else if (cli.hasTargetuser()) {
      SystemUtils.println("Error: Must specify changepassword option when setting targetuser.");
      SystemUtils.exit(ExitStatus.OK);
    }

    isFromGraphStudio = cli.isFromGraphStudio();

    // login with specified user
    login(cli);

    if (cli.hasReset()) {
      // Reset can be executed only after login.
      executeGet(ENDPOINT_RESET, Collections.singletonMap("printStream", Arrays.asList("true")));
    } else if (cli.hasFile() || cli.isReadingFile()) {
      // read from file, either -f option or file name provided right after gsql,
      // e.g. gsql a.gsql
      // collect filepath
      Set<String> filePaths = new HashSet<>();
      String inputFileContent = readFile(cli, filePaths);
      if (inputFileContent == null) return;
      // build data
      executePost(ENDPOINT_STATEMENTS, inputFileContent);
    } else if (cli.hasCommand()) {
      // read from linux shell, -c option provided right after gsql,
      // e.g. gsql -c ls
      executePost(ENDPOINT_STATEMENTS, cli.getCommand());
    } else if (!cli.getArgument().isEmpty()) {
      // no option provided and gsql command followed, e.g. gsql ls
      executePost(ENDPOINT_STATEMENTS, cli.getArgument());
    } else {
      // without anything, gsql shell is started.

      // check session timeout
      String timeout = System.getenv("GSQL_CLIENT_IDLE_TIMEOUT_SEC");
      if (timeout != null) {
        try {
          sessionTimeoutSec = Integer.parseInt(timeout);
          logger.info("idle timeout set to " + sessionTimeoutSec);
        } catch (NumberFormatException e) {
          logger.error(e);
        }
      }
      interactiveShell();
    }
  }

  /**
   * gsql -u $username
   * gsql -g poc_graph
   * or gsql with default username
   *
   * If it is default user, try with default password first.
   * If the default user does not exist, return and exit.
   * If the default password is wrong, ask the user to type in password.
   *
   * @param cli {@code GsqlCli} with parsed client args
   */
  public void login(GsqlCli cli) {
    JSONObject json = new JSONObject();

    // We allow user to retry login twice if unsuccessful password provided
    // If password is provided beforehand, we do not prompt user to retry.
    int retry = cli.hasPassword() ? 1 : 3;
    int count = 0;
    while (count < retry) {
      // for default user
      if (username.equals(DEFAULT_USER) && !cli.hasPassword()) {
        // keep the default password for retry
        password = DEFAULT_PASSWORD;
        json = executeAuth(ENDPOINT_LOGIN);
        if (json != null && json.optBoolean("error", true)) {
          // if password is changed, let user input password
          if (json.optString("message").contains(INVALID_USERNAME_OR_PASSWORD)) {
            password = ConsoleUtils.prompt4Password(false,
                    false, username,"user");
            json = executeAuth(ENDPOINT_LOGIN);
          }
        }
      } else {
        // other users
        if (!cli.hasSecret() && !cli.hasToken() && !cli.hasPassword()) {
          // If is not the default user, just ask the user to type in password.
          password = ConsoleUtils.prompt4Password(false,
                  false, username, "user");
        }
        json = executeAuth(ENDPOINT_LOGIN);
      }

      // determine failure type from json
      Boolean wrongPwd = false;
      Boolean inTimeThreshold = false;
      if (json.optString("message").contains(INVALID_USERNAME_OR_PASSWORD)) {
        wrongPwd = true;
      }

      if (json.optString("message").contains("Please re-login after")) {
        inTimeThreshold = true;
      }

      // If login successful we break out of retry loop.
      if (!wrongPwd && !inTimeThreshold) {
        break;
      }

      // Dont output try again prompt on last attempt. GSQL Server will handle the output to user.
      if (count < retry - 1) {
        SystemUtils.println(json.optString("message"));
      }
      count++;
    }
    handleLoginResponse(json);
  }

  /**
   * Handle response after login.
   *
   * @param json {@code JSONObject} of HTTP response.
   * @return false if incorrect password, this is to handle retry, o/w always return true.
   */
  private boolean handleLoginResponse(JSONObject json) {
    if (json != null) SystemUtils.printlnIfNonEmpty(json.optString("message"));
    if (json == null || json.optBoolean("error", true)) {
      if (json != null && json.optString("message").contains(USER_PASSWORD_EXPIRED)) {
        logger.error("%s: user password has expired", ExitStatus.PASSWORD_EXPIRED_ERROR.toString());
        SystemUtils.exit(ExitStatus.PASSWORD_EXPIRED_ERROR);
      } else {
        logger.error("%s: %s",
            ExitStatus.LOGIN_OR_AUTH_ERROR.toString(),
            json == null ? null : String.valueOf(json.optBoolean("error", true)));
        SystemUtils.exit(ExitStatus.LOGIN_OR_AUTH_ERROR);
      }
      return false;
    } else if (!json.has("isClientCompatible")) {
      // if server is outdated that doesn't have compatibility check logic,
      // isClientCompatible won't be available and message will be null so manually handle here
      logger.error("%s: Server is outdated", ExitStatus.LOGIN_OR_AUTH_ERROR.toString());
      SystemUtils.exit(ExitStatus.COMPATIBILITY_ERROR,
          "It's most likely your TigerGraph server has been upgraded.",
          "Please follow this document to obtain the corresponding GSQL client to the server:",
          "https://docs.tigergraph.com/dev/using-a-remote-gsql-client");
    } else if (!json.optBoolean("isClientCompatible", true)) {
      logger.error("%s: Client is incompatible", ExitStatus.LOGIN_OR_AUTH_ERROR.toString());
      SystemUtils.exit(ExitStatus.COMPATIBILITY_ERROR);
    }
    // if not caught by any error, retrieve welcomeMessage, shellPrompt, and serverPath
    welcomeMessage = json.getString("welcomeMessage");
    shellPrompt = json.getString("shellPrompt");
    securityRecommendations = json.getInt("securityRecommendations");
    if (!IS_LOCAL) {
      SystemUtils.println(
          "If there is any relative path, it is relative to <System.AppRoot>/dev/gdk/gsql");
    }
    return true;
  }

  // Pattern to match comments in a line.
  // Matches '//' or '#' unless '#' is escaped with a backslash ('\').
  // Example: `// Comment` or `# Comment` will be removed, but `file\#1.gsql` keeps the text.
  private static final Pattern COMMENT_PATTERN = Pattern.compile("//.*|(?<!\\\\)#.*");

  // Pattern to match a valid file path command.
  // The command must start with '@', followed by non-whitespace characters,
  // and must not end with ';' or ','. Example: `@queries.gsql` is valid.
  private static final Pattern FILE_NAME_CMD_PATTERN = Pattern.compile("^@[^\\s@]+[^;,]$");

  /**
   * Processes a line of input by removing comments and trimming whitespace.
   * Also converts any escaped '#' characters (i.e., '\#') to '#'.
   *
   * @param line the input line to process
   * @return the processed line with comments removed, whitespace trimmed,
   * and escaped '#' characters converted to '#'.
   */
  private String processLine(String line) {
    Matcher commentMatcher = COMMENT_PATTERN.matcher(line);
    return commentMatcher.replaceAll("").trim().replace("\\#", "#");
  }

  /**
   * Checks if the given line matches the file command pattern, which is
   * used to identify commands that point to a file path (e.g., "@file_path").
   *
   * @param line the line to check
   * @return true if the line matches the file command pattern, false otherwise
   */
  private boolean isFileCommand(String line) {
    Matcher commandMatcher = FILE_NAME_CMD_PATTERN.matcher(line);
    return commandMatcher.matches();
  }

  /**
   * Read files based on {@code cli}.
   *
   * @param cli {@code GsqlCli} with parsed client args
   * @return Contents in {@code fileName}, null if file does not exist
   */
  public String readFile(GsqlCli cli, Set<String> filePathSet) throws Exception {
    String fileName = null;
    String fileValue = cli.getFile();
    if (fileValue == null || fileValue.isEmpty()) {
      fileName = cli.getArgument();
    } else {
      fileName = fileValue;
    }
    // collect filepath param for local gsql client
    return readFile(fileName, filePathSet);
  }

  /**
   * Recursively read the file of {@code fileName}.
   * Keep {@code fileNameSet} to prevent endless loop.
   *
   * @param filePath File name to read
   * @param filePathSet File names which have been read so far
   * @return Contents in {@code fileName}, null if file does not exist
   */
  public String readFile(String filePath, Set<String> filePathSet) throws Exception {
    File file = new File(filePath);
    // Convert file path to absolute and normalized path
    filePath = file.getCanonicalPath();
    if (filePath == null || filePath.isEmpty() || !file.isFile()) {
      String errMsgFmt = "File \"%s\" does not exist!";
      logger.error(errMsgFmt, filePath);
      SystemUtils.println(errMsgFmt, filePath);
      return null;
    }

    if (filePathSet.contains(filePath)) {
      String errMsgFmt = "There is an endless loop by using @%s cmd recursively.";
      logger.error(errMsgFmt, filePath);
      SystemUtils.exit(ExitStatus.RUNTIME_ERROR, errMsgFmt, filePath);
    } else {
      // Collect absolute normalized path
      filePathSet.add(filePath);
    }

    StringBuilder inputFileContent = new StringBuilder();
    try (Scanner sc = new Scanner(file)) {
      while (sc.hasNextLine()) {
        String line = sc.nextLine();
        // Remove comments and check if the line matches the "@file_path" pattern
        String processedLine = processLine(line);
        if (isFileCommand(processedLine)) {
          String fileContent = readFile(processedLine.substring(1), filePathSet);
          if (fileContent == null) return null;
          inputFileContent.append(fileContent).append("\n");
        } else {
          inputFileContent.append(line).append("\n");
        }
      }
    }
    return inputFileContent.toString();
  }

  /**
   * GSQL interactive shell.
   */
  public void interactiveShell() {
    isShell = true;

    if (!isFromGraphStudio) {
      printSecurityRecommendations();
      System.out.println(welcomeMessage);
    }

    // set the console
    String prompt = "\u001B[1;34m" + shellPrompt + "\u001B[0m";

    ConsoleReader console = null;
    try {
      console = new ConsoleReader(
          null, new FileInputStream(FileDescriptor.in), System.out, null, "UTF-8");
    } catch (IOException e) {
      logger.error(e);
      SystemUtils.exit(ExitStatus.UNKNOWN_ERROR, e);
    }
    console.setPrompt(prompt);

    // get auto-complete keys, set auto-completer
    try {
      String keys = getInfo(ENDPOINT_GET_INFO,
          Collections.singletonMap("type", Arrays.asList("auto-keys")));
      if (keys != null) {
        console.addCompleter(new AutoCompleter(keys.split(",")));
      }
    } catch (Exception e) {
      logger.error(e);
    }

    console.addCompleter(new ArgumentCompleter(new FileNameCompleter()));
    // disable bash function
    console.setExpandEvents(false);

    Path cmdHistory = Paths.get(
        System.getProperty("LOG_DIR"),
        String.format("history.%d", getUniqueIdentifier()));
    try {
      // create history file
      try {
        Files.createFile(cmdHistory);
      } catch (FileAlreadyExistsException ignored) {
        // ignore this exception and history will be aggregated in the existing file
      }
      // Windows don’t support Posix file permission
      if (!System.getProperty("os.name").startsWith("Windows")) {
        // change permission to 644
        Files.setPosixFilePermissions(cmdHistory, PosixFilePermissions.fromString("rw-r--r--"));
      }
      // bind history file to console
      History cmdHistoryFile = new FileHistory(cmdHistory.toFile());
      console.setHistory(cmdHistoryFile);
      console.setHistoryEnabled(true);

      // Add a shutdown hook, for example, when Ctrl + C, flush the history log.
      final ConsoleReader finalConsole = console;
      Runtime.getRuntime().addShutdownHook(new Thread() {
        public void run() {
          try {
            ((FileHistory) finalConsole.getHistory()).flush();
          } catch (IOException e) {
            logger.error(e);
          }
        }
      });
    } catch (IOException e) {
      console.setHistoryEnabled(false);
      logger.error(e);
      SystemUtils.println(
          "History file %s cannot be created! Thus, shell command history will not be logged.",
          cmdHistory);
    }

    while (true) {

      Timer timer = getTimer();
      try {
        String inputCommand = console.readLine();
        if (inputCommand == null) {
          SystemUtils.exit(ExitStatus.OK);
        } else {
          inputCommand = inputCommand.trim();
        }

        // reset timer
        if (timer != null) {
          timer.cancel();
        }

        if (inputCommand.isEmpty()) {
          continue;
        }

        // exit command
        if (inputCommand.equalsIgnoreCase("exit") || inputCommand.equalsIgnoreCase("quit")) {
          SystemUtils.exit(ExitStatus.OK);
        }

        // begin ... end block
        if (inputCommand.equalsIgnoreCase("begin")) {
          String InputBlock = "";
          String subInputCommand = console.readLine();
          if (subInputCommand == null) {
            SystemUtils.exit(ExitStatus.OK);
          }
          while (!subInputCommand.equalsIgnoreCase("end")
              && !subInputCommand.equalsIgnoreCase("abort")) {
            InputBlock += subInputCommand + "\n";
            subInputCommand = console.readLine();
          }
          if (subInputCommand.equalsIgnoreCase("abort")) {
            inputCommand = "";
          } else {
            inputCommand = InputBlock;
          }
        }
        // Remove comments and check if the line matches the "@file_path" pattern
        String processedCommand = processLine(inputCommand);
        if (isFileCommand(processedCommand)) {
          String commandFileName = processedCommand.substring(1);
          try {
            // collect filepath param for local gsql client
            Set<String> filePaths = new HashSet<>();
            inputCommand = readFile(commandFileName, filePaths);
            // file does not exist, error has been reported
            if (inputCommand == null) continue;
            // build data
            executePost(ENDPOINT_STATEMENTS, inputCommand);
            continue;
          } catch (Exception e) {
            logger.error(e);
            SystemUtils.println("Can't read file %s", commandFileName);
          }
        }
        executePost(ENDPOINT_STATEMENTS, inputCommand);
      } catch (IOException e) {
        logger.error(e);
        // reset timer
        if (timer != null) {
          timer.cancel();
        }
      }
    }
  }

  /**
   * Login to {@code endpoint}.
   *
   * @param endpoint Endpoint URL to connect
   * @return Server response in {@code JSONObject}
   */
  public JSONObject executeAuth(String endpoint) {
    HttpURLConnection conn = null;
    JSONObject json = null;
    try {
      logger.info("executeAuth: url: %s", endpoint);
      // basic auth will become the message in case of login
      String msg = getBasicAuth(true);

      logger.info("Send payload: " + msg);
      // open connection
      conn = createConnection(endpoint, msg, "POST", null);

      // handle response
      int code = conn.getResponseCode();
      Scanner sc ;
      if ((code / 200) == 1) {
        // when code 2xx, should use getInputStream read data
        sc = new Scanner(conn.getInputStream());
      } else {
        // when code 4xx, 5xx should use getErrorStream read data
        // actually when call login api, server will return 401 if the password expired
        // we need report this error.
        sc = new Scanner(conn.getErrorStream());
      }
      String result = "";
      while (sc.hasNextLine()) {
        result += sc.nextLine();
      }
      sc.close();
      logger.info("Get response: " + result);
      json = new JSONObject(result);

      List<String> cookies = conn.getHeaderFields().get(SET_GSQL_COOKIE);
      if (cookies != null && cookies.size() > 0) {
        userCookie = cookies.get(0);
        logger.setSession(username, retryableHttpConn.getCurrentURI());
        logger.info("Session has been established with cookie %s",
            GsqlClientLogger.maskArgsInCommand(userCookie));
      }
    } catch (ConnectException e) {
      logger.error(e);
      reportConnectExceptionAndExit(conn, false);
    } catch (Exception e) {
      json = null; // reset json if there's an exception
      logger.error(e);
      System.out.println(e.getMessage());
      System.out.println(
          "If SSL/TLS is enabled for TigerGraph, please use -cacert with a certificate file, "
          + "check TigerGraph document for details.");
    } finally {
      disconnectSafely(conn);
    }
    return json;
  }

  /**
   * Send {@code msg} to {@code endpoint}, and return information.
   * Caller is responsible to parse the output.
   *
   * @param endpoint Endpoint URL to connect
   * @param param parameter
   * @return Info in {@code String}
   */
  public String getInfo(String endpoint, Map<String, List<String>> param) {
    StringBuilder retLine = new StringBuilder();
    sendHttpRequest(endpoint, param, null, "GET", false, response -> {
      // get information from response
      Scanner sc = new Scanner(response);
      if (sc.hasNextLine()) {
        retLine.append(sc.nextLine());
      }
      sc.close();
    });
    return retLine.toString();
  }

  /**
   * Send a http request to gsql-server, and apply the operator {@code op} with response data.
   * @param endpoint
   * @param param the URL parameters
   * @param data
   * @param method
   * @param inShutdownHook true if this is called within shutdown hook, we cannot run exit() within
   *        a shutdown hook, otherwise the process will get stuck indefinitely
   * @param op the operator to apply for response data from gsql-server
   * @return true if connection success
   */
  private boolean sendHttpRequest(
      String endpoint,
      Map<String, List<String>> param,
      String data,
      String method,
      boolean inShutdownHook,
      HttpResponseOperator op) {
    HttpURLConnection conn = null;
    try {
      // open connection
      conn = createConnection(endpoint, data, method, param);
      int retCode = conn.getResponseCode();
      if (op == null) {
        if ((retCode / 100) != 2) {
          String error_msg = String.format("Endpoint: %s, Response Code: %d, Message: %s",
              endpoint, retCode, conn.getResponseMessage());
          logger.error(error_msg);
          // for debug purpose
          System.out.println(error_msg);
        }
      } else {
        try {
          op.apply(conn.getInputStream());
        } catch (Exception e) {
          // in case of non-2xx response, input stream wouldn't be available
          // so process error stream instead
          op.apply(conn.getErrorStream());
        }
      }
      return true;
    } catch (ConnectException e) {
      logger.error(e);
      reportConnectExceptionAndExit(conn, inShutdownHook);
    } catch (Exception e) {
      logger.error(e);
    } finally {
      disconnectSafely(conn);
    }
    return false;
  }

  /**
   * Send {@code msg} to {@code endpoint}, without handling response.
   *
   * @param endpoint Endpoint URL to connect
   * @param msg      Message to send
   */
  public void sendPost(String endpoint, String msg) {
    sendHttpRequest(endpoint, null, msg, "POST", false, null);
  }

  public void executeGet(String endpoint, Map<String, List<String>> param) {
    executeRequest(endpoint, param, null, "GET");
  }

  public void executePost(String endpoint, String msg) {
    executeRequest(endpoint, null, msg, "POST");
  }

  public void executePut(String endpoint, String msg) {
    executeRequest(endpoint, null, msg, "PUT");
  }

  /**
   * Send {@code msg} with {@code param} to {@code endpoint}, and handle response.
   *
   * @param endpoint Endpoint URL to connect
   * @param param    Parameters to send
   * @param msg      Message to send
   * @param method   HTTP method
   */
  private void executeRequest(String endpoint,
                              Map<String, List<String>> param,
                              String msg,
                              String method) {
    HttpResponseOperator op = response -> {
      // print out response
      Scanner sc = new Scanner(response);
      String line;
      String cursorMoveup = "__GSQL__MOVE__CURSOR___UP__";
      String cleanLine = "__GSQL__CLEAN__LINE__";
      String progressBarPattern = "\\[=*\\s*\\]\\s[0-9]+%.*";
      String progressBarCompletePattern = "\\[=*\\s*\\]\\s100%[^l]*";

      while (sc.hasNextLine()) {
        line = sc.nextLine();
        if (line.startsWith("__GSQL__RETURN__CODE__")) {
          if (!isShell) {
            String[] words = line.split(",", 2);
            try {
              System.exit(Integer.valueOf(words[1]));
            } catch (NumberFormatException e) {
              String errMsgFmt = "Can't parse return code: %s";
              logger.error(errMsgFmt, words[1]);
              SystemUtils.exit(ExitStatus.UNKNOWN_ERROR, errMsgFmt, words[1]);
            }
          }
        } else if (line.startsWith("__GSQL__INTERACT__")) {
          // request an interaction with the user
          dialogBox(line);
        } else if (line.startsWith("__GSQL__COOKIES__")) {
          String[] words = line.split(",", 2);
          userCookie = words[1];
          logger.info("userCookie set to: " + GsqlClientLogger.maskArgsInCommand(userCookie));
        } else if (line.startsWith(cursorMoveup)) {
          String[] tokens = line.split(",");
          // print a progress bar
          System.out.print("\u001b[" + tokens[1] + "A"); // move up tokens[1] lines
        } else if (line.startsWith(cleanLine)) {
          // print a progress bar
          System.out.print("\u001b[" + "2K"); // clean the entire current line
        } else if (line.matches(progressBarPattern)) {
          // print a progress bar
          if (line.matches(progressBarCompletePattern)) {
            line += "\n";
          }
          System.out.print("\r" + line);
        } else {
          System.out.println(line);
        }
      }
      sc.close();
    };

    sendHttpRequest(endpoint, param, msg, method, false, op);
  }

  /**
   * Handle server response which requires user interaction in dialog.
   *
   * @param input Server response
   */
  public void dialogBox(String input) {
    String[] inputs = input.split(",", 4);
    String qb = inputs[1];
    String dialogId = inputs[2];
    switch (qb) {
      case "DecryptQb":
        try {
          ConsoleReader console = new ConsoleReader();
          String pass = console.readLine((char) 0);
          sendPost(
              ENDPOINT_DIALOG,
              URLEncoder.encode(dialogId + "," + pass, "UTF-8"));
        } catch (IOException e) {
          logger.error(e);
        }
        break;

      case "AlterPasswordQb":
      case "ExportGraphQb":
      case "ImportGraphQb":
        try {
          String pass = ConsoleUtils.prompt4Password(true,
                  true, null, "user");
          sendPost(
              ENDPOINT_DIALOG,
              URLEncoder.encode(dialogId + "," + pass, "UTF-8"));
        } catch (UnsupportedEncodingException e1) {
          logger.error(e1);
        }
        break;

      case "CreateUserQb":
        try {
          String name = ConsoleUtils.prompt4UserName();
          String pass = ConsoleUtils.prompt4Password(true,
                  true, name, "user");
          name = URLEncoder.encode(name, "UTF-8");
          sendPost(
              ENDPOINT_DIALOG,
              URLEncoder.encode(dialogId + "," + name + ":" + pass, "UTF-8"));
        } catch (UnsupportedEncodingException e1) {
          logger.error(e1);
        }
        break;

      case "ClearStoreQb":
        try {
          ConsoleReader console = new ConsoleReader();
          console.setPrompt(
              "Clear store, are you sure? "
              + "This will also shutdown all GSQL services. [y/N]:");
          String command = console.readLine().trim().toLowerCase();
          if (!command.equals("y")) {
            command = "n";
          }
          sendPost(
              ENDPOINT_DIALOG,
              URLEncoder.encode(dialogId + "," + command, "UTF-8"));
        } catch (IOException e) {
          logger.error(e);
        }
        break;
      case "DeleteConnectorQb":
        try {
          ConsoleReader console = new ConsoleReader();
          console.setPrompt(
              "Deleting the connector will delete all data in connector topic "
              + "and the connector offsets, continue to delete? (y/N)");
          String command = console.readLine().trim().toLowerCase();
          if (!command.equals("y")) {
            command = "n";
          }
          sendPost(
              ENDPOINT_DIALOG,
              URLEncoder.encode(dialogId + "," + command, "UTF-8"));
        } catch (IOException e) {
          logger.error(e);
        }
        break;
      case "DropDataSourceQb":
        try {
          ConsoleReader console = new ConsoleReader();
          console.setPrompt("Are you sure to drop data source (" + inputs[3] + ")? [y/N]:");
          String command = console.readLine().trim().toLowerCase();
          if (!command.equals("y")) {
            command = "n";
          }
          sendPost(
              ENDPOINT_DIALOG,
              URLEncoder.encode(dialogId + "," + command, "UTF-8"));
        } catch (IOException e) {
          logger.error(e);
        }
        break;

      case "PutFileQb": {
        String filename = inputs[2];
        String path = inputs[3];
        // prepare params
        Map<String, List<String>> params = new HashMap<>();
        if (IS_LOCAL) {
          // need to check file input policy for local gsql client
          // Notice this "path" must be absolute path
          params.put("filepath", Arrays.asList(path));
        }
        StringBuilder errMsg = new StringBuilder();
        String content = null;
        path = checkAndUpdateFilePath(filename, path, errMsg);
        if (path != null) {
          content = IOUtils.get().readFromFile(path);
        }
        // Even if filename or path is invalid, we still send request to server
        // so that server can be aware of that user tried to PUT file before
        // and reflect this user operation in audit log.
        if (content == null) {
          if (errMsg.toString().isEmpty()) {
            errMsg.append("file ").append(path).append(" not exists!");
          }
          System.out.println(errMsg);
          params.put("errorMessage", Collections.singletonList(errMsg.toString()));
        }
        String endpoint = String.format(ENDPOINT_UDF, filename);
        sendHttpRequest(endpoint, params, content, "PUT", false, response -> {
          StringBuilder retLine = new StringBuilder();
          // get information from response
          Scanner sc = new Scanner(response);
          if (sc.hasNextLine()) {
            retLine.append(sc.nextLine() + "\n");
          }
          sc.close();
          JSONObject json = new JSONObject(retLine.toString());
          if (json.has("message")) {
            System.out.println(json.getString("message"));
          }
        });

        break;
      }

      case "GetFileQb": {
        String filename = inputs[2];
        String path = inputs[3];
        Map<String, List<String>> params = new HashMap<>();
        StringBuilder errMsg = new StringBuilder();
        String updatedPath = checkAndUpdateFilePath(filename, path, errMsg);
        // Even if filename or path is invalid, we still send request to server
        // so that server can be aware of that user tried to GET file before
        // and reflect this user operation in audit log.
        if (updatedPath == null) {
          params.put("errorMessage", Collections.singletonList(errMsg.toString()));
        }
        String endpoint = String.format(ENDPOINT_UDF, filename);
        sendHttpRequest(endpoint, params, null, "GET", false, response -> {
          StringBuilder retLine = new StringBuilder();
          // get information from response
          Scanner sc = new Scanner(response);
          if (sc.hasNextLine()) {
            retLine.append(sc.nextLine() + "\n");
          }
          sc.close();
          JSONObject json = new JSONObject(retLine.toString());
          if (json.getBoolean("error")) {
            System.out.println(json.getString("message"));
            System.out.println("GET " + filename + " failed.");
          } else {
            JSONObject result = json.getJSONObject("results");
            if (result.has(filename)) {
              boolean tryWriteToFile = IOUtils.get().writeToFile(
                  updatedPath, result.getString(filename));
              if (tryWriteToFile) {
                System.out.println("GET " + filename + " successfully.");
              }
            } else {
              System.out.println("GET " + filename + " failed.");
            }
          }
        });
        break;
      }

      case "PutWorkloadQueueQb": {
        Map<String, List<String>> params = new HashMap<>();
        String payload = getJsonStrFromPath(inputs[2]);
        if (payload == null) {
          String errMsg = "Failed to update workload queue configs.";
          params.put("errorMessage", Collections.singletonList(errMsg));
        }
        contentType = "application/json";
        sendHttpRequest(ENDPOINT_WLM_CONFIGS, params, payload, "POST", false,
            response -> {
              StringBuilder retLine = new StringBuilder();
              // get information from response
              Scanner sc = new Scanner(response);
              if (sc.hasNextLine()) {
                retLine.append(sc.nextLine()).append("\n");
              }
              sc.close();
              JSONObject json = new JSONObject(retLine.toString());
              if (json.has("message")) {
                System.out.println(json.getString("message"));
              }
              if (json.has("error") && json.getBoolean("error")) {
                System.out.println("Failed to update workload queue configs.");
              }
            });
        break;
      }

      case "GetWorkloadQueueQb": {
        Path path = Path.of(inputs[2]);
        Map<String, List<String>> params = new HashMap<>();
        if (Files.exists(path)) {
          String errMsg = path + " already exists. Please specify a path to the new file.";
          System.out.println(errMsg);
          params.put("errorMessage", Collections.singletonList(errMsg));
        }
        sendHttpRequest(
            ENDPOINT_WLM_CONFIGS, params, null, "GET", false, response -> {
              StringBuilder retLine = new StringBuilder();
              // get information from response
              Scanner sc = new Scanner(response);
              if (sc.hasNextLine()) {
                retLine.append(sc.nextLine()).append("\n");
              }
              sc.close();
              JSONObject json = new JSONObject(retLine.toString());
              if (json.has("error") && json.getBoolean("error")) {
                if (json.has("message")) {
                  System.out.println(json.getString("message"));
                }
                System.out.println("Failed to retrieve workload queue configs.");
              } else {
                boolean isWritten = IOUtils.get().writeToFile(path.toString(), retLine.toString());
                if (isWritten) {
                  System.out.println("Successfully retrieved workload queue configs.");
                }
              }
            });

        break;
      }

      case "TestModeWaitForContinue": {
        try {
          ConsoleReader console = new ConsoleReader();
          console.setPrompt(
              "Test mode, please press enter to continue ...");
          // don't care about the input
          console.readLine();
          sendPost(
              ENDPOINT_DIALOG,
              URLEncoder.encode(dialogId + ",y", "UTF-8"));

        } catch (IOException e) {
          logger.error(e);
        }

        break;
      }

      case "RunSchemaChangeJobQb":
      case "RunGlobalSchemaChangeJobQb":
        try {
          ConsoleReader console = new ConsoleReader();
          console.setPrompt("Do you want to abort these loading jobs to proceed "
              + "the schema change? (Y/n):");
          String command = console.readLine().trim().toLowerCase();
          if (command.isEmpty()
              || (!command.equals("y") && !command.equals("yes"))) {
            command = "n";
          }
          sendPost(
              ENDPOINT_DIALOG,
              URLEncoder.encode(dialogId + "," + command, "UTF-8"));
        } catch (IOException e) {
          logger.error(e);
        }
        break;

      default:
        String errMsgFmt = "Undefined action %s";
        logger.error(errMsgFmt, qb);
        SystemUtils.exit(ExitStatus.RUNTIME_ERROR, errMsgFmt, qb);
    }
  }

  /**
   * Get the correct file extension for user-define file, including:
   * TokenBank.cpp, [tg_]ExprFunctions.hpp, [tg_]ExprUtil.hpp
   * @param filename
   * @return the corresponding file extension, eg ".hpp"
   */
  private String getUDFFileExtension(String filename) {
    switch (filename) {
      case "TokenBank":
        return ".cpp";
      case "tg_ExprFunctions":
      case "tg_ExprUtil":
      case "ExprFunctions":
      case "ExprUtil":
        return ".hpp";
      default:
        // should not hit here
        throw new RuntimeException("System Error! Unknown user-define file type: " + filename);
    }
  }
  /**
   * Check and update the file path for user-defined file
   *   1. if a directory is given as end of path, append a file name
   *   2. if a filename is given, check if the file extension is correct
   *   3. convert to normalized, absolute path
   * @param filename could be TokenBank, ExprFunctions, ExprUtil, tg_ExprFunctions, or tg_ExprUtil
   * @param path the given path by user
   * @param errMsg error message for log purpose
   * @return the updated path, or null if it's invalid
   */
  private String checkAndUpdateFilePath(String filename, String path, StringBuilder errMsg) {
    if (path == null || path.isEmpty()) {
      System.out.println("Path cannot be empty!");
      errMsg.append("Path cannot be empty!");
      return null;
    }
    Path pathObj = Paths.get(path).normalize().toAbsolutePath();
    if (Files.isDirectory(pathObj)) {
      // use a folder here, append the file name
      String fullFileName = filename + getUDFFileExtension(filename);
      pathObj = pathObj.resolve(fullFileName);
      System.out.println("Path does not include file name. Append it to the path: '" + path + "'.");
    } else {
      // use a regular file name, check its extension
      String udfName = pathObj.getFileName().toString();
      String expectedFileExt = getUDFFileExtension(filename);
      if (!udfName.endsWith(expectedFileExt)) {
        String msg = String.format(
            "When call PUT/GET on '%s', must use '%s' as file extension in the path.",
            filename,
            expectedFileExt);
        System.out.println(msg);
        errMsg.append(msg);
        return null;
      }
    }
    return pathObj.toString();
  }

  private String getJsonStrFromPath(String input) {
    // check if it's a valid path, and use input as body otherwise
    String body = null;
    Path path = Path.of(input);
    if (Files.exists(path)) {
      if (!Files.isRegularFile(path)) {
        System.out.println("Please provide the path to a regular file or JSON.");
        return null;
      }
      body = IOUtils.get().readFromFile(input);
    } else {
      System.out.format("File '%s' is not found.", input);
      return null;
    }
    // check if body is valid json
    try {
      new JSONObject(body);
    } catch (JSONException e) {
      System.out.println("Invalid JSON is given. Please make sure it's not malformed.");
      return null;
    }
    return body;
  }

  /**
   * Open a {@code HttpURLConnection} based on {@code endpoint} and set necessary properties.
   *
   * @param endpoint Endpoint URL
   * @param msg HTTP message body
   * @param method the HttpConnection method
   * @param param the URL parameters
   * @return {@code HttpURLConnection}
   * @throws ConnectException if the connection is not created successfully
   */
  private HttpURLConnection createConnection(String endpoint, String msg,
      String method, Map<String, List<String>> param) throws ConnectException {
    try {

      Map<String, String> headers = new HashMap<>();
      headers.put("Content-Type", String.format("%s; charset=UTF-8", contentType));
      // set it back to default content-type;
      contentType = TEXT_CONTENT_TYPE;
      headers.put("Content-Language", "en-US");
      headers.put(GSQL_COOKIE, generateCookies());
      if (clientIP != null && !clientIP.isEmpty()) {
        headers.put("Client-IP", clientIP);
      }
      byte[] bytes = null;
      if (msg != null) {
        bytes = msg.getBytes(UTF_8);
        headers.put("Content-Length", Integer.toString(bytes.length));
      }
      // skip Authorization if this connection doesn't need login
      if (!NO_LOGIN_ENDPOINTS.contains(endpoint)) {
        headers.put("Authorization", getBasicAuth(false));
      }
      // Add operating system's username to header
      headers.put("OS-Username", System.getProperty("user.name"));

      return retryableHttpConn.connect(endpoint, param, method, bytes, headers);
    } catch (Exception e) {
      // log exception here; print out error message in caller
      logger.error(e);
      throw new ConnectException();
    }
  }

  /**
   * Generate cookies.
   *
   * @return Serialized {@cod JSONObject}
   */
  private String generateCookies() {
    if (userCookie != null && isJSONValid(userCookie)) {
      // return the cookie got from last connection
      return userCookie;
    }

    // generate new cookies
    JSONObject cookieJSON = new JSONObject();

    if (IS_LOCAL) {
      cookieJSON.put("clientPath", System.getProperty("user.dir"));
    }

    // Read the environment variable GSHELL_TEST and set the test flag for the session.
    String gShellTest = System.getenv("GSHELL_TEST");
    if (gShellTest != null && !gShellTest.isEmpty()) {
      cookieJSON.put("gShellTest", gShellTest);
    }

    try {
      cookieJSON.put("compileThread", Integer.valueOf(System.getenv("GSQL_COMPILE_THREADS")));
    } catch (Exception e) {
      // ignore if it is not an integer
    }
    cookieJSON.put("terminalWidth", ConsoleUtils.getConsoleWidth());
    cookieJSON.put("fromGsqlClient", true); // used in handler

    if (graphName != null) {
      cookieJSON.put("graph", graphName);
    }

    // Add to cookie if caller is from Graph Studio
    if (isFromGraphStudio) {
      cookieJSON.put("fromGraphStudio", true);
    }

    // get client version to check compatibility
    String clientVersion = getClientVersion();
    if (clientVersion != null) {
      cookieJSON.put("clientVersion", clientVersion);
    }

    if (userCookie != null) {
      userCookie = userCookie + "; GSQLCookie=" + cookieJSON.toString();
      logger.info("Generate new GSQL-Cookie: %s", GsqlClientLogger.maskArgsInCommand(userCookie));
      return userCookie;
    }
    logger.info("Generate new GSQL-Cookie: %s",
        GsqlClientLogger.maskArgsInCommand(cookieJSON.toString()));
    return cookieJSON.toString();
  }

  private boolean isJSONValid(String test) {
    try {
      new JSONObject(test);
    } catch (JSONException ex) {
      return false;
    }
    return true;
  }

  /**
   * Get client version
   *
   * @return client version
   */
  private String getClientVersion() {
    String clientVersion = null;
    try {
      Properties props = new Properties();
      InputStream in = Client.class.getClassLoader().getResourceAsStream("Version.prop");
      props.load(in);
      in.close();
      clientVersion = props.getProperty("version");
    } catch (Exception e) {
      logger.error(e);
      SystemUtils.println("Can't find Version.prop.");
    }

    return clientVersion;
  }

  /**
   * Build basic auth for {@code HttpURLConnection}.
   *
   * @param isLogin {@code true} if this auth is for login; {@code false} otherwise
   * @return Basic auth in {@code String}
   */
  private String getBasicAuth(boolean isLogin) {
    if (encodedUsername == null) {
      try {
        encodedUsername = URLEncoder.encode(username, "UTF-8");
      } catch (UnsupportedEncodingException ignored) {
        // ignore it
      }
    }

    String auth = String.join(":", encodedUsername, password);
    String auth64 = Base64.getEncoder().encodeToString(auth.getBytes(UTF_8));
    // When logging in, we don't need to put the prfix "Basic"
    return isLogin ? auth64 : String.join(" ", "Basic", auth64);
  }

  /**
   * Disconnect {@code conn} if exists.
   *
   * @param conn {@code HttpURLConnection}
   */
  private void disconnectSafely(HttpURLConnection conn) {
    if (conn != null) {
      conn.disconnect();
    }
  }

  /**
   * Print out error message when there is an issue in {@code conn}, and then exit.
   *
   * @param conn {@code HttpURLConnection} to disconnect
   */
  private void reportConnectExceptionAndExit(HttpURLConnection conn, boolean inShutdownHook) {
    String errMsg = String.join("\n",
        "Connection refused.",
        "Please check the status of GSQL server using \"gadmin status gsql\".",
        "If it's down, please start it on server first by \"gadmin start gsql\".",
        "If you are on a client machine and haven't configured the GSQL server IP address yet,",
        "please create a file called gsql_server_ip_config in the same directory as",
        "gsql_client.jar, containing one item: the GSQL server IP, e.g. ***********",
        "Please also make sure the versions of the client and the server are the same.\n");

    if (inShutdownHook) {
      // can only call halt() instead of exit() within a shutdown hook
      Runtime.getRuntime().halt(ExitStatus.CONNECTION_ERROR.getCode());
    } else {
      // normal exit
      SystemUtils.exit(ExitStatus.CONNECTION_ERROR, errMsg);
    }
  }

  /**
   * Generate unique identifier based on {@code username} and @{code baseEndpoint}
   * to be used with log/history file name.
   *
   * @return
   */
  private int getUniqueIdentifier() {
    int hash = serverIP != null ? serverIP.hashCode() : 0;
    return Math.abs((username + "." + hash).hashCode()) % 1000000;
  }

  private Timer getTimer() {
    if (sessionTimeoutSec == -1) {
      // no timeout
      return null;
    }

    // schedule the timer
    TimerTask task = new TimerTask() {
      public void run() {
        logger.info("Session timeout");
        SystemUtils.exit(ExitStatus.SESSION_TIMEOUT,
            "Session timeout after %d seconds idle.", sessionTimeoutSec);
      }
    };
    Timer timer = new Timer();
    timer.schedule(task, sessionTimeoutSec * 1000);
    return timer;
  }

  /**
   * LoginHandler will construct a number of security recommendations that will be given as feedback
   * here to the command
   */
  private void printSecurityRecommendations() {
    // Prepare feedback if there are any recommendations
    if (securityRecommendations != NO_RECOMMENDATIONS) {
      StringBuilder recOutput = new StringBuilder();
      // Create the header for recommendations
      recOutput.append(RECOMMENDATIONS_HEADER);

      // 1. Default Tigergraph user password check
      if (checkSecurityRecommendation(CHANGE_DEFAULT_TG_PASSWORD)) {
        recOutput.append(REC_CHANGE_DEFAULT_TG_PASSWORD);
      }

      /*
       * 2. Change password recommendation for approaching password expiration
       *    - The password expiration approaching flag is represented by bit 2.
       *    - The remaining days for password expiration are represented by bits 3-5.
       *
       *    For example, if the password will expire in 3 days,
       *    the securityRecommendations value will be 01110.
       *    The bit at 2 is 1, indicating that the password expiration is approaching.
       *    The bits between 3-5 are 011, which represents 3 days.
       */

      if (checkSecurityRecommendation(PASSWORD_EXPIRATION_APPROACHING)) {
        recOutput.append(String.format(REC_PASSWORD_EXPIRATION_APPROACHING,
                (securityRecommendations >> 2) & 0b111));
      }

      System.out.println(recOutput);
    }
  }

  private boolean checkSecurityRecommendation(int recommendation) {
    return (securityRecommendations & recommendation) == recommendation;
  }
}
