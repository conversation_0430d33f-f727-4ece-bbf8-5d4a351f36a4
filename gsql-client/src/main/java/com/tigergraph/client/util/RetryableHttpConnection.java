package com.tigergraph.client.util;

import static com.tigergraph.client.util.SystemUtils.logger;

import java.io.*;
import java.net.*;
import java.security.KeyStore;
import java.security.cert.Certificate;
import java.security.cert.CertificateException;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import javax.net.ssl.*;

import org.apache.http.client.utils.URIBuilder;

/**
 * A RetryableHttpConnection that takes a list of IPs and ports,
 * tries to set up connection to one of them, and retry if it can't
 * connect successfully.
 * It will try each ip/port up to {@code maxRetry} times.
 */
public class RetryableHttpConnection {

  private static final String LOCAL_HOST = "127.0.0.1";
  private static final int DEFAULT_CONNECTION_TIMEOUT = 300000; // 5 min
  // Interval (seconds) of retry to connect to GSQL server.
  private static final int RETRY_INTERVAL = 1;
  private List<String> ips;
  private List<Integer> ports;
  // max retry per ip/port
  private int maxRetry = 1;
  // rotation index for ip/port, so we can use the one succeed last time
  // to save some time.
  private int index = 0;
  // for SSL
  private SSLContext sslContext = null;
  // default port
  private int defaultPrivatePort = 8123;

  // Path to CA Certificate. {@code null} if not given.
  public RetryableHttpConnection(int defaultPort) throws Exception {
    ips = new ArrayList<>();
    ports = new ArrayList<>();
    defaultPrivatePort = defaultPort;
  }

  public void setMaxRetry(int maxRetry) {
    this.maxRetry = maxRetry;
  }

  @Override
  public int hashCode() {
    return ips.hashCode();
  }

  public void addIpPort(String ip, int port) {
    try {
      buildUrl(null, null, ip, port);
      ips.add(ip);
      ports.add(port);
    } catch (URISyntaxException e) {
      logger.error(e);
      SystemUtils.exit(SystemUtils.ExitStatus.WRONG_ARGUMENT_ERROR, "Invalid URL: " + e.getInput());
    } catch (MalformedURLException e) {
      // this does not validate URL and only detects unknown protocol
      logger.error(e);
      SystemUtils.exit(SystemUtils.ExitStatus.RUNTIME_ERROR, "Unknown protocol:%s", e.getMessage());
    }
  }

  public void addLocalHost(int port) {
    addIpPort(LOCAL_HOST, port);
  }

  public void setSslContext(boolean ssl, String cert) throws Exception {
    if (ssl) {
      sslContext = cert != null
          ? getSSLContext(cert)
          // [GLE-5208] for option '--SSL', gsql will use default SSLContext,
          // but if the certificate itself is self-signed, it will not be trusted by default
          : getDefaultSSLContext();
    }
  }

  public URI getCurrentURI() throws MalformedURLException, URISyntaxException {
    URL url = buildUrl(null, null, ips.get(index), ports.get(index));
    return url.toURI();
  }

  /**
   * Build the endpoint URL to GSQL server.
   * e.g. http://127.0.0.1:8123/gsql (local)
   *      http://127.0.0.1:8123/gsql/endpoint (local)
   *      https://************:14240/gsql (remote)
   * @param endpoint the endpoint, null if no endpoint just test gsql-server url is correct
   * @param param the URL parameters
   * @param ip the ip address
   * @param port the port of gsql-server
   * @return the URL to gsql-server with given inputs
   * @throws URISyntaxException, MalformedURLException if given input can't build a valid URL
   */
  private URL buildUrl(String endpoint, Map<String, List<String>> param,
      String ip, int port) throws URISyntaxException, MalformedURLException {

    String baseEndpoint = "gsql";
    String path = endpoint != null
        ? String.join("/", "", baseEndpoint, endpoint)
        : String.join("/", "", baseEndpoint);

    URIBuilder builder = new URIBuilder()
        .setScheme(sslContext != null ? "https" : "http")
        .setHost(ip)
        .setPort(port)
        .setPath(path);

    if (param != null) {
      param.forEach((k, l) ->
          l.forEach(v -> builder.addParameter(k, v))
      );
    }

    return builder.build().toURL();
  }

  /**
   * Open a http or https connection with given {@code url}
   * @param url the url to open connection with
   * @return the HttpURLConnection or HttpsURLConnection
   * @throws Exception
   */
  private HttpURLConnection openConnection(URL url) throws Exception {
    if (sslContext != null) {
      // open HttpsURLConnection when CA Certificate is given
      HttpsURLConnection connSecured = (HttpsURLConnection) url.openConnection();
      connSecured.setSSLSocketFactory(sslContext.getSocketFactory());

      // GF-1633 create all-trusting host name verifier, and install it
      HostnameVerifier allHostsValid = new HostnameVerifier() {
        public boolean verify(String hostname, SSLSession session) {
            return true;
        }
      };
      connSecured.setHostnameVerifier(allHostsValid);
      return connSecured;
    } else {
      return (HttpURLConnection) url.openConnection();
    }
  }

  /**
   * Build the http connection by given inputs with {@code ips} and {@code ports}.
   * Try the {@code ips} and {@code ports} one by one until success.
   * Each ip and port will retry up to {@code maxRetry} times.
   * @param endpoint the gsql-server endpoint
   * @param param the URL parameters
   * @param method GET/POST/PUT
   * @param data the payload, null if no payload
   * @param headers the map of headers, null if no headers
   * @return the HttpURLConnection
   * @throws Exception
   */
  public HttpURLConnection connect(String endpoint, Map<String, List<String>> param,
      String method, byte[] data, Map<String, String> headers) throws Exception {

    logger.info("RetryableHttpConnection: endpoint: %s, data.len: %d, method: %s",
        endpoint,
        data == null ? -1 : data.length,
        method);


    // retry up to maxRetry times
    for (int j = 0; j < maxRetry + 1; ++j) {
      // try all ips
      for (int i = 0; i < ips.size() && !ips.isEmpty(); ++i, ++index) {
        if (index >= ips.size()) {
          index = 0;
        }

        try {
          logger.info("connecting to %s:%s ...",
              ips.get(index),
              ports.get(index));
          // 1. build URL
          URL url = buildUrl(endpoint, param, ips.get(index), ports.get(index));
          if (j > 0) {
            TimeUnit.SECONDS.sleep(RETRY_INTERVAL);
          }
          // 2. open connection
          HttpURLConnection conn = openConnection(url);
          conn.setConnectTimeout(DEFAULT_CONNECTION_TIMEOUT);

          // set headers, e.g. user auth token
          if (headers != null) {
            headers.forEach((k, v) -> conn.setRequestProperty(k, v));
          }
          conn.setRequestMethod(method);
          conn.setUseCaches(false);
          conn.setDoInput(true);
          conn.setDoOutput(true);

          try {
            //2. write data to the connection if needed
            if (data != null) {
              try (OutputStream out = conn.getOutputStream()) {
                out.write(data);
              }
            }

            //3. get response
            // return when 2xx, 4xx or 5xx
            int code = conn.getResponseCode();
            // retry when:
            // * HTTP 3xx (Redirects)
            // * HTTP 502 (Bad Gateway)
            if ((code / 100) == 3 || code == 502) {
              logger.info("code: %s, retrying...", code);
              continue;
            }
            if ((code / 100) == 2 || (code / 100) == 4 || (code / 100) == 5) {
              logger.info("succeed to connect to %s:%s!",
                  ips.get(index),
                  ports.get(index));
              return conn;
            }
            logger.error("code: %d, msg: %s", code, conn.getResponseMessage());
            // get something else, try next ip/port
            break;
          } catch (IOException e) {
            // retry to connect this ip/port
            logger.error("Error when connecting to %s:%s:\n%s",
                ips.get(index),
                ports.get(index),
                e);
            if (e instanceof SSLException) {
              String errMsg = String.format("%s when connecting to %s:%s. "
                      + "Please verify the ip and port, turn on 'Nginx.SSL.Enable', "
                      + "and configure 'Nginx.SSL.Key' and 'Nginx.SSL.Cert' correctly.",
                  e.getClass().getSimpleName(),
                  ips.get(index),
                  ports.get(index));
              SystemUtils.exit(SystemUtils.ExitStatus.RUNTIME_ERROR, errMsg);
            }
          }

        } catch (URISyntaxException | IOException | InterruptedException e) {
          // got non-retryable exception for an ip/port, log and try next
          logger.error(e);
        }
      }
    }
    logger.error("Unable to connect to all IPs");
    throw new ConnectException();
  }

  /**
   * load the CA and use it in the https connection
   * @param filename the CA filename
   * @return the SSL context
   */
  private SSLContext getSSLContext(String filename) throws Exception {
    try {
      // Load CAs from an InputStream
      // (could be from a resource or ByteArrayInputStream or ...)
      // X.509 is a standard that defines the format of public key certificates, used in TLS/SSL.
      CertificateFactory cf = CertificateFactory.getInstance("X.509");
      InputStream caInput = new BufferedInputStream(new FileInputStream(filename));
      Certificate ca = cf.generateCertificate(caInput);

      // Create a KeyStore containing our trusted CAs
      String keyStoreType = KeyStore.getDefaultType();
      KeyStore keyStore = KeyStore.getInstance(keyStoreType);
      keyStore.load(null, null);
      keyStore.setCertificateEntry("ca", ca);

      // Create a TrustManager that trusts the CAs in our KeyStore
      String tmfAlgorithm = TrustManagerFactory.getDefaultAlgorithm();
      TrustManagerFactory tmf = TrustManagerFactory.getInstance(tmfAlgorithm);
      tmf.init(keyStore);

      // Create an SSLContext that uses our TrustManager
      SSLContext context = SSLContext.getInstance("TLS");
      context.init(null, tmf.getTrustManagers(), null);
      return context;
    } catch (Exception e) {
      throw new Exception("Failed to load the CA file: " + e.getMessage(), e);
    }
  }

  /**
   * For self-assigned certificate it is not trusted by default,
   * we use this method to add the server's certificate in the trusted keystore
   * and use in the https connection
   * @return
   * @throws Exception
   */
  private SSLContext getDefaultSSLContext() throws Exception {
    KeyStore keyStore = KeyStore.getInstance(KeyStore.getDefaultType());
    keyStore.load(null, null);

    // available SSLContext protocol: TLS, TLSv1, TLSv1.1, TLSv1.2
    // create context with "TLSv1.2" supports versions up to TLS 1.2
    SSLContext context = SSLContext.getInstance("TLSv1.2");
    TrustManagerFactory tmf = TrustManagerFactory.getInstance(
        TrustManagerFactory.getDefaultAlgorithm());
    tmf.init(keyStore);
    X509TrustManager defaultTrustManager = (X509TrustManager) tmf.getTrustManagers()[0];
    // for self-signed certificate
    SelfTrustManager tm = new SelfTrustManager(defaultTrustManager);
    context.init(null, new TrustManager[]{tm}, null);
    try {
      SSLSocketFactory factory = context.getSocketFactory();
      SSLSocket socket = (SSLSocket) factory.createSocket(ips.get(0), ports.get(0));
      socket.setSoTimeout(10000);
      socket.startHandshake();
      socket.close();
      logger.info("No errors, certificate is already trusted");
      return context;
    } catch (SSLException e) {
      // the certificate is self-signed
      // handle in the next section
      logger.info(e.getMessage());
    } catch (Exception e) {
      logger.info(e.getMessage());
    }
    X509Certificate[] chain = tm.chain;
    if (chain == null) {
      String errMsg = "Could not obtain server certificate chain";
      SystemUtils.exit(SystemUtils.ExitStatus.RUNTIME_ERROR, errMsg);
    }
    X509Certificate cert = chain[0];
    String alias = ips.get(0) + "-0";
    keyStore.setCertificateEntry(alias, cert);
    tmf.init(keyStore);
    context.init(null, tmf.getTrustManagers(), null);
    return context;
  }

  /**
   * Self trust manager to retrieve server certificate
   */
  private static class SelfTrustManager implements X509TrustManager {

    private final X509TrustManager tm;
    private X509Certificate[] chain;

    SelfTrustManager(X509TrustManager tm) {
      this.tm = tm;
    }

    @Override
    public X509Certificate[] getAcceptedIssuers() {
      // This change has been done due to the following resolution advised for Java 1.7+
      // http://infposs.blogspot.kr/2013/06/installcert-and-java-7.html
      return new X509Certificate[0];
    }

    @Override
    public void checkClientTrusted(X509Certificate[] chain, String authType)
        throws CertificateException {
      throw new UnsupportedOperationException();
    }

    @Override
    public void checkServerTrusted(X509Certificate[] chain, String authType)
        throws CertificateException {
      this.chain = chain;
      tm.checkServerTrusted(chain, authType);
    }
  }
}
