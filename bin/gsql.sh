#!/usr/bin/env bash

# This script is under AppRoot/cmd
CMD_DIR=$(dirname "$0")

# gsql_client.jar is under AppRoot/dev/gdk/gsql/lib
GSQL_LIB_DIR="$CMD_DIR/../dev/gdk/gsql/lib"
CLIENT_JAR="$GSQL_LIB_DIR/gsql_client.jar"
CFG_FILE="$GSQL_LIB_DIR/.debug_client"
# by disabling this option, the creation of the hsperfdata_userid directories 
# is suppressed, effectively resolving the warnings related to reusing the PID 
# files under hsperfdata_userid
JAVA_OPTIONS="-XX:-UsePerfData"
declare -a CFG_OPT
if [ -r "$CFG_FILE" ]; then
  read -ra CFG_OPT < <(tail -n1 "$CFG_FILE")
fi

# locate java under AppRoot/.syspre
JAVA_SYSPRE="$CMD_DIR/../.syspre/usr/lib/jvm/java-openjdk/bin"
# redirect stdout/err to a tmp file under $GSQL_LIB_DIR
TMP_OUT="$GSQL_LIB_DIR"/$(date +%s%3N).out
# try java -version to validate
if ! "$JAVA_SYSPRE/java" -version &>"$TMP_OUT" ; then
  # print the tmp file if anything went wrong
  cat "$TMP_OUT"
  # exit non-zero
  exit 1
fi
# remove the tmp file
rm -f "$TMP_OUT"
# run gsql_client with java under AppRoot/.syspre
exec "$JAVA_SYSPRE/java" $JAVA_OPTIONS -jar "${CFG_OPT[@]}" "$CLIENT_JAR" "$@"
