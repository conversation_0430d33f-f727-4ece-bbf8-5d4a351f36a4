{
  // ANTRL4 grammar syntax support
  "antlr4.generation": {
    "mode": "internal"
  },
  // files and folders to exclude from workspace
  "files.exclude": {
    "**/.classpath": true,
    "**/.project": true,
    "**/.settings": true,
    "**/.factorypath": true
  },
  // Checkstyle for Java config file
  "java.checkstyle.configuration": "${workspaceFolder}/config/checkstyle/checkstyle.xml",
  // Checkstyle for Java version (match the version in :devtools:codestylechecker)
  "java.checkstyle.version": "10.3.1",
  // sorting order of import statements
  "java.completion.importOrder": [
    "com.tigergraph",
    "tigergraph",
    "gle",
    "java",
    "javax",
    "com",
    "org"
  ],
  // update build configuration automatically once build files are modified
  "java.configuration.updateBuildConfiguration": "disabled",
  // disable default Java formatter
  "java.format.enabled": false,
  // thresholds to organize Java imports with *
  "java.sources.organizeImports.starThreshold": 5,
  "java.sources.organizeImports.staticStarThreshold": 5,
  // Live Server port for JaCoCo Report (default port 5500 is already reserved by RESTPP)
  "liveServer.settings.port": 8000,
  // Live Server root for JaCoCo Report
  "liveServer.settings.root": "/gsql-server/build/reports/jacoco/coverage/"
}
