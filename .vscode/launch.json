{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "type": "java",
      "name": "Debug :gsql-server",
      "request": "attach",
      "hostName": "localhost",
      "port": 47757,
      "projectName": "gsql-server"
    },
    {
      "type": "java",
      "name": "Debug :gsql-server:test",
      "request": "attach",
      "hostName": "localhost",
      "port": 5005,
      "projectName": "gsql-server"
    },
    {
      "type": "java",
      "name": "Debug :gsql-client",
      "request": "attach",
      "hostName": "localhost",
      "port": 47752,
      "projectName": "gsql-client"
    },
    {
      "type": "java",
      "name": "Debug :gsql-test",
      "request": "attach",
      "hostName": "localhost",
      "port": 47758,
      "projectName": "gsql-test"
    },
    {
      // https://github.com/mike-lischke/vscode-antlr4/blob/master/doc/grammar-debugging.md#setup
      "name": "Debug ANTLR4 grammar",
      "type": "antlr-debug",
      "request": "launch",
      "input": "debug/debug.gsql",
      "grammar": "gsql-server/src/main/antlr/GSQLParser.g4",
      "startRule": "job",
      "printParseTree": true,
      "visualParseTree": true
    }
  ]
}
