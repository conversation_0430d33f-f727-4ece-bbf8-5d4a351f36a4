{
  // See https://go.microsoft.com/fwlink/?LinkId=733558
  // for the documentation about the tasks.json format
  "version": "2.0.0",
  "tasks": [
    {
      "label": "Build (Dev)",
      "type": "shell",
      "command": "./gradlew -Pobfus=false :assemble :updatePkgJars :gsql-server:enableDebug",
      "group": {
        "kind": "build",
        "isDefault": true
      }
    },
    {
      "label": "Build (Release)",
      "type": "shell",
      "command": "./gradlew :assemble :updatePkgJars :gsql-server:enableDebug",
      "group": "build"
    }
  ]
}
